version: '3.8'

services:
  spark-rag-mcp:
    build:
      context: .
      dockerfile: Dockerfile
    image: mcp-crawl4ai-rag-spark-rag-mcp:latest
    container_name: spark-rag-mcp
    ports:
      - "8051:8051"
    environment:
      # Core MCP Server Configuration
      - MCP_SERVER_NAME=spark-rag-mcp
      - MCP_SERVER_VERSION=1.0.0
      - HOST=0.0.0.0
      - PORT=8051
      
      # BGE Embedding Service Configuration
      - USE_DIRECT_EMBEDDING=false
      - BGE_SERVICE_URL=http://bge-embedding-server:8080
      
      # Supabase Configuration (to be set via .env file)
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # OpenRouter LLM Configuration (Multi-Model Setup)
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-}
      - OPENROUTER_BASE_URL=${OPENROUTER_BASE_URL:-https://openrouter.ai/api/v1}
      - MODEL_QUALITY_LEVEL=${MODEL_QUALITY_LEVEL:-balanced}
      - CODE_SUMMARIZATION_MODEL=${CODE_SUMMARIZATION_MODEL:-google/gemini-flash-1.5}
      - SOURCE_SUMMARIZATION_MODEL=${SOURCE_SUMMARIZATION_MODEL:-mistralai/mistral-7b-instruct:free}
      - CONTEXTUAL_EMBEDDINGS_MODEL=${CONTEXTUAL_EMBEDDINGS_MODEL:-google/gemini-flash-1.5}
      
      # GitHub Integration
      - GITHUB_TOKEN=${GITHUB_TOKEN:-}
      - GITHUB_API_URL=${GITHUB_API_URL:-https://api.github.com}
      
      # Feature Toggles & RAG Strategies
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-false}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-false}
      - USE_KNOWLEDGE_GRAPH=${USE_KNOWLEDGE_GRAPH:-false}
      
      # Neo4j Configuration (optional)
      - NEO4J_URI=${NEO4J_URI:-}
      - NEO4J_USER=${NEO4J_USER:-neo4j}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-}
      
      # Crawling Configuration
      - DEFAULT_CHUNK_SIZE=${DEFAULT_CHUNK_SIZE:-5000}
      - MAX_CONCURRENT_CRAWLS=${MAX_CONCURRENT_CRAWLS:-10}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-30}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      
      # Transport Configuration for Docker deployment
      - TRANSPORT=sse
      
    volumes:
      - spark-rag-logs:/app/logs
      - ./.env:/app/.env:ro
    depends_on:
      - bge-embedding-server
    networks:
      - spark-rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/sse/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  bge-embedding-server:
    build:
      context: ./services/bge-embedding-server
      dockerfile: Dockerfile
    image: mcp-crawl4ai-rag-bge-embedding-server:latest
    container_name: bge-embedding-server
    ports:
      - "8080:8080"
    environment:
      - MODEL_NAME=BAAI/bge-large-en-v1.5
      - HOST=0.0.0.0
      - PORT=8080
      - DEVICE=cuda
      - BATCH_SIZE=32
      - MAX_LENGTH=512
    volumes:
      - bge-model-cache:/root/.cache/huggingface
    networks:
      - spark-rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

networks:
  spark-rag-network:
    driver: bridge
    name: spark-rag-network

volumes:
  bge-model-cache:
    name: bge-model-cache
    driver: local
  spark-rag-logs:
    name: spark-rag-logs
    driver: local