# Environment files
.env
.env.*
!.env.example

# Virtual environments
.venv/
venv/
env/
ENV/

# Python cache and build
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs and temporary files
logs/
*.log
.tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
repos/
.claude/
test_script_hallucination*
.roomodes
INITIAL.md