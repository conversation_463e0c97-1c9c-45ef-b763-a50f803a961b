# MCP Crawl4AI RAG Server Configuration
# This file provides default configuration values for the server

# Crawler settings
crawler:
  max_concurrent: 10          # Maximum concurrent crawl operations
  max_depth: 3               # Maximum crawl depth for recursive crawling
  timeout: 30000             # General timeout in milliseconds
  page_timeout: 30000        # Page-specific timeout
  headless: true             # Run browser in headless mode
  verbose: false             # Verbose logging
  browser_type: "chromium"   # Browser type: chromium, firefox, webkit
  chunk_size: 5000           # Content chunk size for storage
  retry_max_attempts: 3      # Maximum retry attempts
  retry_base_delay: 2.0      # Base retry delay in seconds
  retry_max_delay: 60.0      # Maximum retry delay

# Storage settings
storage:
  # These should be set via environment variables for security
  # supabase_url: ""
  # supabase_service_key: ""
  use_direct_embedding: true      # Use direct GPU embedding vs HTTP service
  bge_service_url: "http://localhost:8080"
  embedding_model: "BAAI/bge-large-en-v1.5"
  use_hybrid_search: true         # Enable hybrid semantic + keyword search
  use_agentic_rag: false         # Enable LLM-enhanced RAG
  use_reranking: false           # Enable result reranking
  # openrouter_api_key: ""       # Set via environment variable
  openrouter_model: "anthropic/claude-3.5-sonnet"

# Monitoring settings
monitoring:
  enable_monitoring: true
  metrics_port: 9090
  health_check_interval: 60      # Health check interval in seconds
  alert_webhook_url: ""          # Optional webhook for alerts
  log_level: "INFO"              # Log level: DEBUG, INFO, WARNING, ERROR
  log_file: "logs/crawl4ai_mcp.log"
  
  # Alert thresholds
  error_rate_threshold: 10.0     # Error rate percentage
  consecutive_errors_threshold: 10
  avg_crawl_time_threshold: 60.0 # Average crawl time in seconds
  memory_usage_threshold: **********  # 1GB in bytes
  queue_depth_threshold: 1000

# Worker settings
worker:
  worker_count: 2                # Number of background workers
  worker_id_prefix: "worker"     # Worker ID prefix
  queue_name: "crawl_jobs_queue" # PGMQ queue name
  visibility_timeout: 300        # Job visibility timeout in seconds
  max_retries: 3                # Maximum job retries
  batch_size: 10                # Batch size for job processing

# Feature flags
features:
  enable_knowledge_graph: false  # Enable Neo4j knowledge graph
  enable_code_extraction: true   # Enable code block extraction
  enable_sitemap_detection: true # Auto-detect and crawl sitemaps
  enable_recursive_crawling: true # Enable recursive crawling
  enable_job_system: true        # Enable async job system
  enable_circuit_breakers: true  # Enable circuit breaker pattern
  enable_adaptive_throttling: true # Enable adaptive rate limiting
  enable_graceful_degradation: true # Enable graceful degradation

# Runtime settings
environment: "development"       # Environment: development, production
debug: false                    # Debug mode

# Degradation levels for graceful degradation
degradation:
  levels:
    full:
      max_concurrent: 10
      max_depth: 3
      features: ["sitemap", "recursive", "code_extraction", "knowledge_graph"]
    reduced:
      max_concurrent: 5
      max_depth: 2
      features: ["sitemap", "recursive", "code_extraction"]
    minimal:
      max_concurrent: 2
      max_depth: 1
      features: ["single_page"]
    emergency:
      max_concurrent: 1
      max_depth: 0
      features: ["single_page"]