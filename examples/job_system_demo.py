"""
Job System Demo - Example usage of the new job management system

This script demonstrates how to use the job-based crawling system to eliminate
MCP timeout issues while providing real-time progress monitoring.
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# Add src directory to path
src_path = Path(__file__).resolve().parent.parent / 'src'
sys.path.insert(0, str(src_path))

from job_manager import get_job_manager, JobType, JobStatus

async def demo_single_page_crawl():
    """Demonstrate single page crawling with job system."""
    print("=== Single Page Crawl Demo ===")
    
    job_manager = get_job_manager()
    
    # Start a single page crawl job
    print("Starting single page crawl job...")
    job_id = await job_manager.create_job(
        job_type=JobType.SINGLE_PAGE,
        parameters={
            "url": "https://httpbin.org/html",
            "chunk_size": 1000
        },
        priority=7
    )
    
    print(f"Job created: {job_id}")
    
    # Monitor progress
    print("\nMonitoring progress...")
    start_time = time.time()
    
    while True:
        status = await job_manager.get_job_status(job_id)
        
        elapsed = time.time() - start_time
        print(f"[{elapsed:.1f}s] Status: {status.status.value}, Progress: {status.progress_percent}%")
        
        if status.current_operation:
            print(f"       Operation: {status.current_operation}")
        
        if status.status == JobStatus.COMPLETED:
            print("✅ Job completed successfully!")
            
            # Get detailed results
            results = await job_manager.get_job_results(job_id)
            print(f"   Pages crawled: {results['summary']['pages_crawled']}")
            print(f"   Chunks created: {results['summary']['chunks_created']}")
            print(f"   Processing time: {results['summary']['processing_time_minutes']:.1f} minutes")
            break
            
        elif status.status == JobStatus.FAILED:
            print(f"❌ Job failed: {status.error_message}")
            break
            
        elif status.status == JobStatus.CANCELLED:
            print("⚠️ Job was cancelled")
            break
        
        await asyncio.sleep(2)
    
    return job_id

async def demo_smart_crawl():
    """Demonstrate smart crawling with progress monitoring."""
    print("\n=== Smart Crawl Demo ===")
    
    job_manager = get_job_manager()
    
    # Start a smart crawl job
    print("Starting smart crawl job...")
    job_id = await job_manager.create_job(
        job_type=JobType.SMART_CRAWL,
        parameters={
            "url": "https://httpbin.org",
            "max_depth": 2,
            "max_concurrent": 3,
            "chunk_size": 1500
        },
        priority=6
    )
    
    print(f"Job created: {job_id}")
    
    # Monitor with detailed progress tracking
    print("\nDetailed progress monitoring...")
    last_progress = -1
    
    while True:
        status = await job_manager.get_job_status(job_id)
        
        # Only print updates when progress changes
        if status.progress_percent != last_progress:
            print(f"Progress: {status.progress_percent}% - {status.current_operation}")
            print(f"         Pages: {status.pages_crawled}, Chunks: {status.chunks_created}")
            last_progress = status.progress_percent
        
        if status.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            break
        
        await asyncio.sleep(3)
    
    # Show final results
    if status.status == JobStatus.COMPLETED:
        print("✅ Smart crawl completed!")
        results = await job_manager.get_job_results(job_id)
        
        print("\nFinal Results:")
        print(f"  • Status: {results['status']}")
        print(f"  • Pages crawled: {results['summary']['pages_crawled']}")
        print(f"  • Chunks created: {results['summary']['chunks_created']}")
        print(f"  • Success rate: {results['summary']['success_rate']}")
        print(f"  • Processing time: {results['summary']['processing_time_minutes']:.1f} minutes")
        
        # Show progress history
        if results['progress_history']:
            print("\nProgress History:")
            for entry in results['progress_history'][-5:]:  # Last 5 entries
                timestamp = entry['timestamp'][:19]  # Remove timezone for readability
                print(f"  • {timestamp}: {entry['operation_name']} ({entry['progress_percent']}%)")
    
    return job_id

async def demo_batch_processing():
    """Demonstrate batch processing multiple jobs."""
    print("\n=== Batch Processing Demo ===")
    
    job_manager = get_job_manager()
    
    # URLs to process
    urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://httpbin.org/xml"
    ]
    
    # Start multiple jobs
    job_ids = []
    print("Starting batch jobs...")
    
    for i, url in enumerate(urls):
        job_id = await job_manager.create_job(
            job_type=JobType.SINGLE_PAGE,
            parameters={
                "url": url,
                "chunk_size": 800
            },
            priority=5 + i  # Different priorities
        )
        job_ids.append(job_id)
        print(f"  Started job {i+1}: {job_id[:8]}... for {url}")
    
    # Monitor all jobs
    print(f"\nMonitoring {len(job_ids)} jobs...")
    completed_jobs = set()
    
    while len(completed_jobs) < len(job_ids):
        print(f"\n--- Status Update ({len(completed_jobs)}/{len(job_ids)} completed) ---")
        
        for i, job_id in enumerate(job_ids):
            if job_id in completed_jobs:
                continue
                
            status = await job_manager.get_job_status(job_id)
            short_id = job_id[:8]
            
            print(f"Job {i+1} ({short_id}): {status.status.value} - {status.progress_percent}%")
            
            if status.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                completed_jobs.add(job_id)
        
        if len(completed_jobs) < len(job_ids):
            await asyncio.sleep(5)
    
    # Summary
    print("\n=== Batch Processing Summary ===")
    total_pages = 0
    total_chunks = 0
    
    for i, job_id in enumerate(job_ids):
        results = await job_manager.get_job_results(job_id)
        short_id = job_id[:8]
        
        if results['status'] == 'completed':
            pages = results['summary']['pages_crawled']
            chunks = results['summary']['chunks_created']
            total_pages += pages
            total_chunks += chunks
            print(f"  Job {i+1} ({short_id}): ✅ {pages} pages, {chunks} chunks")
        else:
            print(f"  Job {i+1} ({short_id}): ❌ {results.get('error', 'Failed')}")
    
    print(f"\nTotal: {total_pages} pages, {total_chunks} chunks processed")

async def demo_job_management():
    """Demonstrate job management features."""
    print("\n=== Job Management Demo ===")
    
    job_manager = get_job_manager()
    
    # List current jobs
    print("Current job queue status:")
    job_list = await job_manager.list_jobs(limit=10)
    
    stats = job_list['queue_stats']
    print(f"  • Total jobs: {stats['total_jobs']}")
    print(f"  • Queued: {stats['queued_jobs']}")
    print(f"  • Running: {stats['running_jobs']}")
    print(f"  • Completed: {stats['completed_jobs']}")
    print(f"  • Failed: {stats['failed_jobs']}")
    print(f"  • Average processing time: {stats['avg_processing_time_minutes']:.1f} minutes")
    
    # Show recent jobs
    if job_list['jobs']:
        print(f"\nRecent jobs (showing {len(job_list['jobs'])}):")
        for job in job_list['jobs'][:5]:  # Show first 5
            short_id = job['job_id'][:8]
            print(f"  • {short_id}: {job['job_type']} - {job['status']} ({job['progress_percent']}%)")
    
    # Demonstrate job cancellation
    print("\nTesting job cancellation...")
    
    # Start a job to cancel
    cancel_job_id = await job_manager.create_job(
        job_type=JobType.SINGLE_PAGE,
        parameters={"url": "https://httpbin.org/delay/10"},  # Slow URL
        priority=1  # Low priority
    )
    
    print(f"Started job to cancel: {cancel_job_id[:8]}")
    
    # Wait a moment then cancel
    await asyncio.sleep(2)
    
    cancelled = await job_manager.cancel_job(cancel_job_id)
    if cancelled:
        print("✅ Job cancelled successfully")
        
        # Verify cancellation
        status = await job_manager.get_job_status(cancel_job_id)
        print(f"   Final status: {status.status.value}")
    else:
        print("❌ Failed to cancel job (may have completed)")

async def main():
    """Run all demos."""
    print("🚀 Job System Demo Starting...")
    print("=" * 50)
    
    try:
        # Run individual demos
        await demo_single_page_crawl()
        await demo_smart_crawl()
        await demo_batch_processing()
        await demo_job_management()
        
        print("\n" + "=" * 50)
        print("🎉 All demos completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Check if job worker is running
    print("Note: Make sure the job worker is running for this demo!")
    print("Start with: ./scripts/start_job_system.sh start")
    print()
    
    # Run the demo
    asyncio.run(main())