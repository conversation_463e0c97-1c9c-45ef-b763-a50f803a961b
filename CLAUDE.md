# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Crawl4AI RAG MCP Server** - a Model Context Protocol server that provides AI agents with advanced web crawling and RAG (Retrieval Augmented Generation) capabilities. It integrates Crawl4AI for crawling, Supabase for vector storage, and optionally Neo4j for knowledge graphs with AI hallucination detection.

**Key Purpose**: Enable AI agents to crawl documentation, store it intelligently, perform semantic search, and validate AI-generated code against real repositories.

## Development Principles

- Never over engineer as this is internal tool for two-person team
- Always follow the KISS (Keep It Simple, Stupid) principle
- Always ask for approval before using enterprise-grade architecture, design, or refactoring
- Be open to discussion and justification for any complex architectural changes

## Project Objectives

- Your top objective is to develop a simple yet highly accurate RAG MCP server
- We will not be using high volume crawling but need world-class accuracy
- Prioritize precision and quality of retrieval over crawling quantity

## Core Development Commands

### Environment Setup
```bash
# Install dependencies (using uv for faster installs)
pip install uv
uv venv && source .venv/bin/activate  # Windows: .venv\Scripts\activate
uv pip install -e .

# Setup Crawl4AI (one-time setup)
crawl4ai-setup
```

### Service Management
```bash
# Start services (comprehensive script)
scripts/start_services.sh both        # Start with live logs
scripts/start_services.sh both-bg     # Start in background
scripts/start_services.sh mcp         # Start only MCP server
scripts/start_services.sh bge         # Start only BGE service

# Service monitoring
scripts/start_services.sh logs        # View live logs
scripts/start_services.sh status      # Check service status
scripts/start_services.sh stop        # Stop all services
scripts/start_services.sh restart     # Restart MCP server
```

### Running Tests
```bash
# Direct embedding tests (performance comparison)
python tests/test_direct_embedding.py

# BGE service integration tests
python tests/test_bge_integration.py

# OpenRouter LLM integration tests  
python tests/test_openrouter_integration.py

# Individual test files for specific features
python test_bge_init.py
python test_integration.py
python test_server_startup.py
```

### Research CLI
```bash
# CLI entry points (installed via pyproject.toml)
crawl4ai-research --help
research-cli --help

# CLI with options
crawl4ai-research --debug --timeout 60
```

### Development Mode
```bash
# Start MCP server directly for development
uv run src/crawl4ai_mcp.py

# Connect to Claude Desktop
claude mcp add-json crawl4ai-rag '{"type":"http","url":"http://localhost:8051/sse"}' --scope user
```

## High-Level Architecture

### Core Components

**Main MCP Server** (`src/crawl4ai_mcp.py`)
- FastMCP-based server with 8 MCP tools
- Async web crawler with retry logic and error recovery
- Intelligent crawling (detects sitemaps, text files, regular pages)
- Global resource management with lifespan context
- Knowledge graph integration for AI hallucination detection

**Utility Module** (`src/utils.py`)
- Supabase client management and vector operations
- OpenRouter LLM integration for agentic RAG
- Direct GPU embedding using BGE models
- Code extraction and summarization
- Hybrid search combining semantic and keyword search

**BGE Embedding Service** (`services/bge-embedding-server/`)
- Self-contained Docker service for BGE embeddings
- GPU-accelerated embedding generation
- HTTP API with health monitoring
- Model caching for faster startup

**Knowledge Graph Tools** (`knowledge_graphs/`)
- Neo4j integration for repository analysis
- AI script analyzer for hallucination detection
- Repository parser for code structure extraction
- Query interface for knowledge graph exploration

### Configuration Architecture

**Environment-Based Configuration** (`.env`)
```bash
# Core services
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key
OPENROUTER_API_KEY=your_openrouter_key

# Embedding strategy
USE_DIRECT_EMBEDDING=true     # Direct GPU vs HTTP service
BGE_SERVICE_URL=http://localhost:8080

# Feature toggles
USE_HYBRID_SEARCH=true        # Semantic + keyword search
USE_AGENTIC_RAG=false         # LLM-enhanced responses
USE_RERANKING=false           # Result reranking
USE_KNOWLEDGE_GRAPH=false     # Neo4j integration
```

**Two Embedding Modes**:
1. **Direct GPU Embedding**: Uses sentence-transformers directly (faster startup)
2. **BGE HTTP Service**: Dedicated Docker service (better resource management)

### MCP Tool Architecture

**8 Core Tools Available to AI Agents**:
1. `health_check` - Server status verification
2. `crawl_single_page` - Single page crawling
3. `smart_crawl_url` - Intelligent multi-page crawling
4. `get_available_sources` - List crawled sources
5. `perform_rag_query` - Semantic search with RAG
6. `search_code_examples` - Code-specific search
7. `check_ai_script_hallucinations` - AI validation against knowledge graph
8. `query_knowledge_graph` - Direct graph querying

### Error Handling & Recovery

**Robust Crawling Strategy**:
- Exponential backoff retry logic (max 3 retries)
- Timeout escalation (30s → 60s → 120s)
- Error recovery manager with retry queue
- Graceful degradation for different failure modes

**Resource Management**:
- Global crawler instance with proper lifecycle
- Automatic cleanup in lifespan context
- Memory-adaptive dispatching for large crawls
- Concurrent crawling with configurable limits

## Key Files and Directories

### Source Code (`/src`)
- `crawl4ai_mcp.py` - Main MCP server (primary development file)
- `utils.py` - Core utilities and integrations  
- `cli/` - Research CLI implementation
  - `research.py` - Main CLI interface
  - `mcp_connection.py` - MCP client integration
  - `config.py` - Configuration management

### Testing (`/tests`)
- Focus on integration testing between components
- Performance benchmarking for embedding strategies
- OpenRouter LLM integration validation
- BGE service connectivity and health checks

### Documentation (`/docs`)
- `PROJECT_STRUCTURE.md` - Complete project layout
- `BGE_SERVICE.md` - BGE embedding service guide
- `OPENROUTER_INTEGRATION.md` - LLM integration guide
- Implementation summaries for major features

### Scripts (`/scripts`)
- `start_services.sh` - Comprehensive service management
- `setup_integrated_cli.py` - CLI installation helper

## Development Workflow

### Making Changes
1. **Code Changes**: Primary work in `src/crawl4ai_mcp.py` and `src/utils.py`
2. **Testing**: Run relevant tests from `/tests` directory
3. **Service Testing**: Use `scripts/start_services.sh` for integration testing
4. **Debugging**: Monitor logs via `scripts/start_services.sh logs`

### Adding New Features
1. **MCP Tools**: Add to `crawl4ai_mcp.py` with proper error handling
2. **Utilities**: Extend `utils.py` for reusable functionality
3. **Configuration**: Add environment variables with sensible defaults
4. **Testing**: Create corresponding test in `/tests`
5. **Documentation**: Update relevant docs in `/docs`

### Performance Considerations
- **Embedding Strategy**: Choose between direct GPU vs HTTP service based on resources
- **Crawling Concurrency**: Configure via `max_concurrent` in smart crawling
- **Vector Storage**: Supabase handles embedding storage and similarity search
- **Caching**: BGE service caches models locally for faster startup

## Common Tasks

### Debugging Connection Issues
```bash
# Check service status
scripts/start_services.sh status

# View live logs with color coding
scripts/start_services.sh logs

# Test specific components
python tests/test_bge_integration.py
curl http://localhost:8051/health
```

### Database Operations
- Database schema: `crawled_pages.sql` (run in Supabase SQL editor)
- Vector operations handled automatically via `utils.py`
- No manual database interaction needed for normal operation

### Adding New Crawling Capabilities
- Extend `smart_crawl_url` function in `crawl4ai_mcp.py`
- Add new URL detection logic for different content types
- Implement corresponding retry and error handling
- Test with various website types

### Knowledge Graph Integration
- Enable via `USE_KNOWLEDGE_GRAPH=true` in `.env`
- Requires Neo4j instance and proper configuration
- Used for AI hallucination detection in generated code
- Repository parsing extracts code structure into graph format

## Important Notes

- **KISS Principle**: Keep solutions simple - this is an internal tool for a two-person team
- **Performance Focus**: Optimize for accuracy over speed in RAG operations
- **Resource Management**: Use direct embedding for development, HTTP service for production
- **Error Resilience**: All web operations include comprehensive retry logic
- **Configuration Flexibility**: Environment variables control all major features