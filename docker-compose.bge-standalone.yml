version: '3.8'

services:
  bge-embedding:
    image: bge-embedding-server:latest
    build:
      context: .
      dockerfile_inline: |
        FROM nvidia/cuda:11.8.0-runtime-ubuntu22.04
        
        ENV DEBIAN_FRONTEND=noninteractive
        ENV PYTHONUNBUFFERED=1
        
        RUN apt-get update && apt-get install -y \
            python3 \
            python3-pip \
            python3-dev \
            git \
            curl \
            && rm -rf /var/lib/apt/lists/*
        
        RUN pip3 install --upgrade pip
        
        WORKDIR /app
        
        RUN pip3 install --no-cache-dir \
            FlagEmbedding>=1.2.0 \
            torch>=2.0.0 \
            torchvision>=0.15.0 \
            torchaudio>=2.0.0 \
            flask>=2.3.0 \
            gunicorn>=21.0.0 \
            numpy>=1.24.0
        
        RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
        
        COPY <<EOF /app/app.py
        #!/usr/bin/env python3
        import os
        import time
        import logging
        from typing import List, Dict, Any
        from flask import Flask, request, jsonify
        import torch
        import numpy as np
        
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        app = Flask(__name__)
        model = None
        
        def initialize_model():
            global model
            try:
                os.environ['CUDA_VISIBLE_DEVICES'] = '0'
                from FlagEmbedding import FlagModel
                logger.info("Loading BAAI bge-base-en-v1.5 model...")
                start_time = time.time()
                model = FlagModel(
                    'BAAI/bge-base-en-v1.5', 
                    devices=0,
                    use_fp16=True,
                    query_instruction_for_retrieval="Represent this sentence for searching relevant passages:"
                )
                load_time = time.time() - start_time
                logger.info(f"Model loaded successfully in {load_time:.2f} seconds!")
                if torch.cuda.is_available():
                    gpu_name = torch.cuda.get_device_name(0)
                    gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                    logger.info(f"Using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                else:
                    logger.warning("CUDA not available, falling back to CPU")
                return True
            except Exception as e:
                logger.error(f"Failed to initialize model: {e}")
                return False
        
        @app.route('/health', methods=['GET'])
        def health_check():
            try:
                gpu_available = torch.cuda.is_available()
                gpu_memory_used = 0
                gpu_memory_total = 0
                if gpu_available:
                    gpu_memory_used = torch.cuda.memory_allocated(0) / (1024**2)
                    gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**2)
                return jsonify({
                    'status': 'healthy',
                    'model': 'BAAI/bge-base-en-v1.5',
                    'dimensions': 768,
                    'model_loaded': model is not None,
                    'cuda_available': gpu_available,
                    'gpu_memory_used_mb': gpu_memory_used,
                    'gpu_memory_total_mb': gpu_memory_total,
                    'gpu_utilization_percent': (gpu_memory_used / gpu_memory_total * 100) if gpu_memory_total > 0 else 0
                })
            except Exception as e:
                return jsonify({'status': 'error', 'error': str(e)}), 500
        
        @app.route('/embed', methods=['POST'])
        def embed_text():
            try:
                if model is None:
                    return jsonify({'error': 'Model not initialized'}), 500
                data = request.json
                if not data:
                    return jsonify({'error': 'No JSON data provided'}), 400
                texts = data.get('texts', [])
                if not texts:
                    return jsonify({'error': 'No texts provided'}), 400
                if not isinstance(texts, list):
                    return jsonify({'error': 'texts must be a list'}), 400
                if len(texts) > 100:
                    return jsonify({'error': 'Too many texts (max 100 per request)'}), 400
                start_time = time.time()
                embeddings = model.encode(texts, batch_size=32)
                embeddings_list = embeddings.tolist()
                processing_time = time.time() - start_time
                logger.info(f"Generated {len(embeddings_list)} embeddings in {processing_time:.3f}s")
                return jsonify({
                    'embeddings': embeddings_list,
                    'dimensions': len(embeddings_list[0]) if embeddings_list else 768,
                    'count': len(embeddings_list),
                    'processing_time_seconds': processing_time,
                    'model': 'BAAI/bge-base-en-v1.5'
                })
            except Exception as e:
                logger.error(f"Error generating embeddings: {e}")
                return jsonify({'error': str(e)}), 500
        
        @app.route('/embed/single', methods=['POST'])
        def embed_single_text():
            try:
                if model is None:
                    return jsonify({'error': 'Model not initialized'}), 500
                data = request.json
                if not data:
                    return jsonify({'error': 'No JSON data provided'}), 400
                text = data.get('text', '')
                if not text:
                    return jsonify({'error': 'No text provided'}), 400
                start_time = time.time()
                embedding = model.encode([text])[0]
                processing_time = time.time() - start_time
                return jsonify({
                    'embedding': embedding.tolist(),
                    'dimensions': len(embedding),
                    'processing_time_seconds': processing_time,
                    'model': 'BAAI/bge-base-en-v1.5'
                })
            except Exception as e:
                logger.error(f"Error generating single embedding: {e}")
                return jsonify({'error': str(e)}), 500
        
        @app.route('/metrics', methods=['GET'])
        def get_metrics():
            try:
                metrics = {
                    'model_loaded': model is not None,
                    'cuda_available': torch.cuda.is_available()
                }
                if torch.cuda.is_available():
                    metrics.update({
                        'gpu_memory_allocated_mb': torch.cuda.memory_allocated(0) / (1024**2),
                        'gpu_memory_cached_mb': torch.cuda.memory_reserved(0) / (1024**2),
                        'gpu_memory_total_mb': torch.cuda.get_device_properties(0).total_memory / (1024**2),
                        'gpu_name': torch.cuda.get_device_name(0)
                    })
                return jsonify(metrics)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @app.route('/', methods=['GET'])
        def root():
            return jsonify({
                'service': 'BGE Embedding Server',
                'model': 'BAAI/bge-base-en-v1.5',
                'version': '1.0.0',
                'endpoints': {
                    '/health': 'Health check',
                    '/embed': 'Batch embedding generation',
                    '/embed/single': 'Single text embedding',
                    '/metrics': 'System metrics'
                },
                'dimensions': 768
            })
        
        def main():
            if not initialize_model():
                logger.error("Failed to initialize model, exiting...")
                exit(1)
            host = os.getenv('HOST', '0.0.0.0')
            port = int(os.getenv('PORT', '8080'))
            debug = os.getenv('DEBUG', 'false').lower() == 'true'
            logger.info(f"Starting BGE Embedding Server on {host}:{port}")
            app.run(host=host, port=port, debug=debug, threaded=True)
        
        if __name__ != '__main__':
            if not initialize_model():
                logger.error("Failed to initialize model during module load")
            else:
                logger.info("Model initialized successfully during module load")
        
        if __name__ == '__main__':
            main()
        EOF
        
        RUN mkdir -p /root/.cache/huggingface
        
        EXPOSE 8080
        
        HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
            CMD curl -f http://localhost:8080/health || exit 1
        
        ENV HOST=0.0.0.0
        ENV PORT=8080
        ENV DEBUG=false
        
        CMD ["gunicorn", "--bind", "0.0.0.0:8080", "--workers", "1", "--timeout", "120", "--worker-class", "sync", "app:app"]
    container_name: bge-embedding-server
    ports:
      - "8080:8080"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - HOST=0.0.0.0
      - PORT=8080
      - DEBUG=false
    volumes:
      - bge-models:/root/.cache/huggingface
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - bge-network

networks:
  bge-network:
    driver: bridge
    name: bge-network

volumes:
  bge-models:
    driver: local