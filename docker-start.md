# 🚀 Spark RAG MCP - Docker Quick Start

This guide helps you run the Spark RAG MCP server using Docker containers.

## Prerequisites

- <PERSON>er and Docker Compose installed
- NVIDIA GPU with Docker GPU support (for BGE embedding service)
- Supabase account and project

## Quick Start

### 1. Environment Setup

```bash
# Copy the Docker environment template
cp .env.docker .env

# Edit .env with your actual values
nano .env
```

**Required Configuration:**
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_SERVICE_KEY` - Your Supabase service role key

**Optional Configuration:**
- `GITHUB_TOKEN` - For GitHub repository crawling
- `OPENROUTER_API_KEY` - For enhanced RAG responses
- `NEO4J_*` - For knowledge graph features

### 2. Database Setup

Run the SQL schema in your Supabase SQL editor:

```bash
cat crawled_pages.sql
```

### 3. Start Services

```bash
# Build and start both containers
docker-compose up --build

# Or run in background
docker-compose up --build -d

# View logs
docker-compose logs -f spark-rag-mcp
docker-compose logs -f bge-embedding-server
```

### 4. Health Check

```bash
# Check MCP server
curl http://localhost:8051/health

# Check BGE embedding service
curl http://localhost:8080/health
```

## Service Architecture

### Containers

1. **spark-rag-mcp** (Port 8051)
   - Main MCP server with crawling and RAG capabilities
   - Handles GitHub integration and content processing
   - Stores content in Supabase vector database

2. **bge-embedding-server** (Port 8080)
   - GPU-accelerated BGE embedding service
   - Generates high-quality semantic embeddings
   - Used by the MCP server for vector operations

### Network

- Both containers run on the `spark-rag-network` bridge network
- Internal communication uses container names as hostnames
- External access via published ports (8051, 8080)

## Usage Examples

### Using with Claude Desktop

Add to your Claude Desktop MCP configuration:

```json
{
  "spark-rag-mcp": {
    "type": "sse",
    "url": "http://localhost:8051/sse"
  }
}
```

### Available MCP Tools

- `health_check` - Check server status
- `smart_crawl_url` - Crawl websites (including GitHub repos)
- `get_available_sources` - List crawled content sources
- `perform_rag_query` - Semantic search with RAG
- `search_code_examples` - Find code examples
- `query_knowledge_graph` - Query repository structure (if enabled)

## Troubleshooting

### Container Issues

```bash
# Check container status
docker-compose ps

# View detailed logs
docker-compose logs spark-rag-mcp
docker-compose logs bge-embedding-server

# Restart services
docker-compose restart

# Rebuild containers
docker-compose up --build --force-recreate
```

### Common Problems

1. **GPU Not Available**
   - Ensure NVIDIA Docker runtime is installed
   - Check GPU access: `docker run --gpus all nvidia/cuda:11.8-base nvidia-smi`

2. **Port Already in Use**
   - Change ports in docker-compose.yml
   - Stop conflicting services

3. **Supabase Connection Issues**
   - Verify SUPABASE_URL and SUPABASE_SERVICE_KEY
   - Check database schema is applied

4. **BGE Service Not Ready**
   - First startup downloads models (can take several minutes)
   - Check logs for download progress

## Production Deployment

### Security Considerations

- Use non-root user (already configured)
- Mount .env as read-only volume
- Use Docker secrets for sensitive values
- Enable firewall rules for port access

### Resource Requirements

- **spark-rag-mcp**: 2 CPU cores, 4GB RAM
- **bge-embedding-server**: 1 GPU, 4GB VRAM, 8GB RAM

### Monitoring

```bash
# Resource usage
docker stats

# Health checks
docker-compose ps
curl -f http://localhost:8051/health
curl -f http://localhost:8080/health
```

## Development

### Local Development with Containers

```bash
# Mount source code for live reload
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### Testing

```bash
# Run tests in container
docker-compose exec spark-rag-mcp python -m pytest tests/

# Test specific functionality
docker-compose exec spark-rag-mcp python tests/test_github_integration.py
```

---

🎉 **Your Spark RAG MCP server is now running in containers!**

Access the MCP tools through your AI agent or use the API directly at `http://localhost:8051`.