"""
Utility functions for the Crawl4AI MCP server.
"""
import os
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple
import json
from supabase import create_client, Client
from urllib.parse import urlparse
import requests
import re
import time
import logging
import threading
from dotenv import load_dotenv
from dataclasses import dataclass
from enum import Enum
import numpy as np

# Load environment variables
load_dotenv()

def extract_meaningful_error(error):
    """Extract meaningful error message from Supabase exception, removing verbose HTML content."""
    try:
        error_str = str(error)
        
        # Try to parse as JSON first for structured errors
        if error_str.startswith('{') and error_str.endswith('}'):
            try:
                error_dict = json.loads(error_str)
                if 'message' in error_dict:
                    message = error_dict['message']
                    if message and not message.startswith('<!DOCTYPE html'):
                        return f"{message} (code: {error_dict.get('code', 'unknown')})"
            except:
                pass
        
        # Check if error contains HTML content (like 404 pages)
        if '<!DOCTYPE html' in error_str or '<html' in error_str:
            # Extract just the meaningful part before HTML
            if 'message' in error_str and 'code' in error_str:
                # Try to extract structured error info
                if "'message':" in error_str and "'code':" in error_str:
                    try:
                        # Extract between single quotes for message and code
                        message_match = re.search(r"'message':\s*'([^']*)'", error_str)
                        code_match = re.search(r"'code':\s*(\d+)", error_str)
                        if message_match and code_match:
                            return f"{message_match.group(1)} (HTTP {code_match.group(1)})"
                    except:
                        pass
            return "Database returned HTML error page (likely connectivity issue)"
        
        # For other errors, return first meaningful line
        lines = error_str.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('<') and len(line) < 200:
                return line
        
        # Fallback - return truncated error
        return error_str[:100] + "..." if len(error_str) > 100 else error_str
        
    except Exception:
        # If all parsing fails, return simple fallback
        return f"Database error: {type(error).__name__}"

# Embedding Configuration
USE_DIRECT_EMBEDDING = os.getenv("USE_DIRECT_EMBEDDING", "true").lower() == "true"
BGE_SERVICE_URL = os.getenv("BGE_SERVICE_URL", "http://localhost:8080")
BGE_TIMEOUT = int(os.getenv("BGE_TIMEOUT", "30"))  # seconds
EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME", "BAAI/bge-base-en-v1.5")
EMBEDDING_BATCH_SIZE = int(os.getenv("EMBEDDING_BATCH_SIZE", "32"))
EMBEDDING_DEVICE = os.getenv("EMBEDDING_DEVICE", "auto")  # auto, cpu, cuda

# OpenRouter Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_BASE_URL = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
MODEL_QUALITY_LEVEL = os.getenv("MODEL_QUALITY_LEVEL", "balanced")  # free, balanced, premium

# Model Mappings - configurable via environment variables
CODE_SUMMARIZATION_MODEL = os.getenv("CODE_SUMMARIZATION_MODEL", "google/gemini-flash-1.5")
SOURCE_SUMMARIZATION_MODEL = os.getenv("SOURCE_SUMMARIZATION_MODEL", "mistralai/mistral-7b-instruct:free")
CONTEXTUAL_EMBEDDINGS_MODEL = os.getenv("CONTEXTUAL_EMBEDDINGS_MODEL", "google/gemini-flash-1.5")

class LLMStage(Enum):
    """Enumeration of different LLM usage stages"""
    CODE_SUMMARIZATION = "code_summarization"
    SOURCE_SUMMARIZATION = "source_summarization"
    CONTEXTUAL_EMBEDDINGS = "contextual_embeddings"

@dataclass
class ModelConfig:
    """Configuration for a specific model"""
    name: str
    cost_per_1k_tokens: float
    max_tokens: int
    context_window: int
    quality_tier: str

class ModelSelector:
    """Intelligent model selection for different LLM stages"""
    
    def __init__(self):
        """Initialize model selector with configurable model mappings"""
        # Default model configurations - can be overridden via environment variables
        self.model_configs = {
            # Free models (no cost)
            "google/gemini-flash-1.5": ModelConfig("google/gemini-flash-1.5", 0.0, 8192, 1048576, "free"),
            "mistralai/mistral-7b-instruct:free": ModelConfig("mistralai/mistral-7b-instruct:free", 0.0, 32768, 32768, "free"),
            "huggingfaceh4/zephyr-7b-beta:free": ModelConfig("huggingfaceh4/zephyr-7b-beta:free", 0.0, 4096, 32768, "free"),
            
            # Balanced models (moderate cost, good performance)
            "anthropic/claude-3-haiku": ModelConfig("anthropic/claude-3-haiku", 0.25, 4096, 200000, "balanced"),
            "openai/gpt-3.5-turbo": ModelConfig("openai/gpt-3.5-turbo", 0.5, 4096, 16384, "balanced"),
            "google/gemini-pro": ModelConfig("google/gemini-pro", 0.5, 8192, 30720, "balanced"),
            
            # Premium models (higher cost, best performance)
            "anthropic/claude-3-sonnet": ModelConfig("anthropic/claude-3-sonnet", 3.0, 4096, 200000, "premium"),
            "openai/gpt-4": ModelConfig("openai/gpt-4", 30.0, 8192, 8192, "premium"),
            "anthropic/claude-3-opus": ModelConfig("anthropic/claude-3-opus", 15.0, 4096, 200000, "premium")
        }
        
        # Stage-specific model mappings based on environment variables
        self.stage_models = {
            LLMStage.CODE_SUMMARIZATION: CODE_SUMMARIZATION_MODEL,
            LLMStage.SOURCE_SUMMARIZATION: SOURCE_SUMMARIZATION_MODEL,
            LLMStage.CONTEXTUAL_EMBEDDINGS: CONTEXTUAL_EMBEDDINGS_MODEL
        }
    
    def get_model_for_stage(self, stage: LLMStage) -> str:
        """Get the configured model for a specific stage"""
        return self.stage_models.get(stage, "google/gemini-flash-1.5")  # Default fallback
    
    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """Get configuration for a specific model"""
        return self.model_configs.get(model_name)
    
    def validate_model_availability(self, model_name: str) -> bool:
        """Check if a model is available in our configuration"""
        return model_name in self.model_configs

class OpenRouterClient:
    """OpenRouter API client for LLM interactions"""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize OpenRouter client"""
        self.api_key = api_key or OPENROUTER_API_KEY
        self.base_url = OPENROUTER_BASE_URL
        self.model_selector = ModelSelector()
        
        if not self.api_key:
            print("Warning: OPENROUTER_API_KEY not set. OpenRouter functionality will be disabled.")
    
    def is_available(self) -> bool:
        """Check if OpenRouter is available (API key configured)"""
        return bool(self.api_key)
    
    def _make_request(self, model: str, messages: List[Dict[str, str]], 
                     max_tokens: int = 150, temperature: float = 0.3) -> Optional[str]:
        """Make a request to OpenRouter API"""
        if not self.api_key:
            raise Exception("OpenRouter API key not configured")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/coleam00/mcp-crawl4ai-rag",
            "X-Title": "MCP Crawl4AI RAG Server"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            data = response.json()
            
            if "choices" in data and len(data["choices"]) > 0:
                return data["choices"][0]["message"]["content"].strip()
            else:
                raise Exception("No response content received from OpenRouter")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"OpenRouter API request failed: {e}")
        except Exception as e:
            raise Exception(f"OpenRouter API error: {e}")
    
    def generate_code_summary(self, code: str, context_before: str, context_after: str) -> str:
        """Generate a summary for a code example"""
        model = self.model_selector.get_model_for_stage(LLMStage.CODE_SUMMARIZATION)
        
        # Truncate inputs to prevent token overflow
        code_snippet = code[:1500] if len(code) > 1500 else code
        context_before_snippet = context_before[-500:] if len(context_before) > 500 else context_before
        context_after_snippet = context_after[:500] if len(context_after) > 500 else context_after
        
        prompt = f"""<context_before>
{context_before_snippet}
</context_before>

<code_example>
{code_snippet}
</code_example>

<context_after>
{context_after_snippet}
</context_after>

Based on the code example and its surrounding context, provide a concise summary (2-3 sentences) that describes what this code example demonstrates and its purpose. Focus on the practical application and key concepts illustrated."""
        
        messages = [
            {"role": "system", "content": "You are a helpful assistant that provides concise code example summaries."},
            {"role": "user", "content": prompt}
        ]
        
        try:
            return self._make_request(model, messages, max_tokens=100, temperature=0.3)
        except Exception as e:
            print(f"Error generating code summary with OpenRouter: {e}")
            return "Code example for demonstration purposes."
    
    def generate_source_summary(self, source_id: str, content: str) -> str:
        """Generate a summary for a source/library"""
        model = self.model_selector.get_model_for_stage(LLMStage.SOURCE_SUMMARIZATION)
        
        # Limit content length to avoid token limits
        truncated_content = content[:25000] if len(content) > 25000 else content
        
        prompt = f"""<source_content>
{truncated_content}
</source_content>

The above content is from the documentation for '{source_id}'. Please provide a concise summary (3-5 sentences) that describes what this library/tool/framework is about. The summary should help understand what the library/tool/framework accomplishes and the purpose."""
        
        messages = [
            {"role": "system", "content": "You are a helpful assistant that provides concise library/tool/framework summaries."},
            {"role": "user", "content": prompt}
        ]
        
        try:
            return self._make_request(model, messages, max_tokens=150, temperature=0.3)
        except Exception as e:
            print(f"Error generating source summary with OpenRouter: {e}")
            return f"Content from {source_id}"
    
    def generate_contextual_embedding_text(self, full_document: str, chunk: str) -> str:
        """Generate contextual information for a chunk within a document"""
        model = self.model_selector.get_model_for_stage(LLMStage.CONTEXTUAL_EMBEDDINGS)
        
        # Truncate inputs to prevent token overflow
        document_snippet = full_document[:15000] if len(full_document) > 15000 else full_document
        chunk_snippet = chunk[:3000] if len(chunk) > 3000 else chunk
        
        prompt = f"""<full_document>
{document_snippet}
</full_document>

<chunk>
{chunk_snippet}
</chunk>

Given the above document and the specific chunk, provide a short contextual description (1-2 sentences) that situates this chunk within the broader document. This will help with more accurate retrieval. Focus on what this chunk is about and how it relates to the overall document."""
        
        messages = [
            {"role": "system", "content": "You are a helpful assistant that provides contextual information for document chunks."},
            {"role": "user", "content": prompt}
        ]
        
        try:
            contextual_info = self._make_request(model, messages, max_tokens=100, temperature=0.2)
            # Combine original chunk with contextual information
            return f"Context: {contextual_info}\n\nContent: {chunk}"
        except Exception as e:
            print(f"Error generating contextual embedding with OpenRouter: {e}")
            return chunk  # Return original chunk if contextual generation fails

# Initialize global OpenRouter client
_openrouter_client = None

def get_openrouter_client() -> Optional[OpenRouterClient]:
    """Get or initialize the global OpenRouter client"""
    global _openrouter_client
    if _openrouter_client is None:
        _openrouter_client = OpenRouterClient()
    return _openrouter_client if _openrouter_client.is_available() else None

class EmbeddingManager:
    """Direct GPU embedding generation manager for optimal performance"""
    
    def __init__(self):
        self.model = None
        self.device = None
        self._lock = threading.Lock()
        self._load_model()
    
    def _detect_device(self) -> str:
        """Detect best available device for embedding generation"""
        if EMBEDDING_DEVICE.lower() != "auto":
            return EMBEDDING_DEVICE
        
        try:
            import torch
            if torch.cuda.is_available():
                return "cuda"
        except ImportError:
            pass
        
        return "cpu"
    
    def _load_model(self):
        """Load the BGE embedding model with optimal device configuration"""
        try:
            from FlagEmbedding import FlagModel
            import torch
            
            self.device = self._detect_device()
            
            # Configure GPU environment for optimal performance
            if self.device == "cuda" and torch.cuda.is_available():
                # Set GPU device visibility (best practice from reference)
                os.environ['CUDA_VISIBLE_DEVICES'] = '0'
                print(f"Configured CUDA_VISIBLE_DEVICES=0 for GPU isolation")
            
            print(f"Initializing BGE model '{EMBEDDING_MODEL_NAME}' on device: {self.device}")
            start_time = time.time()
            
            # Configure device for FlagEmbedding
            if self.device == "cuda":
                devices_param = ["cuda:0"]
                use_fp16 = True
            else:
                devices_param = "cpu"  # Use string "cpu" for CPU mode
                use_fp16 = False
            
            self.model = FlagModel(
                EMBEDDING_MODEL_NAME,
                devices=devices_param,
                use_fp16=use_fp16,
                query_instruction_for_retrieval="Represent this sentence for searching relevant passages:"
            )
            
            load_time = time.time() - start_time
            print(f"BGE model loaded successfully in {load_time:.2f} seconds on {self.device}")
            
            # Log GPU info if available and validate GPU configuration
            if self.device == "cuda" and torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                print(f"Using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                
                # Validate GPU has sufficient memory (minimum 2GB for BGE)
                if gpu_memory < 2.0:
                    print(f"Warning: GPU memory ({gpu_memory:.1f}GB) is below recommended 2GB")
                    print("Consider using CPU mode or smaller batch sizes")
                
                # Validate model is actually on GPU
                if hasattr(self.model.model, 'device'):
                    model_device = str(self.model.model.device)
                    if 'cuda' not in model_device:
                        print(f"Warning: Model loaded on {model_device}, expected CUDA")
                        
        except ImportError as e:
            logging.error(f"Failed to import FlagEmbedding or torch: {e}")
            print(f"Failed to import FlagEmbedding or torch: {e}")
            print("Direct embedding disabled - falling back to HTTP service")
            print("Install with: pip install FlagEmbedding torch")
            self.model = None
        except Exception as e:
            # Check if it's a CUDA OutOfMemoryError specifically
            if 'OutOfMemoryError' in str(type(e)) or 'CUDA out of memory' in str(e):
                logging.error(f"GPU out of memory during model initialization: {e}")
                print(f"GPU out of memory during model initialization: {e}")
                print("Consider using CPU mode: set EMBEDDING_DEVICE=cpu")
                self.model = None
            else:
                logging.error(f"Failed to initialize BGE model: {e}")
                print(f"Failed to initialize BGE model: {e}")
                print("Direct embedding disabled - falling back to HTTP service")
                self.model = None
    
    def batch_embed(self, texts: List[str], batch_size: int = None) -> List[List[float]]:
        """Generate embeddings with optimal batch processing and memory management"""
        if not texts:
            return []
        
        if self.model is None:
            raise ValueError("Direct embedding model not available. Check model initialization logs.")
        
        # Validate input texts
        if not all(isinstance(text, str) for text in texts):
            raise TypeError("All texts must be strings")
        
        # Check for empty or very long texts that might cause issues
        max_text_length = 8192  # Reasonable limit for BGE
        for i, text in enumerate(texts):
            if not text.strip():
                logging.warning(f"Empty text at index {i}, using placeholder")
                texts[i] = "[empty text]"
            elif len(text) > max_text_length:
                logging.warning(f"Text at index {i} exceeds {max_text_length} chars, truncating")
                texts[i] = text[:max_text_length]
        
        batch_size = batch_size or EMBEDDING_BATCH_SIZE
        
        # Adaptive batch sizing for GPU memory management
        if self.device == "cuda":
            batch_size = self._calculate_optimal_batch_size(texts, batch_size)
        
        embeddings = []
        start_time = time.time()
        
        try:
            with self._lock:  # Thread safety for model access
                for i in range(0, len(texts), batch_size):
                    batch = texts[i:i + batch_size]
                    batch_embeddings = self.model.encode(
                        batch,
                        batch_size=len(batch),  # Actual batch size for this iteration
                        convert_to_numpy=True
                    )
                    embeddings.extend(batch_embeddings.tolist())
            
            processing_time = time.time() - start_time
            print(f"Generated {len(embeddings)} embeddings in {processing_time:.3f}s using direct GPU")
            
            return embeddings
            
        except Exception as e:
            # Check if it's a CUDA OutOfMemoryError specifically
            if 'OutOfMemoryError' in str(type(e)) or 'CUDA out of memory' in str(e):
                logging.error(f"GPU out of memory during embedding generation: {e}")
                print(f"GPU out of memory: {e}")
                print(f"Try reducing batch size from {batch_size} or using CPU mode")
                raise RuntimeError(f"GPU memory insufficient for batch size {batch_size}") from e
            else:
                logging.error(f"Error in direct embedding generation: {e}")
                print(f"Error in direct embedding generation: {e}")
                raise
        finally:
            # Critical memory cleanup for sustained performance
            if self.device == "cuda":
                try:
                    import torch
                    if torch.cuda.is_available():
                        memory_before = torch.cuda.memory_allocated(0) / (1024**2)
                        torch.cuda.empty_cache()
                        memory_after = torch.cuda.memory_allocated(0) / (1024**2)
                        memory_freed = memory_before - memory_after
                        if memory_freed > 1.0:  # Only log if significant cleanup
                            logging.debug(f"GPU memory cleanup: freed {memory_freed:.1f}MB")
                except Exception as cleanup_error:
                    logging.warning(f"GPU memory cleanup failed: {cleanup_error}")
    
    def _calculate_optimal_batch_size(self, texts: List[str], default_batch_size: int) -> int:
        """Calculate optimal batch size based on text length and GPU memory"""
        try:
            import torch
            if not torch.cuda.is_available():
                return default_batch_size
            
            # Get GPU memory info
            total_memory = torch.cuda.get_device_properties(0).total_memory
            allocated_memory = torch.cuda.memory_allocated(0)
            available_memory = total_memory - allocated_memory
            
            # Safety check for low memory conditions
            memory_usage_percent = (allocated_memory / total_memory) * 100
            if memory_usage_percent > 80:
                logging.warning(f"High GPU memory usage: {memory_usage_percent:.1f}%")
                return min(default_batch_size // 2, 8)  # Conservative batch size
            
            # Estimate memory per text (improved heuristic)
            avg_text_length = sum(len(text) for text in texts) / len(texts)
            # More accurate estimate: character embedding + model overhead
            estimated_memory_per_text = avg_text_length * 0.002 + 0.5  # MB per text
            
            # Calculate safe batch size (use 40% of available memory for safety)
            safe_memory = available_memory * 0.4 / (1024**2)  # Convert to MB
            optimal_batch_size = max(1, int(safe_memory / estimated_memory_per_text))
            
            # Clamp to reasonable bounds with better validation
            optimal_batch_size = min(optimal_batch_size, default_batch_size, 64)
            optimal_batch_size = max(optimal_batch_size, 1)
            
            # Log batch size adjustment for monitoring
            if optimal_batch_size != default_batch_size:
                logging.info(f"Adjusted batch size: {default_batch_size} → {optimal_batch_size} (GPU memory optimization)")
            
            return optimal_batch_size
            
        except Exception as e:
            logging.warning(f"Batch size calculation failed: {e}, using default")
            return default_batch_size
    
    def is_available(self) -> bool:
        """Check if direct embedding is available"""
        return self.model is not None
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive embedding manager metrics with performance monitoring"""
        metrics = {
            "direct_embedding_available": self.is_available(),
            "device": self.device,
            "model_name": EMBEDDING_MODEL_NAME if self.is_available() else None,
            "batch_size": EMBEDDING_BATCH_SIZE,
            "cuda_visible_devices": os.environ.get('CUDA_VISIBLE_DEVICES', 'not_set')
        }
        
        if self.device == "cuda":
            try:
                import torch
                if torch.cuda.is_available():
                    # Memory metrics
                    allocated_memory = torch.cuda.memory_allocated(0)
                    total_memory = torch.cuda.get_device_properties(0).total_memory
                    memory_usage_percent = (allocated_memory / total_memory) * 100
                    
                    # Performance metrics
                    gpu_props = torch.cuda.get_device_properties(0)
                    
                    metrics.update({
                        "gpu_memory_allocated_mb": allocated_memory / (1024**2),
                        "gpu_memory_total_mb": total_memory / (1024**2),
                        "gpu_memory_usage_percent": memory_usage_percent,
                        "gpu_memory_free_mb": (total_memory - allocated_memory) / (1024**2),
                        "gpu_name": torch.cuda.get_device_name(0),
                        "gpu_compute_capability": f"{gpu_props.major}.{gpu_props.minor}",
                        "gpu_multiprocessor_count": gpu_props.multi_processor_count,
                        "cuda_version": torch.version.cuda,
                        "pytorch_version": torch.__version__,
                        "memory_status": self._get_memory_status(memory_usage_percent)
                    })
                    
                    # Add memory cleanup recommendation if needed
                    if memory_usage_percent > 75:
                        metrics["recommendation"] = "Consider reducing batch size or calling torch.cuda.empty_cache()"
                        
            except Exception as e:
                metrics["gpu_metrics_error"] = str(e)
        
        return metrics
    
    def _get_memory_status(self, usage_percent: float) -> str:
        """Get human-readable memory status"""
        if usage_percent < 50:
            return "optimal"
        elif usage_percent < 75:
            return "moderate"
        elif usage_percent < 90:
            return "high"
        else:
            return "critical"

# Initialize global embedding manager
_embedding_manager = None

def get_embedding_manager() -> EmbeddingManager:
    """Get or initialize the global embedding manager"""
    global _embedding_manager
    if _embedding_manager is None:
        _embedding_manager = EmbeddingManager()
    return _embedding_manager

def _make_bge_request(endpoint: str, data: dict, timeout: int = BGE_TIMEOUT) -> dict:
    """Make a request to the BGE embedding service"""
    url = f"{BGE_SERVICE_URL.rstrip('/')}/{endpoint.lstrip('/')}"
    
    try:
        response = requests.post(
            url,
            json=data,
            headers={'Content-Type': 'application/json'},
            timeout=timeout
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.Timeout:
        raise Exception(f"BGE service timeout after {timeout} seconds")
    except requests.exceptions.ConnectionError:
        raise Exception(f"Cannot connect to BGE service at {BGE_SERVICE_URL}")
    except requests.exceptions.HTTPError as e:
        raise Exception(f"BGE service HTTP error: {e}")
    except Exception as e:
        raise Exception(f"BGE service error: {e}")

def get_supabase_client() -> Client:
    """
    Get a Supabase client with the URL and key from environment variables.
    
    Returns:
        Supabase client instance
    """
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables")
    
    return create_client(url, key)

def create_embeddings_batch(texts: List[str]) -> List[List[float]]:
    """
    Create embeddings for multiple texts using direct GPU or BGE service fallback.
    
    Args:
        texts: List of texts to create embeddings for
        
    Returns:
        List of embeddings (each embedding is a list of floats)
    """
    if not texts:
        return []
    
    # Try direct embedding first if enabled and available
    if USE_DIRECT_EMBEDDING:
        try:
            embedding_manager = get_embedding_manager()
            if embedding_manager.is_available():
                return embedding_manager.batch_embed(texts)
        except Exception as e:
            print(f"Direct embedding failed: {e}")
            print("Falling back to HTTP BGE service...")
    
    # Fallback to HTTP BGE service
    return _create_embeddings_batch_http(texts)

def _create_embeddings_batch_http(texts: List[str]) -> List[List[float]]:
    """
    Create embeddings using HTTP BGE service (fallback method).
    
    Args:
        texts: List of texts to create embeddings for
        
    Returns:
        List of embeddings (each embedding is a list of floats)
    """
    max_retries = 3
    retry_delay = 1.0  # Start with 1 second delay
    
    for retry in range(max_retries):
        try:
            response = _make_bge_request('embed', {'texts': texts})
            embeddings = response.get('embeddings', [])
            
            if not embeddings:
                raise Exception("No embeddings returned from BGE service")
            
            print(f"Successfully created {len(embeddings)} embeddings using HTTP BGE service")
            return embeddings
            
        except Exception as e:
            if retry < max_retries - 1:
                print(f"Error creating batch embeddings (attempt {retry + 1}/{max_retries}): {e}")
                print(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                print(f"Failed to create batch embeddings after {max_retries} attempts: {e}")
                # Try creating embeddings one by one as fallback
                print("Attempting to create embeddings individually...")
                embeddings = []
                successful_count = 0
                
                for i, text in enumerate(texts):
                    try:
                        individual_response = _make_bge_request('embed/single', {'text': text})
                        embedding = individual_response.get('embedding', [])
                        if embedding:
                            embeddings.append(embedding)
                            successful_count += 1
                        else:
                            # Add zero embedding as fallback
                            embeddings.append([0.0] * 768)  # BGE uses 768 dimensions
                    except Exception as individual_error:
                        print(f"Failed to create embedding for text {i}: {individual_error}")
                        # Add zero embedding as fallback
                        embeddings.append([0.0] * 768)  # BGE uses 768 dimensions
                
                print(f"Successfully created {successful_count}/{len(texts)} embeddings individually")
                return embeddings

def create_embedding(text: str) -> List[float]:
    """
    Create an embedding for a single text using BGE embedding service.
    
    Args:
        text: Text to create an embedding for
        
    Returns:
        List of floats representing the embedding
    """
    try:
        embeddings = create_embeddings_batch([text])
        return embeddings[0] if embeddings else [0.0] * 768  # BGE uses 768 dimensions
    except Exception as e:
        print(f"Error creating embedding: {e}")
        # Return empty embedding if there's an error
        return [0.0] * 768  # BGE uses 768 dimensions

def generate_contextual_embedding(full_document: str, chunk: str) -> Tuple[str, bool]:
    """
    Generate contextual information for a chunk within a document to improve retrieval.
    
    Uses OpenRouter integration for contextual embedding generation when available.
    
    Args:
        full_document: The complete document text
        chunk: The specific chunk of text to enhance
        
    Returns:
        Tuple containing:
        - The enhanced chunk text with contextual information (or original if no OpenRouter)
        - Boolean indicating if contextual embedding was performed
    """
    # Check if contextual embeddings are enabled and OpenRouter is available
    use_contextual_embeddings = os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false").lower() == "true"
    
    if not use_contextual_embeddings:
        return chunk, False
    
    client = get_openrouter_client()
    if not client:
        print("OpenRouter not available for contextual embeddings")
        return chunk, False
    
    try:
        # Generate contextual embedding text using OpenRouter
        contextual_text = client.generate_contextual_embedding_text(full_document, chunk)
        return contextual_text, True
    except Exception as e:
        print(f"Error generating contextual embedding: {e}")
        return chunk, False

def process_chunk_with_context(args):
    """
    Process a single chunk with contextual embedding.
    This function is designed to be used with concurrent.futures.
    
    Args:
        args: Tuple containing (url, content, full_document)
        
    Returns:
        Tuple containing:
        - The contextual text that situates the chunk within the document
        - Boolean indicating if contextual embedding was performed
    """
    url, content, full_document = args
    return generate_contextual_embedding(full_document, content)

def add_documents_to_supabase(
    client: Client, 
    urls: List[str], 
    chunk_numbers: List[int],
    contents: List[str], 
    metadatas: List[Dict[str, Any]],
    url_to_full_document: Dict[str, str],
    batch_size: int = 20
) -> None:
    """
    Add documents to the Supabase crawled_pages table in batches.
    Deletes existing records with the same URLs before inserting to prevent duplicates.
    
    Args:
        client: Supabase client
        urls: List of URLs
        chunk_numbers: List of chunk numbers
        contents: List of document contents
        metadatas: List of document metadata
        url_to_full_document: Dictionary mapping URLs to their full document content
        batch_size: Size of each batch for insertion
    """
    # Get unique URLs to delete existing records
    unique_urls = list(set(urls))
    
    # Delete existing records for these URLs in a single operation
    try:
        if unique_urls:
            # Use the .in_() filter to delete all records with matching URLs
            client.table("crawled_pages").delete().in_("url", unique_urls).execute()
    except Exception as e:
        print(f"Batch delete failed: {e}. Trying one-by-one deletion as fallback.")
        # Fallback: delete records one by one
        for url in unique_urls:
            try:
                client.table("crawled_pages").delete().eq("url", url).execute()
            except Exception as inner_e:
                print(f"Error deleting record for URL {url}: {inner_e}")
                # Continue with the next URL even if one fails
    
    # Check if MODEL_CHOICE is set for contextual embeddings
    use_contextual_embeddings = os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false") == "true"
    print(f"\n\nUse contextual embeddings: {use_contextual_embeddings}\n\n")
    
    # Process in batches to avoid memory issues
    for i in range(0, len(contents), batch_size):
        batch_end = min(i + batch_size, len(contents))
        
        # Get batch slices
        batch_urls = urls[i:batch_end]
        batch_chunk_numbers = chunk_numbers[i:batch_end]
        batch_contents = contents[i:batch_end]
        batch_metadatas = metadatas[i:batch_end]
        
        # Apply contextual embedding to each chunk if MODEL_CHOICE is set
        if use_contextual_embeddings:
            # Prepare arguments for parallel processing
            process_args = []
            for j, content in enumerate(batch_contents):
                url = batch_urls[j]
                full_document = url_to_full_document.get(url, "")
                process_args.append((url, content, full_document))
            
            # Process in parallel using ThreadPoolExecutor
            contextual_contents = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                # Submit all tasks and collect results
                future_to_idx = {executor.submit(process_chunk_with_context, arg): idx 
                                for idx, arg in enumerate(process_args)}
                
                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_idx):
                    idx = future_to_idx[future]
                    try:
                        result, success = future.result()
                        contextual_contents.append(result)
                        if success:
                            batch_metadatas[idx]["contextual_embedding"] = True
                    except Exception as e:
                        print(f"Error processing chunk {idx}: {e}")
                        # Use original content as fallback
                        contextual_contents.append(batch_contents[idx])
            
            # Sort results back into original order if needed
            if len(contextual_contents) != len(batch_contents):
                print(f"Warning: Expected {len(batch_contents)} results but got {len(contextual_contents)}")
                # Use original contents as fallback
                contextual_contents = batch_contents
        else:
            # If not using contextual embeddings, use original contents
            contextual_contents = batch_contents
        
        # Create embeddings for the entire batch at once
        batch_embeddings = create_embeddings_batch(contextual_contents)
        
        batch_data = []
        for j in range(len(contextual_contents)):
            # Extract metadata fields
            chunk_size = len(contextual_contents[j])
            
            # Extract source_id from URL
            parsed_url = urlparse(batch_urls[j])
            source_id = parsed_url.netloc or parsed_url.path
            
            # Prepare data for insertion
            data = {
                "url": batch_urls[j],
                "chunk_number": batch_chunk_numbers[j],
                "content": contextual_contents[j],  # Store original content
                "metadata": {
                    "chunk_size": chunk_size,
                    **batch_metadatas[j]
                },
                "source_id": source_id,  # Add source_id field
                "embedding": batch_embeddings[j]  # Use embedding from contextual content
            }
            
            batch_data.append(data)
        
        # Insert batch into Supabase with retry logic
        max_retries = 3
        retry_delay = 1.0  # Start with 1 second delay
        
        for retry in range(max_retries):
            try:
                client.table("crawled_pages").insert(batch_data).execute()
                # Success - break out of retry loop
                break
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"Error inserting batch into Supabase (attempt {retry + 1}/{max_retries}): {e}")
                    print(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Final attempt failed
                    print(f"Failed to insert batch after {max_retries} attempts: {e}")
                    # Optionally, try inserting records one by one as a last resort
                    print("Attempting to insert records individually...")
                    successful_inserts = 0
                    for record in batch_data:
                        try:
                            client.table("crawled_pages").insert(record).execute()
                            successful_inserts += 1
                        except Exception as individual_error:
                            print(f"Failed to insert individual record for URL {record['url']}: {extract_meaningful_error(individual_error)}")
                    
                    if successful_inserts > 0:
                        print(f"Successfully inserted {successful_inserts}/{len(batch_data)} records individually")

def search_documents(
    client: Client, 
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """
    Search for documents in Supabase using vector similarity.
    
    Args:
        client: Supabase client
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        
    Returns:
        List of matching documents
    """
    # Create embedding for the query
    query_embedding = create_embedding(query)
    
    # Execute the search using the match_crawled_pages function
    try:
        # Only include filter parameter if filter_metadata is provided and not empty
        params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        # Only add the filter if it's actually provided and not empty
        if filter_metadata:
            params['filter'] = filter_metadata  # Pass the dictionary directly, not JSON-encoded
        
        result = client.rpc('match_crawled_pages', params).execute()
        
        return result.data
    except Exception as e:
        print(f"Error searching documents: {e}")
        return []


def extract_code_blocks(markdown_content: str, min_length: int = 1000) -> List[Dict[str, Any]]:
    """
    Extract code blocks from markdown content along with context.
    
    Args:
        markdown_content: The markdown content to extract code blocks from
        min_length: Minimum length of code blocks to extract (default: 1000 characters)
        
    Returns:
        List of dictionaries containing code blocks and their context
    """
    code_blocks = []
    
    # Skip if content starts with triple backticks (edge case for files wrapped in backticks)
    content = markdown_content.strip()
    start_offset = 0
    if content.startswith('```'):
        # Skip the first triple backticks
        start_offset = 3
        print("Skipping initial triple backticks")
    
    # Find all occurrences of triple backticks
    backtick_positions = []
    pos = start_offset
    while True:
        pos = markdown_content.find('```', pos)
        if pos == -1:
            break
        backtick_positions.append(pos)
        pos += 3
    
    # Process pairs of backticks
    i = 0
    while i < len(backtick_positions) - 1:
        start_pos = backtick_positions[i]
        end_pos = backtick_positions[i + 1]
        
        # Extract the content between backticks
        code_section = markdown_content[start_pos+3:end_pos]
        
        # Check if there's a language specifier on the first line
        lines = code_section.split('\n', 1)
        if len(lines) > 1:
            # Check if first line is a language specifier (no spaces, common language names)
            first_line = lines[0].strip()
            if first_line and not ' ' in first_line and len(first_line) < 20:
                language = first_line
                code_content = lines[1].strip() if len(lines) > 1 else ""
            else:
                language = ""
                code_content = code_section.strip()
        else:
            language = ""
            code_content = code_section.strip()
        
        # Skip if code block is too short
        if len(code_content) < min_length:
            i += 2  # Move to next pair
            continue
        
        # Extract context before (1000 chars)
        context_start = max(0, start_pos - 1000)
        context_before = markdown_content[context_start:start_pos].strip()
        
        # Extract context after (1000 chars)
        context_end = min(len(markdown_content), end_pos + 3 + 1000)
        context_after = markdown_content[end_pos + 3:context_end].strip()
        
        code_blocks.append({
            'code': code_content,
            'language': language,
            'context_before': context_before,
            'context_after': context_after,
            'full_context': f"{context_before}\n\n{code_content}\n\n{context_after}"
        })
        
        # Move to next pair (skip the closing backtick we just processed)
        i += 2
    
    return code_blocks


def generate_code_example_summary(code: str, context_before: str, context_after: str) -> str:
    """
    Generate a summary for a code example using its surrounding context.
    
    Uses OpenRouter integration for code summary generation when available.
    
    Args:
        code: The code example
        context_before: Context before the code
        context_after: Context after the code
        
    Returns:
        A summary of what the code example demonstrates
    """
    # Check if OpenRouter is available
    client = get_openrouter_client()
    if not client:
        print("OpenRouter not available for code example summary generation")
        return "Code example for demonstration purposes."
    
    try:
        return client.generate_code_summary(code, context_before, context_after)
    except Exception as e:
        print(f"Error generating code example summary: {e}")
        return "Code example for demonstration purposes."


def add_code_examples_to_supabase(
    client: Client,
    urls: List[str],
    chunk_numbers: List[int],
    code_examples: List[str],
    summaries: List[str],
    metadatas: List[Dict[str, Any]],
    batch_size: int = 20
):
    """
    Add code examples to the Supabase code_examples table in batches.
    
    Args:
        client: Supabase client
        urls: List of URLs
        chunk_numbers: List of chunk numbers
        code_examples: List of code example contents
        summaries: List of code example summaries
        metadatas: List of metadata dictionaries
        batch_size: Size of each batch for insertion
    """
    if not urls:
        return
        
    # Delete existing records for these URLs
    unique_urls = list(set(urls))
    for url in unique_urls:
        try:
            client.table('code_examples').delete().eq('url', url).execute()
        except Exception as e:
            print(f"Error deleting existing code examples for {url}: {extract_meaningful_error(e)}")
    
    # Process in batches
    total_items = len(urls)
    for i in range(0, total_items, batch_size):
        batch_end = min(i + batch_size, total_items)
        batch_texts = []
        
        # Create combined texts for embedding (code + summary)
        for j in range(i, batch_end):
            combined_text = f"{code_examples[j]}\n\nSummary: {summaries[j]}"
            batch_texts.append(combined_text)
        
        # Create embeddings for the batch
        embeddings = create_embeddings_batch(batch_texts)
        
        # Check if embeddings are valid (not all zeros)
        valid_embeddings = []
        for embedding in embeddings:
            if embedding and not all(v == 0.0 for v in embedding):
                valid_embeddings.append(embedding)
            else:
                print(f"Warning: Zero or invalid embedding detected, creating new one...")
                # Try to create a single embedding as fallback
                single_embedding = create_embedding(batch_texts[len(valid_embeddings)])
                valid_embeddings.append(single_embedding)
        
        # Prepare batch data
        batch_data = []
        for j, embedding in enumerate(valid_embeddings):
            idx = i + j
            
            # Extract source_id from URL
            parsed_url = urlparse(urls[idx])
            source_id = parsed_url.netloc or parsed_url.path
            
            batch_data.append({
                'url': urls[idx],
                'chunk_number': chunk_numbers[idx],
                'content': code_examples[idx],
                'summary': summaries[idx],
                'metadata': metadatas[idx],  # Store as JSON object, not string
                'source_id': source_id,
                'embedding': embedding
            })
        
        # Insert batch into Supabase with retry logic
        max_retries = 3
        retry_delay = 1.0  # Start with 1 second delay
        
        for retry in range(max_retries):
            try:
                client.table('code_examples').insert(batch_data).execute()
                # Success - break out of retry loop
                break
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"Error inserting batch into Supabase (attempt {retry + 1}/{max_retries}): {e}")
                    print(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    # Final attempt failed
                    print(f"Failed to insert batch after {max_retries} attempts: {e}")
                    # Optionally, try inserting records one by one as a last resort
                    print("Attempting to insert records individually...")
                    successful_inserts = 0
                    for record in batch_data:
                        try:
                            client.table('code_examples').insert(record).execute()
                            successful_inserts += 1
                        except Exception as individual_error:
                            print(f"Failed to insert individual record for URL {record['url']}: {extract_meaningful_error(individual_error)}")
                    
                    if successful_inserts > 0:
                        print(f"Successfully inserted {successful_inserts}/{len(batch_data)} records individually")
        print(f"Inserted batch {i//batch_size + 1} of {(total_items + batch_size - 1)//batch_size} code examples")


def update_source_info(client: Client, source_id: str, summary: str, word_count: int):
    """
    Update or insert source information in the sources table.
    
    Args:
        client: Supabase client
        source_id: The source ID (domain)
        summary: Summary of the source
        word_count: Total word count for the source
    """
    try:
        # Try to update existing source
        result = client.table('sources').update({
            'summary': summary,
            'total_word_count': word_count,
            'updated_at': 'now()'
        }).eq('source_id', source_id).execute()
        
        # If no rows were updated, insert new source
        if not result.data:
            client.table('sources').insert({
                'source_id': source_id,
                'summary': summary,
                'total_word_count': word_count
            }).execute()
            print(f"Created new source: {source_id}")
        else:
            print(f"Updated source: {source_id}")
            
    except Exception as e:
        print(f"Error updating source {source_id}: {e}")


def extract_source_summary(source_id: str, content: str, max_length: int = 500) -> str:
    """
    Extract a summary for a source from its content using OpenRouter LLM.
    
    Args:
        source_id: The source ID (domain)
        content: The content to extract a summary from
        max_length: Maximum length of the summary
        
    Returns:
        A summary string
    """
    # Default summary if we can't extract anything meaningful
    default_summary = f"Content from {source_id}"
    
    if not content or len(content.strip()) == 0:
        return default_summary
    
    # Check if OpenRouter is available
    client = get_openrouter_client()
    if not client:
        print(f"OpenRouter not available for source summary generation for {source_id}")
        return default_summary
    
    try:
        summary = client.generate_source_summary(source_id, content)
        
        # Ensure the summary is not too long
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
            
        return summary
    except Exception as e:
        print(f"Error generating summary with OpenRouter for {source_id}: {e}. Using default summary.")
        return default_summary


def search_code_examples(
    client: Client, 
    query: str, 
    match_count: int = 10, 
    filter_metadata: Optional[Dict[str, Any]] = None,
    source_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Search for code examples in Supabase using vector similarity.
    
    Args:
        client: Supabase client
        query: Query text
        match_count: Maximum number of results to return
        filter_metadata: Optional metadata filter
        source_id: Optional source ID to filter results
        
    Returns:
        List of matching code examples
    """
    # Create a more descriptive query for better embedding match
    # Since code examples are embedded with their summaries, we should make the query more descriptive
    enhanced_query = f"Code example for {query}\n\nSummary: Example code showing {query}"
    
    # Create embedding for the enhanced query
    query_embedding = create_embedding(enhanced_query)
    
    # Execute the search using the match_code_examples function
    try:
        # Only include filter parameter if filter_metadata is provided and not empty
        params = {
            'query_embedding': query_embedding,
            'match_count': match_count
        }
        
        # Only add the filter if it's actually provided and not empty
        if filter_metadata:
            params['filter'] = filter_metadata
            
        # Add source filter if provided
        if source_id:
            params['source_filter'] = source_id
        
        result = client.rpc('match_code_examples', params).execute()
        
        return result.data
    except Exception as e:
        print(f"Error searching code examples: {e}")
        return []