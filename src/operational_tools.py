"""
Operational Tools and Utilities for MCP Crawl4AI

Provides operational readiness tools including health checks, diagnostics,
performance profiling, and system maintenance utilities.
"""

import asyncio
import logging
import psutil
import platform
import sys
import time
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import aiohttp
from pathlib import Path
import subprocess

logger = logging.getLogger(__name__)


@dataclass
class SystemHealth:
    """System health status information"""
    status: str  # healthy, degraded, unhealthy
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    open_files: int
    active_connections: int
    uptime_seconds: float
    checks_passed: int
    checks_failed: int
    last_check: datetime
    details: Dict[str, Any]


class HealthChecker:
    """Comprehensive health checking system"""
    
    def __init__(self, config_manager=None):
        self.config = config_manager
        self.start_time = time.time()
        self.health_checks = {
            "system_resources": self._check_system_resources,
            "database_connectivity": self._check_database,
            "crawler_status": self._check_crawler,
            "worker_status": self._check_workers,
            "external_services": self._check_external_services
        }
        
    async def check_health(self) -> SystemHealth:
        """Perform comprehensive health check"""
        checks_passed = 0
        checks_failed = 0
        details = {}
        
        # Run all health checks
        for name, check_func in self.health_checks.items():
            try:
                result = await check_func()
                if result["healthy"]:
                    checks_passed += 1
                else:
                    checks_failed += 1
                details[name] = result
            except Exception as e:
                checks_failed += 1
                details[name] = {
                    "healthy": False,
                    "error": str(e)
                }
                
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Determine overall status
        if checks_failed == 0:
            status = "healthy"
        elif checks_failed < len(self.health_checks) / 2:
            status = "degraded"
        else:
            status = "unhealthy"
            
        # Count open files and connections
        process = psutil.Process()
        open_files = len(process.open_files())
        connections = len(process.connections())
        
        return SystemHealth(
            status=status,
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            disk_percent=disk.percent,
            open_files=open_files,
            active_connections=connections,
            uptime_seconds=time.time() - self.start_time,
            checks_passed=checks_passed,
            checks_failed=checks_failed,
            last_check=datetime.utcnow(),
            details=details
        )
        
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Define thresholds
        cpu_threshold = 80.0
        memory_threshold = 85.0
        disk_threshold = 90.0
        
        healthy = (
            cpu_percent < cpu_threshold and
            memory.percent < memory_threshold and
            disk.percent < disk_threshold
        )
        
        return {
            "healthy": healthy,
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "disk_percent": disk.percent,
            "thresholds": {
                "cpu": cpu_threshold,
                "memory": memory_threshold,
                "disk": disk_threshold
            }
        }
        
    async def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity"""
        if not self.config:
            return {"healthy": True, "skipped": True}
            
        try:
            from utils import get_supabase_client
            client = get_supabase_client()
            
            # Simple connectivity test
            start_time = time.time()
            result = client.table("sources").select("id").limit(1).execute()
            response_time = time.time() - start_time
            
            return {
                "healthy": True,
                "response_time_ms": response_time * 1000,
                "connected": True
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "connected": False
            }
            
    async def _check_crawler(self) -> Dict[str, Any]:
        """Check crawler status"""
        # This would check if the crawler is initialized and responsive
        return {
            "healthy": True,
            "crawler_initialized": True,
            "browser_connected": True
        }
        
    async def _check_workers(self) -> Dict[str, Any]:
        """Check worker status"""
        if not self.config or not self.config.get("features.enable_job_system"):
            return {"healthy": True, "skipped": True}
            
        try:
            from job_manager import get_job_manager
            job_manager = get_job_manager()
            
            # Get worker stats
            stats = await job_manager.get_job_stats()
            
            # Check if workers are processing
            healthy = stats.get("running_count", 0) < 100  # Not overloaded
            
            return {
                "healthy": healthy,
                "queued_jobs": stats.get("queued_count", 0),
                "running_jobs": stats.get("running_count", 0),
                "completed_jobs": stats.get("completed_count", 0),
                "failed_jobs": stats.get("failed_count", 0)
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }
            
    async def _check_external_services(self) -> Dict[str, Any]:
        """Check external service connectivity"""
        services = {}
        all_healthy = True
        
        # Check BGE service if enabled
        if self.config and self.config.get("storage.bge_service_url"):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.config.get('storage.bge_service_url')}/health",
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        services["bge_service"] = {
                            "healthy": response.status == 200,
                            "status_code": response.status
                        }
                        if response.status != 200:
                            all_healthy = False
            except Exception as e:
                services["bge_service"] = {
                    "healthy": False,
                    "error": str(e)
                }
                all_healthy = False
                
        return {
            "healthy": all_healthy,
            "services": services
        }


class PerformanceProfiler:
    """Performance profiling utilities"""
    
    def __init__(self):
        self.profiles = {}
        self.active_profiles = {}
        
    def start_profile(self, name: str):
        """Start a performance profile"""
        self.active_profiles[name] = {
            "start_time": time.time(),
            "start_memory": psutil.Process().memory_info().rss
        }
        
    def end_profile(self, name: str) -> Dict[str, Any]:
        """End a performance profile and return results"""
        if name not in self.active_profiles:
            return {}
            
        profile = self.active_profiles.pop(name)
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss
        
        result = {
            "name": name,
            "duration_seconds": end_time - profile["start_time"],
            "memory_delta_mb": (end_memory - profile["start_memory"]) / 1024 / 1024,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Store profile
        if name not in self.profiles:
            self.profiles[name] = []
        self.profiles[name].append(result)
        
        return result
        
    async def profile_async(self, name: str, func: Callable, *args, **kwargs):
        """Profile an async function"""
        self.start_profile(name)
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            profile = self.end_profile(name)
            logger.info(f"Profile {name}: {profile['duration_seconds']:.2f}s, "
                       f"Memory: {profile['memory_delta_mb']:.2f}MB")
            
    def get_profile_stats(self, name: str) -> Dict[str, Any]:
        """Get statistics for a named profile"""
        if name not in self.profiles:
            return {}
            
        profiles = self.profiles[name]
        durations = [p["duration_seconds"] for p in profiles]
        memory_deltas = [p["memory_delta_mb"] for p in profiles]
        
        return {
            "count": len(profiles),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "avg_memory_delta": sum(memory_deltas) / len(memory_deltas),
            "total_duration": sum(durations)
        }


class DiagnosticTool:
    """System diagnostic utilities"""
    
    def __init__(self, config_manager=None):
        self.config = config_manager
        
    async def run_diagnostics(self) -> Dict[str, Any]:
        """Run comprehensive system diagnostics"""
        diagnostics = {
            "timestamp": datetime.utcnow().isoformat(),
            "system": self._get_system_info(),
            "python": self._get_python_info(),
            "dependencies": await self._check_dependencies(),
            "configuration": self._get_config_diagnostics(),
            "network": await self._check_network(),
            "storage": await self._check_storage()
        }
        
        return diagnostics
        
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            "platform": platform.platform(),
            "processor": platform.processor(),
            "cpu_count": psutil.cpu_count(),
            "total_memory_gb": psutil.virtual_memory().total / 1024**3,
            "python_version": sys.version,
            "hostname": platform.node()
        }
        
    def _get_python_info(self) -> Dict[str, Any]:
        """Get Python environment information"""
        return {
            "version": sys.version,
            "executable": sys.executable,
            "path": sys.path[:5],  # First 5 paths
            "prefix": sys.prefix
        }
        
    async def _check_dependencies(self) -> Dict[str, Any]:
        """Check required dependencies"""
        dependencies = {}
        
        # Check Python packages
        required_packages = [
            "crawl4ai",
            "supabase",
            "fastmcp",
            "sentence-transformers",
            "neo4j"
        ]
        
        for package in required_packages:
            try:
                module = __import__(package.replace("-", "_"))
                version = getattr(module, "__version__", "unknown")
                dependencies[package] = {
                    "installed": True,
                    "version": version
                }
            except ImportError:
                dependencies[package] = {
                    "installed": False,
                    "version": None
                }
                
        # Check system dependencies
        system_deps = ["chromium", "chrome", "playwright"]
        for dep in system_deps:
            try:
                result = subprocess.run(
                    ["which", dep],
                    capture_output=True,
                    text=True
                )
                dependencies[dep] = {
                    "installed": result.returncode == 0,
                    "path": result.stdout.strip() if result.returncode == 0 else None
                }
            except:
                dependencies[dep] = {"installed": False}
                
        return dependencies
        
    def _get_config_diagnostics(self) -> Dict[str, Any]:
        """Get configuration diagnostics"""
        if not self.config:
            return {"available": False}
            
        return {
            "available": True,
            "environment": self.config.get("environment"),
            "debug": self.config.get("debug"),
            "features_enabled": {
                "job_system": self.config.get("features.enable_job_system"),
                "knowledge_graph": self.config.get("features.enable_knowledge_graph"),
                "monitoring": self.config.get("monitoring.enable_monitoring")
            },
            "limits": {
                "max_concurrent": self.config.get("crawler.max_concurrent"),
                "max_depth": self.config.get("crawler.max_depth")
            }
        }
        
    async def _check_network(self) -> Dict[str, Any]:
        """Check network connectivity"""
        endpoints = {
            "internet": "https://www.google.com",
            "supabase": self.config.get("storage.supabase_url") if self.config else None,
            "bge_service": self.config.get("storage.bge_service_url") if self.config else None
        }
        
        results = {}
        
        async with aiohttp.ClientSession() as session:
            for name, url in endpoints.items():
                if not url:
                    continue
                    
                try:
                    start_time = time.time()
                    async with session.get(
                        url,
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        results[name] = {
                            "reachable": True,
                            "status_code": response.status,
                            "response_time_ms": (time.time() - start_time) * 1000
                        }
                except Exception as e:
                    results[name] = {
                        "reachable": False,
                        "error": str(e)
                    }
                    
        return results
        
    async def _check_storage(self) -> Dict[str, Any]:
        """Check storage availability"""
        storage_info = {}
        
        # Check disk space
        disk = psutil.disk_usage('/')
        storage_info["disk"] = {
            "total_gb": disk.total / 1024**3,
            "used_gb": disk.used / 1024**3,
            "free_gb": disk.free / 1024**3,
            "percent_used": disk.percent
        }
        
        # Check temp directory
        temp_dir = Path("/tmp")
        if temp_dir.exists():
            temp_usage = psutil.disk_usage(str(temp_dir))
            storage_info["temp"] = {
                "free_gb": temp_usage.free / 1024**3,
                "writable": os.access(str(temp_dir), os.W_OK)
            }
            
        return storage_info


class MaintenanceTools:
    """System maintenance utilities"""
    
    def __init__(self, config_manager=None):
        self.config = config_manager
        
    async def cleanup_old_logs(self, days_to_keep: int = 7) -> Dict[str, Any]:
        """Clean up old log files"""
        log_dir = Path(self.config.get("monitoring.log_file", "")).parent
        if not log_dir.exists():
            return {"cleaned": 0, "error": "Log directory not found"}
            
        cleaned = 0
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        for log_file in log_dir.glob("*.log*"):
            try:
                mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                if mtime < cutoff_date:
                    log_file.unlink()
                    cleaned += 1
            except Exception as e:
                logger.error(f"Failed to clean {log_file}: {e}")
                
        return {"cleaned": cleaned, "cutoff_date": cutoff_date.isoformat()}
        
    async def vacuum_database(self) -> Dict[str, Any]:
        """Vacuum database to reclaim space"""
        try:
            from utils import get_supabase_client
            client = get_supabase_client()
            
            # Run vacuum on crawled_pages table
            # Note: This is a simplified example - actual implementation
            # would need proper database access
            
            return {
                "success": True,
                "message": "Database vacuum initiated"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
            
    async def reset_error_counters(self) -> Dict[str, Any]:
        """Reset error counters and circuit breakers"""
        # This would integrate with the monitoring system
        return {
            "reset": True,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    async def export_metrics(self, output_file: str) -> Dict[str, Any]:
        """Export performance metrics to file"""
        try:
            # Collect all metrics
            metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "system": {
                    "uptime_hours": (time.time() - psutil.boot_time()) / 3600,
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_percent": psutil.virtual_memory().percent
                }
            }
            
            # Write to file
            with open(output_file, 'w') as f:
                json.dump(metrics, f, indent=2)
                
            return {
                "success": True,
                "file": output_file,
                "size_kb": Path(output_file).stat().st_size / 1024
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }