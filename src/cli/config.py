#!/usr/bin/env python3
"""Configuration management for Integrated Research CLI Assistant."""

import os
import json
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict


@dataclass
class ResearchConfig:
    """Configuration settings for the integrated Research CLI."""
    
    # MCP Server Settings (auto-detected in integrated mode)
    server_path: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    debug: bool = False
    
    # Display Settings
    show_progress: bool = True
    color_output: bool = True
    max_results: int = 10
    
    # Content Settings
    chunk_size: int = 5000
    max_depth: int = 3
    max_concurrent: int = 10
    
    # Integrated Mode Settings
    integrated_mode: bool = True
    auto_detect_server: bool = True


class IntegratedConfigManager:
    """Manages configuration for the integrated Research CLI."""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize configuration manager for integrated mode.
        
        Args:
            config_dir: Directory to store config files (defaults to ~/.crawl4ai-research)
        """
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            self.config_dir = Path.home() / ".crawl4ai-research"
        
        self.config_file = self.config_dir / "config.json"
        self.config_dir.mkdir(exist_ok=True)
        
        self._config = ResearchConfig()
        self._load_config()
        
        # Auto-detect server path in integrated mode
        if self._config.auto_detect_server:
            self._config.server_path = self._find_integrated_server()
    
    def _find_integrated_server(self) -> Optional[str]:
        """Find the integrated server path automatically."""
        try:
            # Get current file's directory and look for the server
            current_dir = Path(__file__).parent
            
            # Look for server in parent src directory (integrated mode)
            server_candidates = [
                current_dir.parent / "crawl4ai_mcp.py",  # ../crawl4ai_mcp.py
                current_dir.parent.parent / "src" / "crawl4ai_mcp.py",  # ../../src/crawl4ai_mcp.py
                current_dir.parent.parent / "crawl4ai_mcp.py",  # ../../crawl4ai_mcp.py
            ]
            
            for candidate in server_candidates:
                if candidate.exists():
                    return str(candidate.resolve())
            
            return None
        except Exception:
            return None
    
    def _load_config(self) -> None:
        """Load configuration from file."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                
                # Update config with loaded values
                for key, value in data.items():
                    if hasattr(self._config, key):
                        setattr(self._config, key, value)
                        
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load config file: {e}")
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(asdict(self._config), f, indent=2)
        except IOError as e:
            print(f"Warning: Could not save config file: {e}")
    
    def get_config(self) -> ResearchConfig:
        """Get current configuration."""
        return self._config
    
    def update_config(self, **kwargs) -> None:
        """Update configuration values."""
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
        self.save_config()
    
    def reset_config(self) -> None:
        """Reset configuration to defaults."""
        self._config = ResearchConfig()
        # Re-detect server path
        if self._config.auto_detect_server:
            self._config.server_path = self._find_integrated_server()
        self.save_config()
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """
        Validate current configuration.
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Check server path in integrated mode
        if self._config.integrated_mode:
            if not self._config.server_path:
                detected_path = self._find_integrated_server()
                if detected_path:
                    self._config.server_path = detected_path
                    self.save_config()
                else:
                    errors.append("Integrated server not found in expected locations")
            elif not Path(self._config.server_path).exists():
                errors.append(f"Server path does not exist: {self._config.server_path}")
        
        # Check numeric values
        if self._config.timeout <= 0:
            errors.append("Timeout must be positive")
        
        if self._config.max_retries < 0:
            errors.append("Max retries cannot be negative")
        
        if self._config.max_results <= 0:
            errors.append("Max results must be positive")
        
        if self._config.chunk_size <= 0:
            errors.append("Chunk size must be positive")
        
        if self._config.max_depth <= 0:
            errors.append("Max depth must be positive")
        
        if self._config.max_concurrent <= 0:
            errors.append("Max concurrent must be positive")
        
        return len(errors) == 0, errors
    
    def setup_environment(self) -> Dict[str, str]:
        """Setup environment variables for the integrated research CLI."""
        env = os.environ.copy()
        
        # Ensure stdio transport for MCP server
        env["TRANSPORT"] = ""
        
        # Python path for imports (integrated mode)
        if self._config.server_path:
            server_dir = Path(self._config.server_path).parent
            current_pythonpath = env.get("PYTHONPATH", "")
            if current_pythonpath:
                env["PYTHONPATH"] = f"{server_dir}:{current_pythonpath}"
            else:
                env["PYTHONPATH"] = str(server_dir)
        
        return env
    
    def get_config_info(self) -> Dict[str, Any]:
        """Get configuration information for display."""
        return {
            "config_file": str(self.config_file),
            "config_dir": str(self.config_dir),
            "config": asdict(self._config),
            "server_found": bool(self._config.server_path and Path(self._config.server_path).exists()),
            "integrated_mode": True
        }


# Global configuration manager instance for integrated mode
config_manager = IntegratedConfigManager()


def get_config() -> ResearchConfig:
    """Get the global configuration."""
    return config_manager.get_config()


def update_config(**kwargs) -> None:
    """Update the global configuration."""
    config_manager.update_config(**kwargs)


def find_server() -> Optional[str]:
    """Find the integrated server path."""
    return config_manager._find_integrated_server()


def validate_setup() -> tuple[bool, list[str]]:
    """Validate the current integrated setup."""
    return config_manager.validate_config()


if __name__ == "__main__":
    # Configuration CLI for setup and management
    import sys
    
    if len(sys.argv) == 1:
        # Show current configuration
        info = config_manager.get_config_info()
        print("Integrated Research CLI Configuration:")
        print(f"  Config file: {info['config_file']}")
        print(f"  Integrated mode: {info['integrated_mode']}")
        print(f"  Server found: {info['server_found']}")
        print(f"  Server path: {info['config']['server_path'] or 'Auto-detecting...'}")
        print(f"  Debug mode: {info['config']['debug']}")
        print(f"  Timeout: {info['config']['timeout']}s")
        
    elif sys.argv[1] == "setup":
        # Auto-setup for integrated mode
        print("Setting up Integrated Research CLI...")
        server_path = config_manager._find_integrated_server()
        
        if server_path:
            print(f"✅ Found integrated MCP server: {server_path}")
        else:
            print("❌ Could not find integrated MCP server")
            print("Expected locations:")
            print("  • ../crawl4ai_mcp.py")
            print("  • ../../src/crawl4ai_mcp.py")
        
        is_valid, errors = config_manager.validate_config()
        if is_valid:
            print("✅ Configuration is valid")
        else:
            print("❌ Configuration errors:")
            for error in errors:
                print(f"  • {error}")
    
    elif sys.argv[1] == "reset":
        # Reset configuration
        config_manager.reset_config()
        print("Configuration reset to defaults with integrated mode")
    
    else:
        print("Usage: python config.py [setup|reset]")