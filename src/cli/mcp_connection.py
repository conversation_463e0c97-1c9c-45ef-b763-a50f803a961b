#!/usr/bin/env python3
"""MCP Client Connection Manager for RAG Server Integration."""

import asyncio
import json
import os
import subprocess
import sys
from typing import Dict, Any, Optional, List
from pathlib import Path

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


class MCPConnectionError(Exception):
    """Exception raised for MCP connection issues."""
    pass


class MCPConnectionManager:
    """Manages connection to the RAG MCP server via stdio."""
    
    def __init__(
        self, 
        server_path: Optional[str] = None,
        max_retries: int = 3,
        timeout: int = 30,
        debug: bool = False
    ):
        """
        Initialize MCP connection manager.
        
        Args:
            server_path: Path to the MCP server script (auto-detected if None)
            max_retries: Maximum retry attempts for operations
            timeout: Timeout for operations in seconds
            debug: Enable debug logging
        """
        # Auto-detect server path if not provided (integrated mode)
        if server_path is None:
            server_path = self._find_integrated_server()
        
        self.server_path = Path(server_path).resolve()
        self.max_retries = max_retries
        self.timeout = timeout
        self.debug = debug
        
        self.session: Optional[ClientSession] = None
        self._is_connected = False
        
        # Validate server path exists
        if not self.server_path.exists():
            raise MCPConnectionError(f"Server script not found: {self.server_path}")
    
    def _find_integrated_server(self) -> str:
        """Find the integrated server path."""
        # Get current file's directory and look for the server
        current_dir = Path(__file__).parent
        
        # Look for server in parent src directory (integrated mode)
        server_candidates = [
            current_dir.parent / "crawl4ai_mcp.py",  # ../crawl4ai_mcp.py
            current_dir.parent.parent / "src" / "crawl4ai_mcp.py",  # ../../src/crawl4ai_mcp.py
        ]
        
        for candidate in server_candidates:
            if candidate.exists():
                return str(candidate)
        
        raise MCPConnectionError("Could not find integrated MCP server")
    
    def _python_available(self, python_cmd: str) -> bool:
        """Check if a Python command is available."""
        try:
            subprocess.run([python_cmd, "--version"], 
                         capture_output=True, check=True, timeout=5)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    async def connect(self) -> None:
        """Establish connection to the MCP server."""
        if self._is_connected:
            return
            
        try:
            if self.debug:
                console.print(f"[dim]Connecting to integrated MCP server: {self.server_path}[/dim]")
            
            # Set up server parameters for stdio communication
            # Try python3 first, fallback to python
            python_cmd = "python3" if self._python_available("python3") else "python"
            
            # Create environment with explicit transport override
            env = os.environ.copy()
            env["TRANSPORT"] = "stdio"  # Explicitly force stdio mode
            env["PYTHONPATH"] = str(self.server_path.parent)
            
            # Debug logging
            if self.debug:
                console.print(f"[dim]Setting TRANSPORT=stdio for server process[/dim]")
            
            server_params = StdioServerParameters(
                command=python_cmd,
                args=[str(self.server_path)],
                env=env
            )
            
            # Connect using stdio client
            self._stdio_context = stdio_client(server_params)
            read, write = await self._stdio_context.__aenter__()
            
            # Create client session
            self.session = ClientSession(read, write)
            await self.session.__aenter__()
            
            # Initialize the connection
            await self.session.initialize()
            
            # Test connection with health check
            await self._health_check()
            
            self._is_connected = True
            
            if self.debug:
                console.print("[dim green]✓ Connected to integrated MCP server[/dim green]")
                
        except Exception as e:
            if self.debug:
                console.print(f"[dim red]✗ Connection failed: {e}[/dim red]")
            raise MCPConnectionError(f"Failed to connect to MCP server: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from the MCP server."""
        if not self._is_connected:
            return
            
        try:
            if self.session:
                await self.session.__aexit__(None, None, None)
                
            if hasattr(self, '_stdio_context'):
                await self._stdio_context.__aexit__(None, None, None)
                
            self._is_connected = False
            self.session = None
            
            if self.debug:
                console.print("[dim]Disconnected from MCP server[/dim]")
                
        except Exception as e:
            if self.debug:
                console.print(f"[dim red]Disconnect error: {e}[/dim red]")
    
    async def _health_check(self) -> None:
        """Perform health check on the server."""
        try:
            result = await self.call_tool("health_check")
            health_data = json.loads(result)
            
            if health_data.get("status") != "healthy":
                raise MCPConnectionError("Server health check failed")
                
        except json.JSONDecodeError:
            raise MCPConnectionError("Invalid health check response")
        except Exception as e:
            raise MCPConnectionError(f"Health check failed: {e}")
    
    async def call_tool(
        self, 
        tool_name: str, 
        show_progress: bool = False,
        **kwargs
    ) -> str:
        """
        Call a tool on the MCP server with robust error handling.
        
        Args:
            tool_name: Name of the tool to call
            show_progress: Show progress indicator during call
            **kwargs: Tool arguments
            
        Returns:
            Tool result as string
            
        Raises:
            MCPConnectionError: If tool call fails
        """
        if not self._is_connected or not self.session:
            await self.connect()
        
        for attempt in range(self.max_retries):
            try:
                if show_progress and attempt == 0:
                    with Progress(
                        SpinnerColumn(),
                        TextColumn(f"[blue]Calling {tool_name}...[/blue]"),
                        console=console,
                        transient=True
                    ) as progress:
                        progress.add_task("calling_tool", total=None)
                        result = await self._execute_tool_call(tool_name, kwargs)
                else:
                    result = await self._execute_tool_call(tool_name, kwargs)
                
                if self.debug:
                    console.print(f"[dim green]✓ Tool call succeeded: {tool_name}[/dim green]")
                
                return result
                
            except Exception as e:
                if self.debug:
                    console.print(f"[dim red]✗ Tool call failed (attempt {attempt + 1}): {e}[/dim red]")
                
                if attempt == self.max_retries - 1:
                    raise MCPConnectionError(f"Tool call failed after {self.max_retries} attempts: {e}")
                
                # Exponential backoff
                await asyncio.sleep(2 ** attempt)
                
                # Try to reconnect on failure
                try:
                    await self.disconnect()
                    await self.connect()
                except Exception as reconnect_error:
                    if self.debug:
                        console.print(f"[dim red]Reconnection failed: {reconnect_error}[/dim red]")
        
        raise MCPConnectionError(f"Tool call failed: {tool_name}")
    
    async def _execute_tool_call(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Execute the actual tool call with timeout."""
        if not self.session:
            raise MCPConnectionError("No active session")
        
        try:
            # Call the tool with timeout
            result = await asyncio.wait_for(
                self.session.call_tool(tool_name, arguments),
                timeout=self.timeout
            )
            
            # Extract content from result - MCP returns CallToolResult
            if hasattr(result, 'content') and result.content:
                if isinstance(result.content, list) and len(result.content) > 0:
                    first_content = result.content[0]
                    if hasattr(first_content, 'text'):
                        return first_content.text
                    elif hasattr(first_content, 'content'):
                        return first_content.content
                return str(result.content[0])
            
            return str(result)
            
        except asyncio.TimeoutError:
            raise MCPConnectionError(f"Tool call timed out after {self.timeout}s")
        except Exception as e:
            raise MCPConnectionError(f"Tool execution failed: {e}")
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools from the server."""
        if not self._is_connected or not self.session:
            await self.connect()
        
        try:
            tools = await self.session.list_tools()
            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema.model_dump() if tool.inputSchema else None
                }
                for tool in tools.tools
            ]
        except Exception as e:
            raise MCPConnectionError(f"Failed to list tools: {e}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
    
    @property
    def is_connected(self) -> bool:
        """Check if connection is active."""
        return self._is_connected
    
    def get_server_info(self) -> Dict[str, Any]:
        """Get information about the server configuration."""
        return {
            "server_path": str(self.server_path),
            "max_retries": self.max_retries,
            "timeout": self.timeout,
            "debug": self.debug,
            "connected": self._is_connected,
            "integrated_mode": True
        }


# Convenience function for quick tool calls
async def quick_tool_call(
    tool_name: str,
    debug: bool = False,
    **kwargs
) -> str:
    """
    Quick one-off tool call without managing connection lifecycle.
    Uses integrated server auto-detection.
    
    Args:
        tool_name: Name of the tool to call
        debug: Enable debug output
        **kwargs: Tool arguments
        
    Returns:
        Tool result as string
    """
    async with MCPConnectionManager(debug=debug) as manager:
        return await manager.call_tool(tool_name, **kwargs)


# Example usage and testing
if __name__ == "__main__":
    async def test_connection():
        """Test the integrated MCP connection manager."""
        try:
            async with MCPConnectionManager(debug=True) as manager:
                # Test health check
                health = await manager.call_tool("health_check")
                console.print(f"[green]Health check: {health}[/green]")
                
                # List available tools
                tools = await manager.list_tools()
                console.print(f"[blue]Available tools: {len(tools)}[/blue]")
                for tool in tools:
                    console.print(f"  - {tool['name']}: {tool['description']}")
        
        except MCPConnectionError as e:
            console.print(f"[red]Connection error: {e}[/red]")
        except Exception as e:
            console.print(f"[red]Unexpected error: {e}[/red]")
    
    # Run test
    import asyncio
    asyncio.run(test_connection())