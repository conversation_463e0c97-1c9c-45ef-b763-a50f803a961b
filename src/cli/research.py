#!/usr/bin/env python3
"""Integrated Research CLI Assistant with RAG MCP Server."""

import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any, List

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.syntax import Syntax
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.text import Text

# Local imports for integrated mode
from .mcp_connection import MCPConnectionManager, MCPConnectionError
from .config import config_manager

console = Console()


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug output')
@click.option('--timeout', default=30, help='Request timeout in seconds')
@click.pass_context
def research(ctx, debug: bool, timeout: int):
    """
    Integrated Research CLI Assistant with RAG capabilities.
    
    Provides intelligent web crawling, RAG queries, and knowledge graph exploration
    through direct integration with the crawl4ai-mcp server.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Configure settings
    config_updates = {}
    if debug:
        config_updates['debug'] = debug
    if timeout != 30:
        config_updates['timeout'] = timeout
    
    if config_updates:
        config_manager.update_config(**config_updates)
    
    # Store config manager in context for subcommands
    ctx.obj['config_manager'] = config_manager


@research.command()
@click.pass_context
async def info(ctx):
    """Show integrated server information and available tools."""
    config_mgr = ctx.obj['config_manager']
    config = config_mgr.get_config()
    
    # Validate configuration
    is_valid, errors = config_mgr.validate_config()
    if not is_valid:
        for error in errors:
            console.print(f"[red]❌ {error}[/red]")
        raise click.Abort()
    
    try:
        async with MCPConnectionManager(debug=config.debug, timeout=config.timeout) as manager:
            
            # Get server info
            server_info = manager.get_server_info()
            
            # Create info panel
            info_text = f"""[bold blue]Integrated RAG MCP Server Information[/bold blue]

[bold]Server Path:[/bold] {server_info['server_path']}
[bold]Integrated Mode:[/bold] {server_info['integrated_mode']}
[bold]Connected:[/bold] {server_info['connected']}
[bold]Timeout:[/bold] {server_info['timeout']}s
[bold]Max Retries:[/bold] {server_info['max_retries']}
[bold]Debug Mode:[/bold] {server_info['debug']}"""
            
            console.print(Panel(info_text, style="blue"))
            
            # List available tools
            tools = await manager.list_tools()
            
            table = Table(title="Available Tools", show_header=True, header_style="bold magenta")
            table.add_column("Tool Name", style="cyan")
            table.add_column("Description", style="green")
            
            for tool in tools:
                description = tool['description'] or "No description"
                # Truncate long descriptions
                if len(description) > 80:
                    description = description[:77] + "..."
                table.add_row(tool['name'], description)
            
            console.print(table)
            
    except MCPConnectionError as e:
        console.print(f"[red]❌ Connection Error: {e}[/red]")
        raise click.Abort()
    except Exception as e:
        console.print(f"[red]❌ Unexpected Error: {e}[/red]")
        if config.debug:
            console.print_exception()
        raise click.Abort()


@research.command()
@click.argument('url')
@click.option('--depth', default=3, help='Maximum crawl depth')
@click.option('--concurrent', default=10, help='Maximum concurrent requests')
@click.option('--chunk-size', default=5000, help='Chunk size for content processing')
@click.pass_context
async def crawl(ctx, url: str, depth: int, concurrent: int, chunk_size: int):
    """
    Intelligently crawl a URL with automatic detection.
    
    Supports sitemaps, text files, and regular web pages with recursive crawling.
    """
    config_mgr = ctx.obj['config_manager']
    config = config_mgr.get_config()
    
    # Validate configuration
    is_valid, errors = config_mgr.validate_config()
    if not is_valid:
        for error in errors:
            console.print(f"[red]❌ {error}[/red]")
        raise click.Abort()
    
    try:
        console.print(f"[blue]🕷️  Starting intelligent crawl of:[/blue] {url}")
        console.print(f"[dim]Max depth: {depth}, Concurrent: {concurrent}, Chunk size: {chunk_size}[/dim]")
        
        async with MCPConnectionManager(debug=config.debug, timeout=config.timeout * 3) as manager:
            
            result = await manager.call_tool(
                "smart_crawl_url",
                show_progress=True,
                url=url,
                max_depth=depth,
                max_concurrent=concurrent,
                chunk_size=chunk_size
            )
            
            # Parse and display results
            crawl_data = json.loads(result)
            
            # Summary panel
            summary = f"""[bold green]Crawl Complete[/bold green]

[bold]URL:[/bold] {url}
[bold]Pages Crawled:[/bold] {crawl_data.get('pages_crawled', 'N/A')}
[bold]Total Chunks:[/bold] {crawl_data.get('total_chunks', 'N/A')}
[bold]Errors:[/bold] {crawl_data.get('errors', 0)}
[bold]Status:[/bold] {crawl_data.get('status', 'Unknown')}"""
            
            console.print(Panel(summary, style="green"))
            
            # Show any errors or warnings
            if crawl_data.get('errors', 0) > 0 and crawl_data.get('error_details'):
                console.print("\n[yellow]⚠️  Errors encountered:[/yellow]")
                for error in crawl_data['error_details'][:5]:  # Show first 5 errors
                    console.print(f"  • {error}")
            
            console.print(f"\n[green]✅ Crawl data stored and ready for RAG queries[/green]")
            
    except MCPConnectionError as e:
        console.print(f"[red]❌ Connection Error: {e}[/red]")
        raise click.Abort()
    except json.JSONDecodeError:
        console.print(f"[red]❌ Invalid response from server[/red]")
        raise click.Abort()
    except Exception as e:
        console.print(f"[red]❌ Crawl failed: {e}[/red]")
        if config.debug:
            console.print_exception()
        raise click.Abort()


@research.command()
@click.argument('query')
@click.option('--source', help='Filter by source domain (e.g., example.com)')
@click.option('--count', default=5, help='Maximum number of results')
@click.pass_context
async def rag(ctx, query: str, source: Optional[str], count: int):
    """
    Perform RAG (Retrieval Augmented Generation) query on crawled content.
    
    Searches the vector database for content relevant to your query.
    Use 'research sources' to see available sources for filtering.
    """
    config_mgr = ctx.obj['config_manager']
    config = config_mgr.get_config()
    
    # Validate configuration
    is_valid, errors = config_mgr.validate_config()
    if not is_valid:
        for error in errors:
            console.print(f"[red]❌ {error}[/red]")
        raise click.Abort()
    
    try:
        console.print(f"[blue]🔍 Searching for:[/blue] {query}")
        if source:
            console.print(f"[dim]Filtering by source: {source}[/dim]")
        
        async with MCPConnectionManager(debug=config.debug, timeout=config.timeout) as manager:
            
            result = await manager.call_tool(
                "perform_rag_query",
                show_progress=True,
                query=query,
                source=source,
                match_count=count
            )
            
            # Parse results
            rag_data = json.loads(result)
            
            if not rag_data.get('matches'):
                console.print("[yellow]⚠️  No matches found for your query[/yellow]")
                console.print("Try:\n• Broadening your search terms\n• Using 'research sources' to see available content\n• Crawling more sources first")
                return
            
            # Display results
            console.print(f"\n[bold green]Found {len(rag_data['matches'])} matches:[/bold green]\n")
            
            for i, match in enumerate(rag_data['matches'], 1):
                # Create result panel
                content = match.get('content', 'No content')
                if len(content) > 400:
                    content = content[:397] + "..."
                
                result_text = f"""[bold]Source:[/bold] {match.get('source', 'Unknown')}
[bold]Relevance Score:[/bold] {match.get('score', 0):.3f}

{content}"""
                
                panel = Panel(
                    result_text,
                    title=f"Result {i}",
                    border_style="blue" if i == 1 else "dim",
                    padding=(0, 1)
                )
                console.print(panel)
                console.print()
            
    except MCPConnectionError as e:
        console.print(f"[red]❌ Connection Error: {e}[/red]")
        raise click.Abort()
    except json.JSONDecodeError:
        console.print(f"[red]❌ Invalid response from server[/red]")
        raise click.Abort()
    except Exception as e:
        console.print(f"[red]❌ Query failed: {e}[/red]")
        if config.debug:
            console.print_exception()
        raise click.Abort()


@research.command()
@click.pass_context
async def sources(ctx):
    """List available sources for RAG queries."""
    config_mgr = ctx.obj['config_manager']
    config = config_mgr.get_config()
    
    # Validate configuration
    is_valid, errors = config_mgr.validate_config()
    if not is_valid:
        for error in errors:
            console.print(f"[red]❌ {error}[/red]")
        raise click.Abort()
    
    try:
        async with MCPConnectionManager(debug=config.debug, timeout=config.timeout) as manager:
            
            result = await manager.call_tool("get_available_sources", show_progress=True)
            sources_data = json.loads(result)
            
            if not sources_data.get('sources'):
                console.print("[yellow]⚠️  No sources available[/yellow]")
                console.print("Use 'research crawl <url>' to add content first")
                return
            
            # Create sources table
            table = Table(title="Available Sources for RAG Queries", show_header=True, header_style="bold magenta")
            table.add_column("Domain", style="cyan", width=30)
            table.add_column("Documents", justify="right", style="green", width=10)
            table.add_column("Summary", style="white", width=50)
            
            for source in sources_data['sources']:
                summary = source.get('summary', 'No summary')
                if len(summary) > 47:
                    summary = summary[:44] + "..."
                
                table.add_row(
                    source.get('source', 'Unknown'),
                    str(source.get('document_count', 0)),
                    summary
                )
            
            console.print(table)
            console.print(f"\n[dim]💡 Use --source flag with 'research rag' to filter by domain[/dim]")
            
    except MCPConnectionError as e:
        console.print(f"[red]❌ Connection Error: {e}[/red]")
        raise click.Abort()
    except json.JSONDecodeError:
        console.print(f"[red]❌ Invalid response from server[/red]")
        raise click.Abort()
    except Exception as e:
        console.print(f"[red]❌ Failed to get sources: {e}[/red]")
        if config.debug:
            console.print_exception()
        raise click.Abort()


# Async CLI adapter
def async_command(f):
    """Decorator to run async click commands."""
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


# Apply async decorator to commands
for command in [info, crawl, rag, sources]:
    command.callback = async_command(command.callback)


def main():
    """Main entry point for the integrated research CLI."""
    try:
        research()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[red]Unexpected error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()