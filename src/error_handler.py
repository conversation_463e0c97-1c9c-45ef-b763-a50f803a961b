"""
Advanced Error Handling and Recovery System for MCP Crawl4AI

This module provides comprehensive error handling, classification, recovery strategies,
and graceful degradation for the crawl system.
"""

import asyncio
import logging
import traceback
from typing import Dict, Any, Optional, List, Callable, Type
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import re

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels for classification"""
    LOW = "low"           # Can be ignored or logged
    MEDIUM = "medium"     # Should be handled but not critical
    HIGH = "high"         # Requires immediate attention
    CRITICAL = "critical" # System-threatening, requires immediate action


class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    PARSING = "parsing"
    STORAGE = "storage"
    VALIDATION = "validation"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"
    EXTERNAL_SERVICE = "external_service"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Context information for an error"""
    error_type: Type[Exception]
    error_message: str
    stack_trace: str
    timestamp: datetime
    url: Optional[str] = None
    job_id: Optional[str] = None
    worker_id: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0
    

@dataclass
class ErrorClassification:
    """Error classification result"""
    category: ErrorCategory
    severity: ErrorSeverity
    is_retryable: bool
    retry_delay: Optional[float] = None
    recovery_strategy: Optional[str] = None
    user_message: str = ""
    log_message: str = ""


class ErrorClassifier:
    """Classifies errors and determines appropriate handling strategies"""
    
    def __init__(self):
        # Error patterns for classification
        self.patterns = {
            ErrorCategory.NETWORK: [
                (r"timeout", ErrorSeverity.MEDIUM),
                (r"connection.*refused", ErrorSeverity.HIGH),
                (r"dns.*fail", ErrorSeverity.HIGH),
                (r"ssl.*error", ErrorSeverity.HIGH),
                (r"network.*unreachable", ErrorSeverity.HIGH),
                (r"404.*not found", ErrorSeverity.LOW),
                (r"403.*forbidden", ErrorSeverity.MEDIUM),
                (r"429.*too many requests", ErrorSeverity.MEDIUM),
                (r"5\d{2}", ErrorSeverity.HIGH),  # 5xx errors
            ],
            ErrorCategory.PARSING: [
                (r"parse.*error", ErrorSeverity.MEDIUM),
                (r"invalid.*xml", ErrorSeverity.MEDIUM),
                (r"encoding.*error", ErrorSeverity.MEDIUM),
                (r"malformed.*content", ErrorSeverity.LOW),
            ],
            ErrorCategory.STORAGE: [
                (r"database.*error", ErrorSeverity.CRITICAL),
                (r"supabase.*error", ErrorSeverity.HIGH),
                (r"disk.*full", ErrorSeverity.CRITICAL),
                (r"permission.*denied", ErrorSeverity.HIGH),
            ],
            ErrorCategory.RESOURCE: [
                (r"memory.*error", ErrorSeverity.CRITICAL),
                (r"out of memory", ErrorSeverity.CRITICAL),
                (r"too many.*files", ErrorSeverity.HIGH),
                (r"resource.*exhausted", ErrorSeverity.HIGH),
            ],
            ErrorCategory.CONFIGURATION: [
                (r"missing.*config", ErrorSeverity.CRITICAL),
                (r"invalid.*configuration", ErrorSeverity.CRITICAL),
                (r"environment.*variable", ErrorSeverity.HIGH),
            ],
        }
        
        # Retry strategies
        self.retry_strategies = {
            ErrorCategory.NETWORK: {
                "max_retries": 3,
                "base_delay": 2.0,
                "max_delay": 60.0,
                "exponential": True
            },
            ErrorCategory.PARSING: {
                "max_retries": 1,
                "base_delay": 1.0,
                "max_delay": 5.0,
                "exponential": False
            },
            ErrorCategory.STORAGE: {
                "max_retries": 2,
                "base_delay": 5.0,
                "max_delay": 30.0,
                "exponential": True
            },
            ErrorCategory.EXTERNAL_SERVICE: {
                "max_retries": 3,
                "base_delay": 10.0,
                "max_delay": 120.0,
                "exponential": True
            }
        }
        
    def classify(self, context: ErrorContext) -> ErrorClassification:
        """Classify an error and determine handling strategy"""
        error_str = f"{context.error_message} {context.stack_trace}".lower()
        
        # Determine category and severity
        category = ErrorCategory.UNKNOWN
        severity = ErrorSeverity.MEDIUM
        
        for cat, patterns in self.patterns.items():
            for pattern, sev in patterns:
                if re.search(pattern, error_str):
                    category = cat
                    severity = sev
                    break
            if category != ErrorCategory.UNKNOWN:
                break
                
        # Special case handling
        if "429" in error_str or "rate limit" in error_str:
            category = ErrorCategory.EXTERNAL_SERVICE
            severity = ErrorSeverity.MEDIUM
            
        # Determine if retryable
        is_retryable = category in [
            ErrorCategory.NETWORK,
            ErrorCategory.EXTERNAL_SERVICE,
            ErrorCategory.PARSING
        ] and context.retry_count < self._get_max_retries(category)
        
        # Calculate retry delay
        retry_delay = None
        if is_retryable:
            retry_delay = self._calculate_retry_delay(category, context.retry_count)
            
        # Generate messages
        user_message = self._generate_user_message(category, severity, context)
        log_message = self._generate_log_message(category, severity, context)
        
        # Determine recovery strategy
        recovery_strategy = self._determine_recovery_strategy(category, severity)
        
        return ErrorClassification(
            category=category,
            severity=severity,
            is_retryable=is_retryable,
            retry_delay=retry_delay,
            recovery_strategy=recovery_strategy,
            user_message=user_message,
            log_message=log_message
        )
        
    def _get_max_retries(self, category: ErrorCategory) -> int:
        """Get maximum retries for error category"""
        strategy = self.retry_strategies.get(category, {})
        return strategy.get("max_retries", 0)
        
    def _calculate_retry_delay(self, category: ErrorCategory, retry_count: int) -> float:
        """Calculate retry delay based on strategy"""
        strategy = self.retry_strategies.get(category, {})
        base_delay = strategy.get("base_delay", 1.0)
        max_delay = strategy.get("max_delay", 60.0)
        exponential = strategy.get("exponential", False)
        
        if exponential:
            delay = min(base_delay * (2 ** retry_count), max_delay)
        else:
            delay = base_delay
            
        # Add jitter to prevent thundering herd
        import random
        jitter = random.uniform(0.8, 1.2)
        return delay * jitter
        
    def _generate_user_message(self, category: ErrorCategory, severity: ErrorSeverity, 
                              context: ErrorContext) -> str:
        """Generate user-friendly error message"""
        messages = {
            ErrorCategory.NETWORK: "Network connectivity issue encountered",
            ErrorCategory.PARSING: "Unable to parse content from the URL",
            ErrorCategory.STORAGE: "Storage operation failed",
            ErrorCategory.RESOURCE: "System resource limitation encountered",
            ErrorCategory.CONFIGURATION: "Configuration issue detected",
            ErrorCategory.EXTERNAL_SERVICE: "External service is temporarily unavailable",
            ErrorCategory.UNKNOWN: "An unexpected error occurred"
        }
        
        base_message = messages.get(category, "An error occurred")
        
        if context.url:
            base_message += f" while processing {context.url}"
            
        if severity == ErrorSeverity.CRITICAL:
            base_message = "CRITICAL: " + base_message
            
        return base_message
        
    def _generate_log_message(self, category: ErrorCategory, severity: ErrorSeverity,
                             context: ErrorContext) -> str:
        """Generate detailed log message"""
        parts = [
            f"[{severity.value.upper()}]",
            f"Category: {category.value}",
            f"Error: {context.error_type.__name__}",
            f"Message: {context.error_message}"
        ]
        
        if context.job_id:
            parts.append(f"Job: {context.job_id}")
        if context.worker_id:
            parts.append(f"Worker: {context.worker_id}")
        if context.url:
            parts.append(f"URL: {context.url}")
        if context.retry_count > 0:
            parts.append(f"Retry: {context.retry_count}")
            
        return " | ".join(parts)
        
    def _determine_recovery_strategy(self, category: ErrorCategory, 
                                   severity: ErrorSeverity) -> str:
        """Determine appropriate recovery strategy"""
        if severity == ErrorSeverity.CRITICAL:
            return "immediate_alert"
        elif category == ErrorCategory.NETWORK:
            return "retry_with_backoff"
        elif category == ErrorCategory.EXTERNAL_SERVICE:
            return "circuit_breaker"
        elif category == ErrorCategory.STORAGE:
            return "fallback_storage"
        elif category == ErrorCategory.RESOURCE:
            return "resource_throttling"
        else:
            return "log_and_continue"


class ErrorHandler:
    """Central error handling system with recovery strategies"""
    
    def __init__(self, 
                 alert_callback: Optional[Callable] = None,
                 metrics_collector: Optional[Any] = None):
        self.classifier = ErrorClassifier()
        self.alert_callback = alert_callback
        self.metrics_collector = metrics_collector
        
        # Error history for pattern detection
        self.error_history: List[ErrorContext] = []
        self.max_history = 100
        
        # Recovery handlers
        self.recovery_handlers: Dict[str, Callable] = {
            "retry_with_backoff": self._retry_with_backoff,
            "circuit_breaker": self._apply_circuit_breaker,
            "fallback_storage": self._use_fallback_storage,
            "resource_throttling": self._apply_resource_throttling,
            "immediate_alert": self._send_immediate_alert,
            "log_and_continue": self._log_and_continue
        }
        
    async def handle_error(self, 
                          error: Exception,
                          context_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Main error handling method"""
        # Create error context
        context = ErrorContext(
            error_type=type(error),
            error_message=str(error),
            stack_trace=traceback.format_exc(),
            timestamp=datetime.utcnow(),
            **(context_data or {})
        )
        
        # Add to history
        self._add_to_history(context)
        
        # Classify error
        classification = self.classifier.classify(context)
        
        # Log the error
        if classification.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            logger.error(classification.log_message)
        else:
            logger.warning(classification.log_message)
            
        # Record metrics
        if self.metrics_collector:
            await self._record_metrics(context, classification)
            
        # Apply recovery strategy
        recovery_result = await self._apply_recovery(context, classification)
        
        # Check for error patterns
        patterns = self._detect_error_patterns()
        if patterns:
            await self._handle_error_patterns(patterns)
            
        return {
            "handled": True,
            "classification": classification,
            "recovery_result": recovery_result,
            "patterns_detected": patterns
        }
        
    def _add_to_history(self, context: ErrorContext):
        """Add error to history with size limit"""
        self.error_history.append(context)
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
            
    async def _record_metrics(self, context: ErrorContext, 
                            classification: ErrorClassification):
        """Record error metrics"""
        if self.metrics_collector:
            await self.metrics_collector.record_error(
                category=classification.category.value,
                severity=classification.severity.value,
                url=context.url,
                job_id=context.job_id
            )
            
    async def _apply_recovery(self, context: ErrorContext,
                            classification: ErrorClassification) -> Dict[str, Any]:
        """Apply the appropriate recovery strategy"""
        handler = self.recovery_handlers.get(
            classification.recovery_strategy,
            self._log_and_continue
        )
        
        return await handler(context, classification)
        
    async def _retry_with_backoff(self, context: ErrorContext,
                                 classification: ErrorClassification) -> Dict[str, Any]:
        """Retry with exponential backoff"""
        return {
            "strategy": "retry_with_backoff",
            "should_retry": classification.is_retryable,
            "retry_delay": classification.retry_delay,
            "retry_count": context.retry_count + 1
        }
        
    async def _apply_circuit_breaker(self, context: ErrorContext,
                                    classification: ErrorClassification) -> Dict[str, Any]:
        """Apply circuit breaker pattern"""
        # This would integrate with the existing circuit breaker in monitoring.py
        return {
            "strategy": "circuit_breaker",
            "action": "open_circuit",
            "recovery_timeout": 60
        }
        
    async def _use_fallback_storage(self, context: ErrorContext,
                                   classification: ErrorClassification) -> Dict[str, Any]:
        """Use fallback storage mechanism"""
        # Could implement local file storage as fallback
        return {
            "strategy": "fallback_storage",
            "action": "queue_for_later",
            "fallback_location": "/tmp/crawl_fallback"
        }
        
    async def _apply_resource_throttling(self, context: ErrorContext,
                                        classification: ErrorClassification) -> Dict[str, Any]:
        """Apply resource throttling"""
        return {
            "strategy": "resource_throttling",
            "action": "reduce_concurrency",
            "new_limit": 5  # Reduce from default
        }
        
    async def _send_immediate_alert(self, context: ErrorContext,
                                  classification: ErrorClassification) -> Dict[str, Any]:
        """Send immediate alert for critical errors"""
        if self.alert_callback:
            await self.alert_callback({
                "type": "critical_error",
                "error": classification.user_message,
                "context": context,
                "timestamp": datetime.utcnow().isoformat()
            })
            
        return {
            "strategy": "immediate_alert",
            "alert_sent": True
        }
        
    async def _log_and_continue(self, context: ErrorContext,
                              classification: ErrorClassification) -> Dict[str, Any]:
        """Simple log and continue strategy"""
        return {
            "strategy": "log_and_continue",
            "logged": True
        }
        
    def _detect_error_patterns(self) -> List[Dict[str, Any]]:
        """Detect patterns in error history"""
        patterns = []
        
        if len(self.error_history) < 5:
            return patterns
            
        # Check for repeated errors
        recent_errors = self.error_history[-10:]
        error_types = [e.error_type for e in recent_errors]
        
        # Repeated same error type
        for error_type in set(error_types):
            count = error_types.count(error_type)
            if count >= 3:
                patterns.append({
                    "type": "repeated_error",
                    "error_type": error_type.__name__,
                    "count": count,
                    "severity": "high"
                })
                
        # Rapid error rate
        if len(recent_errors) >= 5:
            time_span = (recent_errors[-1].timestamp - recent_errors[-5].timestamp).total_seconds()
            if time_span < 60:  # 5 errors in less than a minute
                patterns.append({
                    "type": "rapid_errors",
                    "rate": len(recent_errors) / time_span * 60,
                    "severity": "critical"
                })
                
        return patterns
        
    async def _handle_error_patterns(self, patterns: List[Dict[str, Any]]):
        """Handle detected error patterns"""
        for pattern in patterns:
            if pattern["severity"] == "critical":
                logger.critical(f"Critical error pattern detected: {pattern}")
                if self.alert_callback:
                    await self.alert_callback({
                        "type": "error_pattern",
                        "pattern": pattern,
                        "timestamp": datetime.utcnow().isoformat()
                    })


class GracefulDegradation:
    """Manages graceful degradation strategies"""
    
    def __init__(self):
        self.degradation_levels = {
            "full": {
                "max_concurrent": 10,
                "max_depth": 3,
                "features": ["sitemap", "recursive", "code_extraction", "knowledge_graph"]
            },
            "reduced": {
                "max_concurrent": 5,
                "max_depth": 2,
                "features": ["sitemap", "recursive", "code_extraction"]
            },
            "minimal": {
                "max_concurrent": 2,
                "max_depth": 1,
                "features": ["single_page"]
            },
            "emergency": {
                "max_concurrent": 1,
                "max_depth": 0,
                "features": ["single_page"]
            }
        }
        
        self.current_level = "full"
        
    def degrade(self) -> Dict[str, Any]:
        """Move to next degradation level"""
        levels = list(self.degradation_levels.keys())
        current_index = levels.index(self.current_level)
        
        if current_index < len(levels) - 1:
            self.current_level = levels[current_index + 1]
            logger.warning(f"Degrading to level: {self.current_level}")
            
        return self.get_current_config()
        
    def restore(self) -> Dict[str, Any]:
        """Move to previous degradation level"""
        levels = list(self.degradation_levels.keys())
        current_index = levels.index(self.current_level)
        
        if current_index > 0:
            self.current_level = levels[current_index - 1]
            logger.info(f"Restoring to level: {self.current_level}")
            
        return self.get_current_config()
        
    def get_current_config(self) -> Dict[str, Any]:
        """Get current degradation configuration"""
        return {
            "level": self.current_level,
            **self.degradation_levels[self.current_level]
        }
        
    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled at current level"""
        config = self.degradation_levels[self.current_level]
        return feature in config["features"]