"""
Job Management System for MCP Crawl4AI RAG Server

This module provides job-based crawling capabilities using Supabase PGMQ
to eliminate MCP timeout issues. Jobs are queued and processed asynchronously
with real-time progress tracking and status updates.
"""

import asyncio
import json
import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import traceback

from supabase import Client
from utils import get_supabase_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JobStatus(Enum):
    """Job status enumeration matching database enum."""
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class JobType(Enum):
    """Job type enumeration matching database enum."""
    SINGLE_PAGE = "single_page"
    SMART_CRAWL = "smart_crawl"
    SITEMAP_CRAWL = "sitemap_crawl"
    TEXT_FILE_CRAWL = "text_file_crawl"
    REPOSITORY_PARSE = "repository_parse"

@dataclass
class JobInfo:
    """Data class representing job information."""
    id: str
    job_type: JobType
    status: JobStatus
    parameters: Dict[str, Any]
    progress_percent: int
    current_operation: Optional[str]
    total_operations: int
    completed_operations: int
    pages_crawled: int
    pages_processed: int
    chunks_created: int
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    result_summary: Dict[str, Any]
    priority: int

class JobManager:
    """
    Manages job creation, queuing, and status tracking using Supabase PGMQ.
    
    This class provides the interface between MCP tools and the background
    job processing system, ensuring immediate response times while handling
    long-running operations asynchronously.
    """
    
    def __init__(self, supabase_client: Optional[Client] = None):
        """Initialize job manager with Supabase client."""
        self.supabase = supabase_client or get_supabase_client()
        self.queue_name = "crawl_jobs_queue"
    
    async def create_job(
        self,
        job_type: JobType,
        parameters: Dict[str, Any],
        priority: int = 5
    ) -> str:
        """
        Create a new job and queue it for processing.
        
        Args:
            job_type: Type of job to create
            parameters: Job-specific parameters
            priority: Job priority (1-10, higher is more important)
            
        Returns:
            Job ID (UUID string)
            
        Raises:
            Exception: If job creation fails
        """
        try:
            job_id = str(uuid.uuid4())
            
            # Create job record
            job_data = {
                "id": job_id,
                "job_type": job_type.value,
                "status": JobStatus.QUEUED.value,
                "parameters": parameters,
                "priority": priority,
                "created_by": "mcp_tool"
            }
            
            result = self.supabase.table("crawl_jobs").insert(job_data).execute()
            
            if not result.data:
                raise Exception("Failed to create job record")
            
            # Queue the job using PGMQ
            message_data = {
                "job_id": job_id,
                "job_type": job_type.value,
                "parameters": parameters,
                "priority": priority
            }
            
            # Use raw SQL to send PGMQ message
            pgmq_result = self.supabase.rpc(
                "pgmq_send", 
                {
                    "queue_name": self.queue_name,
                    "msg": json.dumps(message_data),
                    "delay": 0
                }
            ).execute()
            
            if pgmq_result.data:
                # Update job with PGMQ message ID
                message_id = pgmq_result.data
                self.supabase.table("crawl_jobs").update({
                    "pgmq_message_id": message_id
                }).eq("id", job_id).execute()
            
            logger.info(f"Created job {job_id} of type {job_type.value} with priority {priority}")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to create job: {str(e)}")
            logger.error(traceback.format_exc())
            raise Exception(f"Job creation failed: {str(e)}")
    
    async def get_job_status(self, job_id: str) -> Optional[JobInfo]:
        """
        Get current job status and progress information.
        
        Args:
            job_id: Job ID to query
            
        Returns:
            JobInfo object or None if job not found
        """
        try:
            # Use the database function for optimized status retrieval
            result = self.supabase.rpc("get_job_status", {"p_job_id": job_id}).execute()
            
            if not result.data:
                return None
            
            job_data = result.data[0] if result.data else None
            if not job_data:
                return None
            
            return JobInfo(
                id=job_data["job_id"],
                job_type=JobType(job_data["job_type"]),
                status=JobStatus(job_data["status"]),
                parameters=job_data.get("parameters", {}),
                progress_percent=job_data.get("progress_percent", 0),
                current_operation=job_data.get("current_operation"),
                total_operations=job_data.get("total_operations", 0),
                completed_operations=job_data.get("completed_operations", 0),
                pages_crawled=job_data.get("pages_crawled", 0),
                pages_processed=job_data.get("pages_processed", 0),
                chunks_created=job_data.get("chunks_created", 0),
                created_at=datetime.fromisoformat(job_data["created_at"].replace("Z", "+00:00")),
                started_at=datetime.fromisoformat(job_data["started_at"].replace("Z", "+00:00")) if job_data.get("started_at") else None,
                completed_at=datetime.fromisoformat(job_data["completed_at"].replace("Z", "+00:00")) if job_data.get("completed_at") else None,
                error_message=job_data.get("error_message"),
                result_summary=job_data.get("result_summary", {}),
                priority=job_data.get("priority", 5)
            )
            
        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {str(e)}")
            return None
    
    async def get_job_results(self, job_id: str) -> Dict[str, Any]:
        """
        Get detailed results for a completed job.
        
        Args:
            job_id: Job ID to get results for
            
        Returns:
            Dictionary containing job results
        """
        try:
            # Get job status first
            job_info = await self.get_job_status(job_id)
            if not job_info:
                return {"error": "Job not found"}
            
            if job_info.status != JobStatus.COMPLETED:
                return {
                    "error": f"Job not completed (status: {job_info.status.value})",
                    "status": job_info.status.value,
                    "progress": job_info.progress_percent
                }
            
            # Get detailed results
            results = self.supabase.table("job_results").select("*").eq("job_id", job_id).execute()
            
            # Get job progress history
            progress = self.supabase.table("job_progress").select("*").eq("job_id", job_id).order("timestamp").execute()
            
            return {
                "job_id": job_id,
                "status": job_info.status.value,
                "job_type": job_info.job_type.value,
                "summary": job_info.result_summary,
                "pages_crawled": job_info.pages_crawled,
                "pages_processed": job_info.pages_processed,
                "chunks_created": job_info.chunks_created,
                "processing_time_minutes": self._calculate_processing_time(job_info),
                "detailed_results": results.data if results.data else [],
                "progress_history": progress.data if progress.data else [],
                "created_at": job_info.created_at.isoformat(),
                "completed_at": job_info.completed_at.isoformat() if job_info.completed_at else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get job results for {job_id}: {str(e)}")
            return {"error": f"Failed to retrieve results: {str(e)}"}
    
    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a queued or running job.
        
        Args:
            job_id: Job ID to cancel
            
        Returns:
            True if job was cancelled, False otherwise
        """
        try:
            result = self.supabase.rpc("cancel_job", {"p_job_id": job_id}).execute()
            success = result.data if result.data is not None else False
            
            if success:
                logger.info(f"Successfully cancelled job {job_id}")
            else:
                logger.warning(f"Failed to cancel job {job_id} (may already be completed)")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {str(e)}")
            return False
    
    async def list_jobs(
        self,
        status_filter: Optional[JobStatus] = None,
        job_type_filter: Optional[JobType] = None,
        limit: int = 20,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        List jobs with optional filtering.
        
        Args:
            status_filter: Filter by job status
            job_type_filter: Filter by job type
            limit: Maximum number of jobs to return
            offset: Number of jobs to skip
            
        Returns:
            Dictionary containing jobs list and metadata
        """
        try:
            # Build query
            query = self.supabase.table("job_status_summary").select("*")
            
            if status_filter:
                query = query.eq("status", status_filter.value)
            
            if job_type_filter:
                query = query.eq("job_type", job_type_filter.value)
            
            # Execute query with pagination
            result = query.order("created_at", desc=True).range(offset, offset + limit - 1).execute()
            
            # Get queue statistics
            stats_result = self.supabase.rpc("get_job_queue_stats").execute()
            stats = stats_result.data[0] if stats_result.data else {}
            
            return {
                "jobs": result.data if result.data else [],
                "total_count": len(result.data) if result.data else 0,
                "limit": limit,
                "offset": offset,
                "queue_stats": {
                    "total_jobs": stats.get("total_jobs", 0),
                    "queued_jobs": stats.get("queued_jobs", 0),
                    "running_jobs": stats.get("running_jobs", 0),
                    "completed_jobs": stats.get("completed_jobs", 0),
                    "failed_jobs": stats.get("failed_jobs", 0),
                    "cancelled_jobs": stats.get("cancelled_jobs", 0),
                    "avg_processing_time_minutes": float(stats.get("avg_processing_time_minutes", 0) or 0)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to list jobs: {str(e)}")
            return {
                "error": f"Failed to list jobs: {str(e)}",
                "jobs": [],
                "total_count": 0
            }
    
    async def update_job_progress(
        self,
        job_id: str,
        operation_name: str,
        progress_percent: int,
        message: Optional[str] = None,
        pages_processed: int = 0,
        processing_time_ms: int = 0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update job progress (used by background workers).
        
        Args:
            job_id: Job ID to update
            operation_name: Current operation name
            progress_percent: Progress percentage (0-100)
            message: Optional progress message
            pages_processed: Number of pages processed in this update
            processing_time_ms: Processing time for this operation
            metadata: Additional metadata
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            result = self.supabase.rpc("update_job_progress", {
                "p_job_id": job_id,
                "p_operation_name": operation_name,
                "p_progress_percent": progress_percent,
                "p_message": message,
                "p_pages_processed": pages_processed,
                "p_processing_time_ms": processing_time_ms,
                "p_metadata": metadata or {}
            }).execute()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update job progress for {job_id}: {str(e)}")
            return False
    
    async def mark_job_running(self, job_id: str) -> bool:
        """Mark job as running (used by background workers)."""
        try:
            result = self.supabase.table("crawl_jobs").update({
                "status": JobStatus.RUNNING.value,
                "started_at": datetime.now(timezone.utc).isoformat()
            }).eq("id", job_id).execute()
            
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Failed to mark job {job_id} as running: {str(e)}")
            return False
    
    async def complete_job(
        self,
        job_id: str,
        result_summary: Dict[str, Any],
        pages_crawled: int = 0,
        chunks_created: int = 0
    ) -> bool:
        """Complete job with results (used by background workers)."""
        try:
            result = self.supabase.rpc("complete_job", {
                "p_job_id": job_id,
                "p_result_summary": result_summary,
                "p_pages_crawled": pages_crawled,
                "p_chunks_created": chunks_created
            }).execute()
            
            logger.info(f"Completed job {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete job {job_id}: {str(e)}")
            return False
    
    async def fail_job(self, job_id: str, error_message: str) -> bool:
        """Mark job as failed (used by background workers)."""
        try:
            result = self.supabase.rpc("fail_job", {
                "p_job_id": job_id,
                "p_error_message": error_message
            }).execute()
            
            logger.error(f"Failed job {job_id}: {error_message}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to mark job {job_id} as failed: {str(e)}")
            return False
    
    def _calculate_processing_time(self, job_info: JobInfo) -> Optional[float]:
        """Calculate processing time in minutes."""
        if job_info.started_at and job_info.completed_at:
            delta = job_info.completed_at - job_info.started_at
            return delta.total_seconds() / 60.0
        return None

# Global job manager instance
_job_manager = None

def get_job_manager() -> JobManager:
    """Get global job manager instance."""
    global _job_manager
    if _job_manager is None:
        _job_manager = JobManager()
    return _job_manager