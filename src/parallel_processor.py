"""
Parallel processing and performance optimization module for MCP Crawl4AI RAG Server.
Implements efficient parallel crawling with resource management.
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Callable, Set, Tuple
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import logging
from urllib.parse import urlparse
from collections import defaultdict
import aiohttp
from asyncio import Semaphore, Queue

from crawl4ai import AsyncWebCrawler
from .monitoring import CrawlMonitor, AdaptiveThrottler, CircuitBreaker

logger = logging.getLogger(__name__)


@dataclass
class CrawlTask:
    """Represents a single crawl task."""
    url: str
    depth: int = 0
    priority: int = 5
    metadata: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0
    parent_url: Optional[str] = None


@dataclass
class CrawlResult:
    """Result of a crawl operation."""
    url: str
    success: bool
    content: Optional[str] = None
    links: List[str] = field(default_factory=list)
    error: Optional[str] = None
    duration: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


class ParallelCrawler:
    """
    High-performance parallel crawler with resource management and monitoring.
    """
    
    def __init__(self,
                 max_concurrent: int = 10,
                 max_depth: int = 3,
                 monitor: Optional[CrawlMonitor] = None,
                 progress_callback: Optional[Callable] = None):
        """
        Initialize parallel crawler.
        
        Args:
            max_concurrent: Maximum concurrent crawl operations
            max_depth: Maximum crawl depth
            monitor: Monitoring instance for metrics
            progress_callback: Callback for progress updates
        """
        self.max_concurrent = max_concurrent
        self.max_depth = max_depth
        self.monitor = monitor or CrawlMonitor()
        self.progress_callback = progress_callback
        
        # Resource management
        self.semaphore = Semaphore(max_concurrent)
        self.active_tasks: Set[asyncio.Task] = set()
        self.crawled_urls: Set[str] = set()
        self.url_queue: asyncio.Queue = asyncio.Queue()
        
        # Domain-based throttling
        self.domain_throttlers: Dict[str, AdaptiveThrottler] = defaultdict(
            lambda: AdaptiveThrottler(min_delay=0.5, max_delay=5.0)
        )
        
        # Circuit breakers per domain
        self.domain_breakers: Dict[str, CircuitBreaker] = defaultdict(
            lambda: CircuitBreaker(failure_threshold=5, recovery_timeout=60)
        )
        
        # Statistics
        self.stats = {
            "total_crawled": 0,
            "successful": 0,
            "failed": 0,
            "skipped": 0,
            "total_duration": 0.0
        }
        
        # Crawler instances pool
        self.crawler_pool: List[AsyncWebCrawler] = []
        self.pool_lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize crawler pool."""
        # Pre-create crawler instances for better performance
        for _ in range(min(self.max_concurrent, 5)):
            crawler = AsyncWebCrawler(
                browser_type="chromium",
                headless=True,
                verbose=False,
                timeout=30000,
                page_timeout=30000,
                wait_for_network_idle=True,
                remove_overlay_elements=True
            )
            await crawler.start()
            self.crawler_pool.append(crawler)
            
    async def cleanup(self):
        """Cleanup crawler resources."""
        for crawler in self.crawler_pool:
            try:
                await crawler.close()
            except Exception as e:
                logger.error(f"Error closing crawler: {e}")
                
    async def get_crawler(self) -> AsyncWebCrawler:
        """Get a crawler from the pool or create a new one."""
        async with self.pool_lock:
            if self.crawler_pool:
                return self.crawler_pool.pop()
            else:
                # Create new crawler if pool is empty
                crawler = AsyncWebCrawler(
                    browser_type="chromium",
                    headless=True,
                    verbose=False,
                    timeout=30000,
                    page_timeout=30000,
                    wait_for_network_idle=True,
                    remove_overlay_elements=True
                )
                await crawler.start()
                return crawler
                
    async def return_crawler(self, crawler: AsyncWebCrawler):
        """Return a crawler to the pool."""
        async with self.pool_lock:
            if len(self.crawler_pool) < self.max_concurrent:
                self.crawler_pool.append(crawler)
            else:
                # Close excess crawlers
                try:
                    await crawler.close()
                except:
                    pass
                    
    def extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            return urlparse(url).netloc
        except:
            return "unknown"
            
    async def crawl_url(self, task: CrawlTask) -> CrawlResult:
        """Crawl a single URL with monitoring and error handling."""
        start_time = time.time()
        domain = self.extract_domain(task.url)
        
        # Check circuit breaker
        breaker = self.domain_breakers[domain]
        if not breaker.can_proceed():
            return CrawlResult(
                url=task.url,
                success=False,
                error="Circuit breaker open",
                duration=0.0
            )
            
        # Apply domain throttling
        throttler = self.domain_throttlers[domain]
        await throttler.wait()
        
        crawler = None
        try:
            # Get crawler from pool
            crawler = await self.get_crawler()
            
            # Perform crawl
            result = await crawler.arun(
                url=task.url,
                bypass_cache=True,
                remove_overlay_elements=True,
                exclude_external_links=True,
                exclude_social_media_links=True
            )
            
            # Extract links if successful
            links = []
            if result.success:
                # Parse links from the content
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(result.html, 'html.parser')
                
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    if href.startswith('http'):
                        links.append(href)
                        
            duration = time.time() - start_time
            
            # Record metrics
            await self.monitor.record_crawl(
                url=task.url,
                duration=duration,
                success=result.success,
                error=None if result.success else "Crawl failed"
            )
            
            # Update throttler and breaker
            throttler.record_result(result.success)
            if result.success:
                breaker.record_success()
            else:
                breaker.record_failure()
                
            return CrawlResult(
                url=task.url,
                success=result.success,
                content=result.markdown if result.success else None,
                links=links,
                error=None if result.success else result.error_message,
                duration=duration,
                metadata={
                    "depth": task.depth,
                    "title": result.metadata.get("title", "") if result.success else None
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Error crawling {task.url}: {str(e)}")
            
            # Record failure
            breaker.record_failure()
            throttler.record_result(False)
            await self.monitor.record_crawl(
                url=task.url,
                duration=duration,
                success=False,
                error=str(e)
            )
            
            return CrawlResult(
                url=task.url,
                success=False,
                error=str(e),
                duration=duration
            )
            
        finally:
            # Return crawler to pool
            if crawler:
                await self.return_crawler(crawler)
                
    async def process_crawl_task(self, task: CrawlTask) -> Optional[CrawlResult]:
        """Process a single crawl task with semaphore control."""
        # Skip if already crawled
        if task.url in self.crawled_urls:
            self.stats["skipped"] += 1
            return None
            
        # Mark as crawled
        self.crawled_urls.add(task.url)
        
        async with self.semaphore:
            # Update active connections
            await self.monitor.update_resource_usage(
                active_connections=self.max_concurrent - self.semaphore._value,
                queue_depth=self.url_queue.qsize()
            )
            
            # Crawl the URL
            result = await self.crawl_url(task)
            
            # Update statistics
            self.stats["total_crawled"] += 1
            if result.success:
                self.stats["successful"] += 1
                
                # Add discovered links to queue if within depth limit
                if task.depth < self.max_depth:
                    for link in result.links:
                        if link not in self.crawled_urls:
                            await self.url_queue.put(CrawlTask(
                                url=link,
                                depth=task.depth + 1,
                                parent_url=task.url
                            ))
            else:
                self.stats["failed"] += 1
                
            self.stats["total_duration"] += result.duration
            
            # Progress callback
            if self.progress_callback:
                await self.progress_callback({
                    "url": task.url,
                    "success": result.success,
                    "stats": self.stats.copy(),
                    "queue_size": self.url_queue.qsize()
                })
                
            return result
            
    async def crawl_batch(self, urls: List[str]) -> List[CrawlResult]:
        """
        Crawl a batch of URLs in parallel.
        
        Args:
            urls: List of URLs to crawl
            
        Returns:
            List of crawl results
        """
        # Initialize if needed
        if not self.crawler_pool:
            await self.initialize()
            
        # Reset statistics
        self.stats = {
            "total_crawled": 0,
            "successful": 0,
            "failed": 0,
            "skipped": 0,
            "total_duration": 0.0
        }
        
        # Add initial URLs to queue
        for url in urls:
            await self.url_queue.put(CrawlTask(url=url, depth=0))
            
        # Process queue
        results = []
        workers = []
        
        async def worker():
            """Worker coroutine to process tasks from queue."""
            while True:
                try:
                    # Get task with timeout
                    task = await asyncio.wait_for(
                        self.url_queue.get(),
                        timeout=5.0
                    )
                    
                    result = await self.process_crawl_task(task)
                    if result:
                        results.append(result)
                        
                except asyncio.TimeoutError:
                    # No more tasks, exit worker
                    break
                except Exception as e:
                    logger.error(f"Worker error: {str(e)}")
                    
        # Start workers
        for _ in range(self.max_concurrent):
            worker_task = asyncio.create_task(worker())
            workers.append(worker_task)
            
        # Wait for all workers to complete
        await asyncio.gather(*workers, return_exceptions=True)
        
        # Final progress update
        if self.progress_callback:
            await self.progress_callback({
                "completed": True,
                "stats": self.stats,
                "total_results": len(results)
            })
            
        return results
        
    async def crawl_smart(self,
                         start_url: str,
                         url_filter: Optional[Callable[[str], bool]] = None,
                         content_processor: Optional[Callable[[str, str], Dict[str, Any]]] = None
                         ) -> Dict[str, Any]:
        """
        Smart crawl with custom filtering and processing.
        
        Args:
            start_url: Starting URL
            url_filter: Optional function to filter URLs
            content_processor: Optional function to process content
            
        Returns:
            Crawl results with processed content
        """
        if not self.crawler_pool:
            await self.initialize()
            
        # Custom progress tracking
        processed_results = []
        
        async def process_with_callback(progress):
            """Extended progress callback with content processing."""
            if progress.get("url") and progress.get("success"):
                # Get the result
                url = progress["url"]
                # Find matching result
                for result in results:
                    if result.url == url and content_processor:
                        processed = await asyncio.to_thread(
                            content_processor,
                            url,
                            result.content
                        )
                        processed_results.append({
                            "url": url,
                            "processed": processed,
                            "metadata": result.metadata
                        })
                        
            # Call original progress callback
            if self.progress_callback:
                await self.progress_callback(progress)
                
        # Temporarily replace progress callback
        original_callback = self.progress_callback
        self.progress_callback = process_with_callback
        
        # Perform crawl
        results = await self.crawl_batch([start_url])
        
        # Restore original callback
        self.progress_callback = original_callback
        
        # Get monitoring data
        health_data = self.monitor.get_dashboard_data()
        
        return {
            "results": processed_results if content_processor else results,
            "stats": self.stats,
            "health": health_data,
            "crawled_urls": list(self.crawled_urls)
        }