"""
Enhanced Background Job Worker System with Advanced Features

This module implements the enhanced background job processing system with:
- Comprehensive monitoring and metrics collection
- Circuit breakers and adaptive throttling
- Parallel processing capabilities
- Real-time progress streaming
- Advanced error handling and recovery
"""

import asyncio
import json
import logging
import signal
import sys
import traceback
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
from urllib.parse import urlparse

from supabase import Client
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

# Add the src directory to the path
src_path = Path(__file__).resolve().parent
sys.path.insert(0, str(src_path))

from job_manager import JobManager, JobType, JobStatus, get_job_manager
from utils import get_supabase_client, add_documents_to_supabase, add_code_examples_to_supabase, update_source_info
from monitoring import CrawlMonitor, HealthStatus
from parallel_processor import ParallelCrawler, CrawlTask

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedJobWorker:
    """
    Enhanced background worker with monitoring, resilience, and parallel processing.
    """
    
    def __init__(self, worker_id: str = "enhanced-worker-1", max_concurrent: int = 10):
        """Initialize the enhanced job worker."""
        self.worker_id = worker_id
        self.max_concurrent = max_concurrent
        self.supabase = get_supabase_client()
        self.job_manager = get_job_manager()
        self.running = False
        self.current_job_id: Optional[str] = None
        self.queue_name = "crawl_jobs_queue"
        
        # Initialize monitoring
        self.monitor = CrawlMonitor(
            alert_callback=self._handle_monitoring_alert,
            log_file=f"worker_{worker_id}.log"
        )
        
        # Initialize parallel crawler
        self.parallel_crawler = ParallelCrawler(
            max_concurrent=max_concurrent,
            monitor=self.monitor,
            progress_callback=self._handle_crawl_progress
        )
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Performance tracking
        self.job_start_time: Optional[float] = None
        self.processed_jobs = 0
        self.failed_jobs = 0
        
        logger.info(f"Initialized enhanced job worker {worker_id} with {max_concurrent} concurrent crawlers")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    async def start(self):
        """Start the enhanced job worker main loop."""
        logger.info(f"Starting enhanced job worker {self.worker_id}")
        self.running = True
        
        # Initialize parallel crawler pool
        await self.parallel_crawler.initialize()
        
        # Start monitoring loop
        monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        try:
            while self.running:
                try:
                    # Check system health before processing
                    health_status = self.monitor.get_health_status()
                    if health_status["status"] == HealthStatus.CRITICAL.value:
                        logger.warning("System in critical state, pausing job processing")
                        await asyncio.sleep(30)
                        continue
                    
                    # Poll for jobs
                    job_message = await self._poll_job_queue()
                    
                    if job_message:
                        await self._process_job_enhanced(job_message)
                    else:
                        # No jobs available
                        await asyncio.sleep(5)
                        
                except Exception as e:
                    logger.error(f"Error in worker main loop: {str(e)}")
                    logger.error(traceback.format_exc())
                    await asyncio.sleep(10)
                    
        finally:
            monitoring_task.cancel()
            await self._cleanup()
            logger.info(f"Enhanced job worker {self.worker_id} stopped")
            logger.info(f"Processed {self.processed_jobs} jobs ({self.failed_jobs} failed)")
    
    async def _monitoring_loop(self):
        """Background monitoring loop."""
        while self.running:
            try:
                # Update resource usage
                await self.monitor.update_resource_usage(
                    active_connections=len(self.parallel_crawler.active_tasks),
                    queue_depth=await self._get_queue_depth()
                )
                
                # Check health status
                health = self.monitor.get_health_status()
                if health["status"] != HealthStatus.HEALTHY.value:
                    logger.warning(f"System health degraded: {health}")
                
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {str(e)}")
                await asyncio.sleep(30)
    
    async def _handle_monitoring_alert(self, alerts: List[Dict[str, Any]]):
        """Handle monitoring alerts."""
        for alert in alerts:
            logger.warning(f"ALERT: {alert['metric']} = {alert['value']} (threshold: {alert['threshold']})")
            
            # Take action based on alert type
            if alert["metric"] == "consecutive_errors" and alert["level"] == "critical":
                logger.error("Too many consecutive errors, worker will pause for 60 seconds")
                await asyncio.sleep(60)
    
    async def _handle_crawl_progress(self, progress: Dict[str, Any]):
        """Handle crawl progress updates."""
        if self.current_job_id:
            # Update job progress
            stats = progress.get("stats", {})
            total = stats.get("total_crawled", 0)
            successful = stats.get("successful", 0)
            
            if total > 0:
                progress_percent = int((successful / total) * 100)
                message = f"Crawled {successful}/{total} pages"
                
                await self.job_manager.update_job_progress(
                    self.current_job_id,
                    "Crawling pages",
                    progress_percent,
                    message,
                    pages_processed=successful
                )
    
    async def _poll_job_queue(self) -> Optional[Dict[str, Any]]:
        """Poll the PGMQ queue for new jobs with circuit breaker."""
        cb = self.monitor.get_circuit_breaker("queue")
        if not cb.can_proceed():
            logger.warning("Queue circuit breaker open, skipping poll")
            return None
        
        try:
            result = self.supabase.rpc(
                "pgmq_read",
                {
                    "queue_name": self.queue_name,
                    "vt": 300,  # 5 minutes visibility timeout
                    "qty": 1
                }
            ).execute()
            
            cb.record_success()
            
            if result.data and len(result.data) > 0:
                message = result.data[0]
                return {
                    "msg_id": message["msg_id"],
                    "read_ct": message["read_ct"],
                    "enqueued_at": message["enqueued_at"],
                    "message": json.loads(message["message"])
                }
            
            return None
            
        except Exception as e:
            cb.record_failure()
            logger.error(f"Error polling job queue: {str(e)}")
            return None
    
    async def _process_job_enhanced(self, job_message: Dict[str, Any]):
        """Process job with enhanced monitoring and error handling."""
        msg_id = job_message["msg_id"]
        job_data = job_message["message"]
        job_id = job_data["job_id"]
        
        logger.info(f"Processing job {job_id} (message {msg_id})")
        self.current_job_id = job_id
        self.job_start_time = time.time()
        
        try:
            # Mark job as running
            await self.job_manager.mark_job_running(job_id)
            await self.job_manager.update_job_progress(
                job_id, "Starting enhanced job processing", 0, 
                f"Job picked up by enhanced worker {self.worker_id}"
            )
            
            # Route to appropriate handler
            job_type = JobType(job_data["job_type"])
            parameters = job_data["parameters"]
            
            if job_type == JobType.SINGLE_PAGE:
                await self._handle_single_page_enhanced(job_id, parameters)
            elif job_type == JobType.SMART_CRAWL:
                await self._handle_smart_crawl_enhanced(job_id, parameters)
            elif job_type == JobType.SITEMAP_CRAWL:
                await self._handle_sitemap_crawl_enhanced(job_id, parameters)
            else:
                # Fall back to standard handlers for other types
                await self._handle_standard_job(job_id, job_type, parameters)
            
            # Delete message from queue
            self.supabase.rpc("pgmq_delete", {
                "queue_name": self.queue_name,
                "msg_id": msg_id
            }).execute()
            
            # Record success
            duration = time.time() - self.job_start_time
            await self.monitor.record_crawl("job_completion", duration, True)
            self.processed_jobs += 1
            
            logger.info(f"Successfully processed job {job_id} in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Error processing job {job_id}: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Record failure
            duration = time.time() - self.job_start_time if self.job_start_time else 0
            await self.monitor.record_crawl("job_failure", duration, False, str(e))
            self.failed_jobs += 1
            
            # Mark job as failed
            await self.job_manager.fail_job(job_id, f"Enhanced processing failed: {str(e)}")
            
        finally:
            self.current_job_id = None
            self.job_start_time = None
    
    async def _handle_single_page_enhanced(self, job_id: str, parameters: Dict[str, Any]):
        """Handle single page with enhanced monitoring."""
        url = parameters["url"]
        chunk_size = parameters.get("chunk_size", 5000)
        
        await self.job_manager.update_job_progress(
            job_id, "Single page crawl", 10, f"Starting enhanced crawl of {url}"
        )
        
        # Use parallel crawler for single page (for monitoring benefits)
        results = await self.parallel_crawler.crawl_batch([url])
        
        if results and results[0].success:
            result = results[0]
            content = result.content
            
            if content:
                chunks = self._chunk_content(content, chunk_size)
                
                await self.job_manager.update_job_progress(
                    job_id, "Storing content", 80, f"Storing {len(chunks)} chunks"
                )
                
                await add_documents_to_supabase(
                    url, chunks, source_id=self._get_source_id(url)
                )
                
                await update_source_info(
                    source_id=self._get_source_id(url),
                    summary=f"Enhanced single page crawl of {url}",
                    word_count=len(content.split())
                )
                
                await self.job_manager.complete_job(
                    job_id,
                    result_summary={
                        "url": url,
                        "chunks_created": len(chunks),
                        "content_length": len(content),
                        "word_count": len(content.split()),
                        "crawl_duration": result.duration
                    },
                    pages_crawled=1,
                    chunks_created=len(chunks)
                )
            else:
                raise Exception("No content extracted from page")
        else:
            error = results[0].error if results else "Unknown error"
            raise Exception(f"Failed to crawl page: {error}")
    
    async def _handle_smart_crawl_enhanced(self, job_id: str, parameters: Dict[str, Any]):
        """Handle smart crawl with parallel processing."""
        url = parameters["url"]
        max_depth = parameters.get("max_depth", 3)
        max_concurrent = parameters.get("max_concurrent", self.max_concurrent)
        chunk_size = parameters.get("chunk_size", 5000)
        
        await self.job_manager.update_job_progress(
            job_id, "Smart crawl initialization", 5, 
            f"Starting parallel crawl of {url} with depth {max_depth}"
        )
        
        # Configure parallel crawler
        self.parallel_crawler.max_depth = max_depth
        self.parallel_crawler.max_concurrent = min(max_concurrent, self.max_concurrent)
        
        # Content processor for chunking and storing
        async def process_content(url: str, content: str) -> Dict[str, Any]:
            """Process and store crawled content."""
            if not content:
                return {"chunks": 0}
            
            chunks = self._chunk_content(content, chunk_size)
            await add_documents_to_supabase(
                url, chunks, source_id=self._get_source_id(parameters["url"])
            )
            
            return {
                "chunks": len(chunks),
                "words": len(content.split())
            }
        
        # Perform smart crawl
        result = await self.parallel_crawler.crawl_smart(
            start_url=url,
            content_processor=process_content
        )
        
        # Aggregate results
        total_chunks = sum(r["processed"]["chunks"] for r in result["results"] if "processed" in r)
        total_words = sum(r["processed"]["words"] for r in result["results"] if "processed" in r)
        
        await update_source_info(
            source_id=self._get_source_id(url),
            summary=f"Enhanced smart crawl of {url} ({len(result['crawled_urls'])} pages)",
            word_count=total_words
        )
        
        await self.job_manager.complete_job(
            job_id,
            result_summary={
                "start_url": url,
                "max_depth": max_depth,
                "pages_crawled": result["stats"]["successful"],
                "pages_failed": result["stats"]["failed"],
                "total_chunks": total_chunks,
                "total_words": total_words,
                "health_status": result["health"]["health"]["status"]
            },
            pages_crawled=result["stats"]["successful"],
            chunks_created=total_chunks
        )
    
    async def _handle_sitemap_crawl_enhanced(self, job_id: str, parameters: Dict[str, Any]):
        """Handle sitemap crawl with parallel processing."""
        sitemap_url = parameters["url"]
        chunk_size = parameters.get("chunk_size", 5000)
        max_concurrent = parameters.get("max_concurrent", self.max_concurrent)
        
        await self.job_manager.update_job_progress(
            job_id, "Parsing sitemap", 10, f"Extracting URLs from {sitemap_url}"
        )
        
        # Fetch and parse sitemap
        import requests
        import xml.etree.ElementTree as ET
        
        try:
            response = requests.get(sitemap_url, timeout=30)
            response.raise_for_status()
            
            root = ET.fromstring(response.content)
            urls = []
            
            # Extract URLs from sitemap
            for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                if loc_elem is not None and loc_elem.text:
                    urls.append(loc_elem.text)
            
            if not urls:
                # Try without namespace
                for url_elem in root.findall('.//url'):
                    loc_elem = url_elem.find('loc')
                    if loc_elem is not None and loc_elem.text:
                        urls.append(loc_elem.text)
            
            await self.job_manager.update_job_progress(
                job_id, "Parallel sitemap crawl", 30, 
                f"Found {len(urls)} URLs, starting parallel crawl"
            )
            
            # Configure parallel crawler
            self.parallel_crawler.max_concurrent = min(max_concurrent, self.max_concurrent)
            self.parallel_crawler.max_depth = 0  # No recursive crawling for sitemap URLs
            
            # Crawl all URLs in parallel
            results = await self.parallel_crawler.crawl_batch(urls)
            
            # Process results
            total_chunks = 0
            successful_crawls = 0
            
            for result in results:
                if result.success and result.content:
                    chunks = self._chunk_content(result.content, chunk_size)
                    await add_documents_to_supabase(
                        result.url, chunks, source_id=self._get_source_id(sitemap_url)
                    )
                    total_chunks += len(chunks)
                    successful_crawls += 1
            
            # Update source info
            await update_source_info(
                source_id=self._get_source_id(sitemap_url),
                summary=f"Enhanced sitemap crawl of {sitemap_url} ({successful_crawls}/{len(urls)} pages)",
                word_count=total_chunks * 100  # Rough estimate
            )
            
            # Get final health status
            health_data = self.monitor.get_dashboard_data()
            
            await self.job_manager.complete_job(
                job_id,
                result_summary={
                    "sitemap_url": sitemap_url,
                    "total_urls": len(urls),
                    "successful_crawls": successful_crawls,
                    "failed_crawls": len(urls) - successful_crawls,
                    "total_chunks": total_chunks,
                    "avg_crawl_time": health_data["performance"]["avg_crawl_time"],
                    "error_rate": health_data["errors"]["rate"]
                },
                pages_crawled=successful_crawls,
                chunks_created=total_chunks
            )
            
        except Exception as e:
            raise Exception(f"Enhanced sitemap crawling failed: {str(e)}")
    
    async def _handle_standard_job(self, job_id: str, job_type: JobType, parameters: Dict[str, Any]):
        """Handle other job types with standard processing."""
        # This would implement standard handling for TEXT_FILE_CRAWL and REPOSITORY_PARSE
        # For now, we'll just fail these as not implemented in enhanced mode
        await self.job_manager.fail_job(
            job_id, 
            f"Job type {job_type.value} not yet implemented in enhanced mode"
        )
    
    async def _get_queue_depth(self) -> int:
        """Get current queue depth from PGMQ."""
        try:
            result = self.supabase.rpc(
                "pgmq_metrics",
                {"queue_name": self.queue_name}
            ).execute()
            
            if result.data and len(result.data) > 0:
                return result.data[0].get("queue_length", 0)
            return 0
            
        except Exception:
            return 0
    
    async def _cleanup(self):
        """Clean up resources."""
        await self.parallel_crawler.cleanup()
    
    def _chunk_content(self, content: str, chunk_size: int) -> List[str]:
        """Chunk content into manageable pieces."""
        if not content:
            return []
        
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            
            # Try to break at sentence boundaries
            if end < len(content):
                last_period = content.rfind('. ', start, end)
                if last_period > start + chunk_size * 0.3:
                    end = last_period + 1
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end
        
        return chunks
    
    def _get_source_id(self, url: str) -> str:
        """Extract source ID from URL."""
        parsed = urlparse(url)
        return parsed.netloc


async def main():
    """Main entry point for the enhanced job worker."""
    import os
    
    worker_id = os.getenv("WORKER_ID", f"enhanced-worker-{os.getpid()}")
    max_concurrent = int(os.getenv("MAX_CONCURRENT", "10"))
    
    worker = EnhancedJobWorker(worker_id, max_concurrent)
    
    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("Enhanced worker stopped by user")
    except Exception as e:
        logger.error(f"Enhanced worker failed: {str(e)}")
        logger.error(traceback.format_exc())
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))