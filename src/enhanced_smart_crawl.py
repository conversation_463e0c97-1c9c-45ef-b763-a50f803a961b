"""
Enhanced Smart Crawl with Streaming Support

This module provides an enhanced version of smart_crawl_url that processes
content incrementally to avoid timeouts on large sites.
"""

import json
import async<PERSON>
from typing import Dict, Any, Optional
from urllib.parse import urlparse
import os

from streaming_processor import (
    StreamingChunkProcessor, 
    StreamingCrawler, 
    ResumableStreamingCrawler,
    CrawlProgress
)
from crawl4ai_mcp import (
    get_crawler,
    get_supabase_client,
    is_txt,
    is_sitemap,
    parse_sitemap,
    crawl_markdown_file
)
from utils import compute_source_summary


async def smart_crawl_url_streaming(
    ctx: Any,
    url: str,
    max_depth: int = 3,
    max_concurrent: int = 10,
    chunk_size: int = 5000,
    enable_checkpoints: bool = True,
    progress_updates: bool = True
) -> str:
    """
    Enhanced version of smart_crawl_url with streaming support.
    
    This function processes content incrementally to avoid timeouts:
    - Chunks are created and stored as pages are crawled
    - Progress is reported in real-time
    - Checkpoints allow resuming interrupted crawls
    - Graceful handling of large sites
    
    Args:
        ctx: The MCP server provided context
        url: URL to crawl
        max_depth: Maximum recursion depth for regular URLs
        max_concurrent: Maximum number of concurrent browser sessions
        chunk_size: Maximum size of each content chunk
        enable_checkpoints: Whether to save checkpoints for resumption
        progress_updates: Whether to send progress updates
        
    Returns:
        JSON string with crawl summary and progress information
    """
    try:
        # Initialize components
        crawler = get_crawler()
        supabase_client = get_supabase_client()
        chunk_processor = StreamingChunkProcessor(chunk_size=chunk_size)
        
        # Create appropriate crawler based on checkpoint support
        if enable_checkpoints:
            streaming_crawler = ResumableStreamingCrawler(crawler, chunk_processor)
            crawl_id = f"crawl_{urlparse(url).netloc}_{int(asyncio.get_event_loop().time())}"
        else:
            streaming_crawler = StreamingCrawler(crawler, chunk_processor)
            crawl_id = None
        
        # Track overall statistics
        overall_stats = {
            "url": url,
            "crawl_type": None,
            "total_pages": 0,
            "total_chunks": 0,
            "failed_pages": 0,
            "sources": {},
            "progress_updates": []
        }
        
        # Progress callback for real-time updates
        async def progress_callback(progress: CrawlProgress):
            if progress_updates:
                update = {
                    "timestamp": asyncio.get_event_loop().time(),
                    "processed": progress.processed_urls,
                    "successful": progress.successful_urls,
                    "chunks_stored": progress.chunks_stored,
                    "success_rate": progress.success_rate,
                    "processing_rate": progress.processing_rate
                }
                overall_stats["progress_updates"].append(update)
                
                # Send SSE update if supported
                if hasattr(ctx, 'send_progress'):
                    await ctx.send_progress({
                        "type": "progress",
                        "data": update
                    })
        
        # Determine crawl strategy based on URL type
        if is_txt(url):
            # Text files - simple single file processing
            overall_stats["crawl_type"] = "text_file"
            results = await crawl_markdown_file(crawler, url)
            
            if results:
                for result in results:
                    chunks_created, chunks_stored = await chunk_processor.process_and_store_chunks(
                        result['url'],
                        result['markdown']
                    )
                    overall_stats["total_pages"] += 1
                    overall_stats["total_chunks"] += chunks_stored
                    
        elif is_sitemap(url):
            # Sitemap - extract and stream process URLs
            overall_stats["crawl_type"] = "sitemap"
            sitemap_urls = parse_sitemap(url)
            
            if not sitemap_urls:
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": "No URLs found in sitemap"
                }, indent=2)
            
            # Stream process sitemap URLs
            async for result in streaming_crawler.crawl_urls_streaming(
                sitemap_urls, 
                max_concurrent=max_concurrent
            ):
                if result.success:
                    overall_stats["total_pages"] += 1
                    overall_stats["total_chunks"] += len(result.chunks)
                else:
                    overall_stats["failed_pages"] += 1
                    
                # Track source statistics
                source_id = urlparse(result.url).netloc
                if source_id not in overall_stats["sources"]:
                    overall_stats["sources"][source_id] = {
                        "pages": 0,
                        "chunks": 0,
                        "failed": 0
                    }
                
                if result.success:
                    overall_stats["sources"][source_id]["pages"] += 1
                    overall_stats["sources"][source_id]["chunks"] += len(result.chunks)
                else:
                    overall_stats["sources"][source_id]["failed"] += 1
                    
        else:
            # Regular webpage - recursive streaming crawl
            overall_stats["crawl_type"] = "webpage"
            
            async for update in streaming_crawler.crawl_recursive_streaming(
                [url],
                max_depth=max_depth,
                max_concurrent=max_concurrent,
                progress_callback=progress_callback
            ):
                if update["type"] == "result":
                    result = update["result"]
                    if result.success:
                        overall_stats["total_pages"] += 1
                        # Chunks are already stored by streaming crawler
                    else:
                        overall_stats["failed_pages"] += 1
                        
                elif update["type"] == "checkpoint" and enable_checkpoints:
                    # Save checkpoint
                    await streaming_crawler.save_checkpoint(crawl_id)
                    
                elif update["type"] == "complete":
                    # Extract final statistics
                    final_progress = update["progress"]
                    overall_stats["total_chunks"] = final_progress["chunks_stored"]
                    
                elif update["type"] == "error":
                    print(f"❌ Streaming crawl error: {update['error']}")
        
        # Update source summaries
        if overall_stats["sources"]:
            for source_id, stats in overall_stats["sources"].items():
                if stats["pages"] > 0:
                    try:
                        # Update source summary in database
                        source_summary = {
                            "page_count": stats["pages"],
                            "chunk_count": stats["chunks"],
                            "failed_count": stats["failed"],
                            "crawl_type": overall_stats["crawl_type"]
                        }
                        
                        # Update or insert source record
                        existing = supabase_client.table('sources')\
                            .select('*')\
                            .eq('source_id', source_id)\
                            .execute()
                        
                        if existing.data:
                            # Update existing source
                            supabase_client.table('sources')\
                                .update({
                                    'summary': json.dumps(source_summary),
                                    'total_chunks': stats["chunks"],
                                    'last_updated': 'now()'
                                })\
                                .eq('source_id', source_id)\
                                .execute()
                        else:
                            # Insert new source
                            supabase_client.table('sources')\
                                .insert({
                                    'source_id': source_id,
                                    'summary': json.dumps(source_summary),
                                    'total_chunks': stats["chunks"]
                                })\
                                .execute()
                                
                    except Exception as e:
                        print(f"⚠️ Error updating source {source_id}: {str(e)}")
        
        # Prepare final response
        elapsed_time = streaming_crawler.progress.elapsed_time if hasattr(streaming_crawler, 'progress') else 0
        
        response = {
            "success": overall_stats["total_pages"] > 0,
            "url": url,
            "crawl_type": overall_stats["crawl_type"],
            "statistics": {
                "total_pages_crawled": overall_stats["total_pages"],
                "total_chunks_created": overall_stats["total_chunks"],
                "failed_pages": overall_stats["failed_pages"],
                "success_rate": (overall_stats["total_pages"] / 
                               (overall_stats["total_pages"] + overall_stats["failed_pages"]) * 100
                               if overall_stats["total_pages"] + overall_stats["failed_pages"] > 0 else 0),
                "elapsed_time_seconds": elapsed_time,
                "pages_per_minute": (overall_stats["total_pages"] / elapsed_time * 60) if elapsed_time > 0 else 0
            },
            "sources": overall_stats["sources"]
        }
        
        if enable_checkpoints and crawl_id:
            response["crawl_id"] = crawl_id
            response["checkpoint_enabled"] = True
            
        if progress_updates:
            response["progress_update_count"] = len(overall_stats["progress_updates"])
            
        return json.dumps(response, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "url": url,
            "error": f"Streaming crawl failed: {str(e)}",
            "error_type": type(e).__name__
        }, indent=2)


async def resume_crawl_from_checkpoint(
    ctx: Any,
    crawl_id: str,
    max_depth: int = 3,
    max_concurrent: int = 10
) -> str:
    """
    Resume an interrupted crawl from a checkpoint.
    
    Args:
        ctx: The MCP server provided context
        crawl_id: The ID of the crawl to resume
        max_depth: Maximum recursion depth
        max_concurrent: Maximum concurrent sessions
        
    Returns:
        JSON string with resumption result
    """
    try:
        crawler = get_crawler()
        chunk_processor = StreamingChunkProcessor()
        resumable_crawler = ResumableStreamingCrawler(crawler, chunk_processor)
        
        # Check if checkpoint exists
        checkpoint = await resumable_crawler.load_checkpoint(crawl_id)
        if not checkpoint:
            return json.dumps({
                "success": False,
                "crawl_id": crawl_id,
                "error": "No checkpoint found for this crawl ID"
            }, indent=2)
        
        # Resume statistics
        resume_stats = {
            "resumed_pages": 0,
            "resumed_chunks": 0,
            "resume_errors": 0
        }
        
        # Resume the crawl
        async for update in resumable_crawler.resume_crawl(
            crawl_id,
            max_depth=max_depth,
            max_concurrent=max_concurrent
        ):
            if update["type"] == "result":
                result = update["result"]
                if result.success:
                    resume_stats["resumed_pages"] += 1
                else:
                    resume_stats["resume_errors"] += 1
                    
            elif update["type"] == "complete":
                final_progress = update["progress"]
                resume_stats["resumed_chunks"] = final_progress["chunks_stored"]
        
        return json.dumps({
            "success": True,
            "crawl_id": crawl_id,
            "resumed_from_checkpoint": checkpoint["checkpoint"],
            "statistics": resume_stats
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "crawl_id": crawl_id,
            "error": f"Resume failed: {str(e)}"
        }, indent=2)


# Configuration for timeout management
STREAMING_CONFIG = {
    "DEFAULT_CHUNK_SIZE": 5000,
    "DEFAULT_BATCH_SIZE": 10,
    "CHECKPOINT_INTERVAL": 100,  # Save checkpoint every N pages
    "PROGRESS_UPDATE_INTERVAL": 10,  # Send progress every N pages
    "MAX_RETRIES_PER_URL": 3,
    "RETRY_DELAY": 2.0,  # seconds
    "CONNECTION_TIMEOUT": 30000,  # ms
    "NAVIGATION_TIMEOUT": 30000,  # ms
    "ADAPTIVE_TIMEOUT": {
        "enabled": True,
        "min_timeout": 10000,
        "max_timeout": 120000,
        "scale_factor": 1.5
    }
}