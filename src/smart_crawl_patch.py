"""
Patch for smart_crawl_url to add streaming mode

This patch adds a streaming_mode parameter to smart_crawl_url that enables
incremental processing to avoid timeouts on large sites.
"""

# Add this import at the top of crawl4ai_mcp.py
# from enhanced_smart_crawl import smart_crawl_url_streaming

# Replace the existing smart_crawl_url function with this enhanced version:

@mcp.tool()
async def smart_crawl_url(
    ctx: Context, 
    url: str, 
    max_depth: int = 3, 
    max_concurrent: int = 10, 
    chunk_size: int = 5000,
    streaming_mode: bool = False
) -> str:
    """
    Intelligently crawl a URL based on its type and store content in Supabase.
    
    This tool automatically detects the URL type and applies the appropriate crawling method:
    - For sitemaps: Extracts and crawls all URLs in parallel
    - For text files (llms.txt): Directly retrieves the content
    - For regular webpages: Recursively crawls internal links up to the specified depth
    
    All crawled content is chunked and stored in Supabase for later retrieval and querying.
    
    NEW: Streaming Mode (streaming_mode=True)
    - Processes and stores chunks incrementally as pages are crawled
    - Provides real-time progress updates
    - Supports checkpoint-based resumption for interrupted crawls
    - Prevents timeouts on large sites by not waiting for all crawling to complete
    
    Args:
        ctx: The MCP server provided context
        url: URL to crawl (can be a regular webpage, sitemap.xml, or .txt file)
        max_depth: Maximum recursion depth for regular URLs (default: 3)
        max_concurrent: Maximum number of concurrent browser sessions (default: 10)
        chunk_size: Maximum size of each content chunk in characters (default: 5000)
        streaming_mode: Enable incremental processing to avoid timeouts (default: False)
    
    Returns:
        JSON string with crawl summary and storage information
    """
    # Use streaming mode for large sites or when explicitly requested
    if streaming_mode or _should_use_streaming(url, max_depth):
        try:
            from enhanced_smart_crawl import smart_crawl_url_streaming
            return await smart_crawl_url_streaming(
                ctx=ctx,
                url=url,
                max_depth=max_depth,
                max_concurrent=max_concurrent,
                chunk_size=chunk_size,
                enable_checkpoints=True,
                progress_updates=True
            )
        except ImportError:
            # Fallback if streaming module not available
            print("⚠️ Streaming mode requested but module not available, using standard mode")
    
    # Original implementation continues here...
    try:
        # Get components safely
        crawler = get_crawler()
        supabase_client = get_supabase_client()
        
        # [Rest of original implementation...]


def _should_use_streaming(url: str, max_depth: int) -> bool:
    """
    Heuristic to determine if streaming mode should be used automatically.
    
    Returns True if:
    - max_depth >= 3 (deep crawls likely to be large)
    - URL is a sitemap (potentially many pages)
    - URL pattern suggests documentation site
    """
    # Deep crawls benefit from streaming
    if max_depth >= 3:
        return True
    
    # Sitemaps often contain many URLs
    if is_sitemap(url):
        return True
    
    # Documentation sites tend to be large
    doc_patterns = [
        '/docs', '/documentation', '/guide', '/manual',
        '/reference', '/api-reference', '/developer'
    ]
    
    if any(pattern in url.lower() for pattern in doc_patterns):
        return True
    
    return False


# Add this new tool for resuming interrupted crawls:

@mcp.tool()
async def resume_crawl(
    ctx: Context,
    crawl_id: str,
    max_depth: int = 3,
    max_concurrent: int = 10
) -> str:
    """
    Resume an interrupted crawl from a checkpoint.
    
    This tool allows resuming a crawl that was interrupted due to timeout,
    error, or manual cancellation. The crawl will continue from where it left off.
    
    Args:
        ctx: The MCP server provided context
        crawl_id: The ID of the crawl to resume (provided in original crawl response)
        max_depth: Maximum recursion depth (should match original)
        max_concurrent: Maximum concurrent sessions (should match original)
    
    Returns:
        JSON string with resumption result and continued statistics
    """
    try:
        from enhanced_smart_crawl import resume_crawl_from_checkpoint
        return await resume_crawl_from_checkpoint(
            ctx=ctx,
            crawl_id=crawl_id,
            max_depth=max_depth,
            max_concurrent=max_concurrent
        )
    except ImportError:
        return json.dumps({
            "success": False,
            "error": "Resume functionality not available - streaming module required"
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"Resume failed: {str(e)}"
        }, indent=2)