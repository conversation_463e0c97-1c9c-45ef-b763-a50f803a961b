"""
Configuration Management System for MCP Crawl4AI

Provides centralized configuration management with environment variable support,
validation, and runtime configuration updates.
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field, asdict
from pathlib import Path
from dotenv import load_dotenv
import yaml

logger = logging.getLogger(__name__)


@dataclass
class CrawlerConfig:
    """Crawler-specific configuration"""
    max_concurrent: int = 10
    max_depth: int = 3
    timeout: int = 30000
    page_timeout: int = 30000
    headless: bool = True
    verbose: bool = False
    browser_type: str = "chromium"
    wait_for_network_idle: bool = True
    remove_overlay_elements: bool = True
    chunk_size: int = 5000
    retry_max_attempts: int = 3
    retry_base_delay: float = 2.0
    retry_max_delay: float = 60.0


@dataclass
class StorageConfig:
    """Storage-specific configuration"""
    supabase_url: str = ""
    supabase_service_key: str = ""
    use_direct_embedding: bool = True
    bge_service_url: str = "http://localhost:8080"
    embedding_model: str = "BAAI/bge-large-en-v1.5"
    use_hybrid_search: bool = True
    use_agentic_rag: bool = False
    use_reranking: bool = False
    openrouter_api_key: str = ""
    openrouter_model: str = "anthropic/claude-3.5-sonnet"


@dataclass
class MonitoringConfig:
    """Monitoring-specific configuration"""
    enable_monitoring: bool = True
    metrics_port: int = 9090
    health_check_interval: int = 60
    alert_webhook_url: str = ""
    log_level: str = "INFO"
    log_file: str = "crawl4ai_mcp.log"
    error_rate_threshold: float = 10.0
    consecutive_errors_threshold: int = 10
    avg_crawl_time_threshold: float = 60.0
    memory_usage_threshold: int = 1024 * 1024 * 1024  # 1GB
    queue_depth_threshold: int = 1000


@dataclass
class WorkerConfig:
    """Worker-specific configuration"""
    worker_count: int = 1
    worker_id_prefix: str = "worker"
    queue_name: str = "crawl_jobs_queue"
    visibility_timeout: int = 300
    max_retries: int = 3
    batch_size: int = 10


@dataclass
class FeatureFlags:
    """Feature flags for enabling/disabling functionality"""
    enable_knowledge_graph: bool = False
    enable_code_extraction: bool = True
    enable_sitemap_detection: bool = True
    enable_recursive_crawling: bool = True
    enable_job_system: bool = True
    enable_circuit_breakers: bool = True
    enable_adaptive_throttling: bool = True
    enable_graceful_degradation: bool = True


@dataclass
class Config:
    """Main configuration container"""
    crawler: CrawlerConfig = field(default_factory=CrawlerConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    worker: WorkerConfig = field(default_factory=WorkerConfig)
    features: FeatureFlags = field(default_factory=FeatureFlags)
    
    # Runtime configuration
    environment: str = "development"
    debug: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return asdict(self)
        
    def update_from_dict(self, updates: Dict[str, Any]):
        """Update configuration from dictionary"""
        for section, values in updates.items():
            if hasattr(self, section) and isinstance(values, dict):
                section_obj = getattr(self, section)
                for key, value in values.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)


class ConfigManager:
    """Manages application configuration with validation and runtime updates"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = Config()
        self.config_file = config_file
        self.env_prefix = "CRAWL4AI_"
        
        # Load configuration in order of precedence
        self._load_defaults()
        self._load_config_file()
        self._load_environment_variables()
        self._validate_configuration()
        
        # Setup logging based on configuration
        self._setup_logging()
        
    def _load_defaults(self):
        """Load default configuration values"""
        # Defaults are already set in dataclass definitions
        pass
        
    def _load_config_file(self):
        """Load configuration from file if provided"""
        if not self.config_file:
            # Try to find config file in standard locations
            config_paths = [
                Path("config.yaml"),
                Path("config.json"),
                Path.home() / ".crawl4ai" / "config.yaml",
                Path("/etc/crawl4ai/config.yaml")
            ]
            
            for path in config_paths:
                if path.exists():
                    self.config_file = str(path)
                    break
                    
        if self.config_file and Path(self.config_file).exists():
            logger.info(f"Loading configuration from {self.config_file}")
            
            with open(self.config_file, 'r') as f:
                if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                    data = yaml.safe_load(f)
                else:
                    data = json.load(f)
                    
            self.config.update_from_dict(data)
            
    def _load_environment_variables(self):
        """Load configuration from environment variables"""
        # Load .env file if it exists
        load_dotenv()
        
        # Map of environment variables to configuration paths
        env_mapping = {
            # Crawler settings
            f"{self.env_prefix}MAX_CONCURRENT": ("crawler", "max_concurrent", int),
            f"{self.env_prefix}MAX_DEPTH": ("crawler", "max_depth", int),
            f"{self.env_prefix}TIMEOUT": ("crawler", "timeout", int),
            f"{self.env_prefix}HEADLESS": ("crawler", "headless", self._parse_bool),
            f"{self.env_prefix}CHUNK_SIZE": ("crawler", "chunk_size", int),
            
            # Storage settings
            "SUPABASE_URL": ("storage", "supabase_url", str),
            "SUPABASE_SERVICE_KEY": ("storage", "supabase_service_key", str),
            "USE_DIRECT_EMBEDDING": ("storage", "use_direct_embedding", self._parse_bool),
            "BGE_SERVICE_URL": ("storage", "bge_service_url", str),
            "USE_HYBRID_SEARCH": ("storage", "use_hybrid_search", self._parse_bool),
            "USE_AGENTIC_RAG": ("storage", "use_agentic_rag", self._parse_bool),
            "USE_RERANKING": ("storage", "use_reranking", self._parse_bool),
            "OPENROUTER_API_KEY": ("storage", "openrouter_api_key", str),
            
            # Monitoring settings
            f"{self.env_prefix}ENABLE_MONITORING": ("monitoring", "enable_monitoring", self._parse_bool),
            f"{self.env_prefix}LOG_LEVEL": ("monitoring", "log_level", str),
            f"{self.env_prefix}LOG_FILE": ("monitoring", "log_file", str),
            
            # Worker settings
            f"{self.env_prefix}WORKER_COUNT": ("worker", "worker_count", int),
            f"{self.env_prefix}WORKER_ID": ("worker", "worker_id_prefix", str),
            
            # Feature flags
            "USE_KNOWLEDGE_GRAPH": ("features", "enable_knowledge_graph", self._parse_bool),
            f"{self.env_prefix}ENABLE_CODE_EXTRACTION": ("features", "enable_code_extraction", self._parse_bool),
            f"{self.env_prefix}ENABLE_JOB_SYSTEM": ("features", "enable_job_system", self._parse_bool),
            
            # Runtime settings
            f"{self.env_prefix}ENVIRONMENT": ("environment", None, str),
            f"{self.env_prefix}DEBUG": ("debug", None, self._parse_bool),
        }
        
        for env_var, (section, key, parser) in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    parsed_value = parser(value)
                    if section and key:
                        section_obj = getattr(self.config, section)
                        setattr(section_obj, key, parsed_value)
                    else:
                        setattr(self.config, section, parsed_value)
                except Exception as e:
                    logger.warning(f"Failed to parse {env_var}: {e}")
                    
    def _parse_bool(self, value: str) -> bool:
        """Parse boolean from string"""
        return value.lower() in ('true', '1', 'yes', 'on')
        
    def _validate_configuration(self):
        """Validate configuration values"""
        errors = []
        
        # Required configurations
        if not self.config.storage.supabase_url:
            errors.append("SUPABASE_URL is required")
        if not self.config.storage.supabase_service_key:
            errors.append("SUPABASE_SERVICE_KEY is required")
            
        # Validate ranges
        if self.config.crawler.max_concurrent < 1:
            errors.append("max_concurrent must be at least 1")
        if self.config.crawler.max_concurrent > 50:
            errors.append("max_concurrent should not exceed 50")
            
        if self.config.crawler.max_depth < 0:
            errors.append("max_depth must be non-negative")
        if self.config.crawler.max_depth > 10:
            errors.append("max_depth should not exceed 10")
            
        # Validate feature dependencies
        if self.config.storage.use_agentic_rag and not self.config.storage.openrouter_api_key:
            errors.append("OPENROUTER_API_KEY is required when use_agentic_rag is enabled")
            
        if self.config.features.enable_knowledge_graph:
            # Check if Neo4j environment variables are set
            if not os.getenv("NEO4J_URI"):
                errors.append("NEO4J_URI is required when knowledge graph is enabled")
                
        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"  - {e}" for e in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
            
    def _setup_logging(self):
        """Setup logging based on configuration"""
        log_level = getattr(logging, self.config.monitoring.log_level.upper(), logging.INFO)
        
        # Configure root logger
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.monitoring.log_file),
                logging.StreamHandler()
            ]
        )
        
    def get(self, path: str, default: Any = None) -> Any:
        """Get configuration value by dot-separated path"""
        parts = path.split('.')
        value = self.config
        
        try:
            for part in parts:
                if hasattr(value, part):
                    value = getattr(value, part)
                else:
                    return default
            return value
        except:
            return default
            
    def set(self, path: str, value: Any):
        """Set configuration value by dot-separated path"""
        parts = path.split('.')
        obj = self.config
        
        # Navigate to the parent object
        for part in parts[:-1]:
            if hasattr(obj, part):
                obj = getattr(obj, part)
            else:
                raise ValueError(f"Invalid configuration path: {path}")
                
        # Set the value
        if hasattr(obj, parts[-1]):
            setattr(obj, parts[-1], value)
        else:
            raise ValueError(f"Invalid configuration key: {parts[-1]}")
            
    def update(self, updates: Dict[str, Any]):
        """Update multiple configuration values"""
        self.config.update_from_dict(updates)
        
    def save(self, file_path: Optional[str] = None):
        """Save current configuration to file"""
        save_path = file_path or self.config_file
        if not save_path:
            save_path = "config.yaml"
            
        config_dict = self.config.to_dict()
        
        # Remove sensitive information
        if 'storage' in config_dict:
            config_dict['storage']['supabase_service_key'] = "***"
            config_dict['storage']['openrouter_api_key'] = "***"
            
        with open(save_path, 'w') as f:
            if save_path.endswith('.yaml') or save_path.endswith('.yml'):
                yaml.safe_dump(config_dict, f, default_flow_style=False)
            else:
                json.dump(config_dict, f, indent=2)
                
        logger.info(f"Configuration saved to {save_path}")
        
    def get_runtime_info(self) -> Dict[str, Any]:
        """Get runtime configuration information"""
        return {
            "environment": self.config.environment,
            "debug": self.config.debug,
            "features_enabled": {
                k: v for k, v in asdict(self.config.features).items() if v
            },
            "crawler_limits": {
                "max_concurrent": self.config.crawler.max_concurrent,
                "max_depth": self.config.crawler.max_depth
            },
            "monitoring_enabled": self.config.monitoring.enable_monitoring,
            "worker_count": self.config.worker.worker_count
        }


# Global configuration instance
_config_manager: Optional[ConfigManager] = None


def get_config() -> ConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def init_config(config_file: Optional[str] = None) -> ConfigManager:
    """Initialize the global configuration manager"""
    global _config_manager
    _config_manager = ConfigManager(config_file)
    return _config_manager