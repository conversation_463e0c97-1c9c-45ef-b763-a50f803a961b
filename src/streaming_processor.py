"""
Streaming Processor for Incremental Crawling and Chunking

This module provides a streaming architecture for processing web crawls incrementally,
avoiding timeouts by processing and storing content as it arrives rather than waiting
for all crawling to complete.
"""

import asyncio
import json
import time
from typing import AsyncIterator, Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict
import os
from urllib.parse import urlparse

# Import utilities from the main module
from utils import get_supabase_client, generate_embedding


@dataclass
class CrawlProgress:
    """Track crawling progress and statistics"""
    total_urls: int = 0
    processed_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    chunks_created: int = 0
    chunks_stored: int = 0
    start_time: float = field(default_factory=time.time)
    last_checkpoint: Optional[Dict[str, Any]] = None
    errors: List[Dict[str, Any]] = field(default_factory=list)
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time
    
    @property
    def success_rate(self) -> float:
        if self.processed_urls == 0:
            return 0.0
        return (self.successful_urls / self.processed_urls) * 100
    
    @property
    def processing_rate(self) -> float:
        """URLs processed per minute"""
        if self.elapsed_time == 0:
            return 0.0
        return (self.processed_urls / self.elapsed_time) * 60
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert progress to dictionary for serialization"""
        return {
            "total_urls": self.total_urls,
            "processed_urls": self.processed_urls,
            "successful_urls": self.successful_urls,
            "failed_urls": self.failed_urls,
            "chunks_created": self.chunks_created,
            "chunks_stored": self.chunks_stored,
            "elapsed_time": self.elapsed_time,
            "success_rate": self.success_rate,
            "processing_rate": self.processing_rate,
            "last_checkpoint": self.last_checkpoint,
            "error_count": len(self.errors)
        }


@dataclass
class StreamingResult:
    """Result of a streaming crawl operation"""
    url: str
    success: bool
    markdown: Optional[str] = None
    chunks: List[str] = field(default_factory=list)
    error: Optional[str] = None
    links: Dict[str, List[str]] = field(default_factory=dict)
    processing_time: float = 0.0


class StreamingChunkProcessor:
    """Process chunks in a streaming fashion as they arrive"""
    
    def __init__(self, chunk_size: int = 5000, batch_size: int = 10):
        self.chunk_size = chunk_size
        self.batch_size = batch_size
        self.supabase = get_supabase_client()
        self.pending_chunks = []
        self.use_direct_embedding = os.getenv("USE_DIRECT_EMBEDDING", "true") == "true"
        
    async def process_and_store_chunks(self, 
                                     url: str, 
                                     markdown: str,
                                     metadata: Optional[Dict[str, Any]] = None) -> Tuple[int, int]:
        """
        Process markdown content into chunks and store them immediately.
        
        Returns:
            Tuple of (chunks_created, chunks_stored)
        """
        from crawl4ai_mcp import smart_chunk_markdown
        
        # Create chunks
        chunks = smart_chunk_markdown(markdown, chunk_size=self.chunk_size)
        chunks_created = len(chunks)
        
        # Extract source information
        parsed_url = urlparse(url)
        source_id = parsed_url.netloc or parsed_url.path
        
        # Prepare chunk data
        chunk_data = []
        for i, chunk_content in enumerate(chunks):
            chunk_metadata = {
                "source": source_id,
                "chunk_number": i + 1,
                "total_chunks": chunks_created,
                "url": url,
                "timestamp": datetime.utcnow().isoformat(),
                **(metadata or {})
            }
            
            chunk_data.append({
                "url": url,
                "chunk_number": i + 1,
                "content": chunk_content,
                "metadata": chunk_metadata
            })
        
        # Store chunks in batches
        chunks_stored = 0
        for i in range(0, len(chunk_data), self.batch_size):
            batch = chunk_data[i:i + self.batch_size]
            chunks_stored += await self._store_chunk_batch(batch)
            
            # Small delay to prevent overwhelming the database
            await asyncio.sleep(0.1)
        
        return chunks_created, chunks_stored
    
    async def _store_chunk_batch(self, chunks: List[Dict[str, Any]]) -> int:
        """Store a batch of chunks to Supabase"""
        try:
            # Generate embeddings for batch
            if self.use_direct_embedding:
                embeddings = []
                for chunk in chunks:
                    embedding = generate_embedding(chunk["content"])
                    embeddings.append(embedding)
            else:
                # Use BGE service (implement if needed)
                embeddings = [None] * len(chunks)  # Placeholder
            
            # Prepare data for insertion
            insert_data = []
            for chunk, embedding in zip(chunks, embeddings):
                insert_data.append({
                    "url": chunk["url"],
                    "chunk_number": chunk["chunk_number"],
                    "content": chunk["content"],
                    "metadata": chunk["metadata"],
                    "embedding": embedding
                })
            
            # Insert into database
            response = self.supabase.table('crawled_pages').insert(insert_data).execute()
            
            return len(response.data) if response.data else 0
            
        except Exception as e:
            print(f"❌ Error storing chunk batch: {str(e)}")
            return 0


class StreamingCrawler:
    """
    Streaming crawler that processes results incrementally
    """
    
    def __init__(self, crawler, chunk_processor: StreamingChunkProcessor):
        self.crawler = crawler
        self.chunk_processor = chunk_processor
        self.progress = CrawlProgress()
        
    async def crawl_urls_streaming(self, 
                                 urls: List[str], 
                                 max_concurrent: int = 10,
                                 timeout: int = 30000) -> AsyncIterator[StreamingResult]:
        """
        Crawl URLs and yield results as they complete, processing chunks immediately.
        """
        from crawl4ai_mcp import robust_multi_crawl_with_monitoring
        
        # Update progress
        self.progress.total_urls = len(urls)
        
        # Create crawl tasks with monitoring
        crawl_result = await robust_multi_crawl_with_monitoring(
            self.crawler, urls, max_concurrent, base_timeout=timeout
        )
        
        # Process successful results as they arrive
        for result in crawl_result['successful_results']:
            start_time = time.time()
            streaming_result = StreamingResult(
                url=result.url,
                success=True,
                markdown=result.markdown
            )
            
            try:
                # Process and store chunks immediately
                chunks_created, chunks_stored = await self.chunk_processor.process_and_store_chunks(
                    result.url,
                    result.markdown
                )
                
                # Update progress
                self.progress.successful_urls += 1
                self.progress.chunks_created += chunks_created
                self.progress.chunks_stored += chunks_stored
                
                # Extract links if available
                if hasattr(result, 'links') and result.links:
                    streaming_result.links = result.links
                    
            except Exception as e:
                streaming_result.success = False
                streaming_result.error = str(e)
                self.progress.errors.append({
                    "url": result.url,
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                })
            
            streaming_result.processing_time = time.time() - start_time
            self.progress.processed_urls += 1
            
            # Yield the result immediately
            yield streaming_result
        
        # Process failed URLs
        for failed_url in crawl_result['failed_urls']:
            self.progress.failed_urls += 1
            self.progress.processed_urls += 1
            
            yield StreamingResult(
                url=failed_url,
                success=False,
                error="Failed to crawl"
            )
    
    async def crawl_recursive_streaming(self,
                                      start_urls: List[str],
                                      max_depth: int = 3,
                                      max_concurrent: int = 10,
                                      progress_callback: Optional[callable] = None) -> AsyncIterator[Dict[str, Any]]:
        """
        Recursively crawl with streaming processing and progress updates.
        """
        visited = set()
        current_urls = set(start_urls)
        
        try:
            for depth in range(max_depth):
                urls_to_crawl = [url for url in current_urls if url not in visited]
                if not urls_to_crawl:
                    break
                
                print(f"🕷️ Depth {depth + 1}/{max_depth}: Crawling {len(urls_to_crawl)} URLs")
                next_level_urls = set()
                
                # Stream process URLs at this depth
                async for result in self.crawl_urls_streaming(urls_to_crawl, max_concurrent):
                    visited.add(result.url)
                    
                    # Yield progress update
                    if progress_callback:
                        await progress_callback(self.progress)
                    
                    yield {
                        "type": "result",
                        "depth": depth + 1,
                        "result": result,
                        "progress": self.progress.to_dict()
                    }
                    
                    # Collect internal links for next depth
                    if result.success and result.links:
                        for link in result.links.get("internal", []):
                            if isinstance(link, dict) and "href" in link:
                                next_url = link["href"]
                                if next_url not in visited:
                                    next_level_urls.add(next_url)
                
                # Create checkpoint after each depth
                self.progress.last_checkpoint = {
                    "depth": depth + 1,
                    "visited": list(visited),
                    "next_urls": list(next_level_urls),
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                yield {
                    "type": "checkpoint",
                    "depth": depth + 1,
                    "checkpoint": self.progress.last_checkpoint,
                    "progress": self.progress.to_dict()
                }
                
                current_urls = next_level_urls
                
        except Exception as e:
            yield {
                "type": "error",
                "error": str(e),
                "progress": self.progress.to_dict()
            }
        
        # Final summary
        yield {
            "type": "complete",
            "progress": self.progress.to_dict(),
            "summary": {
                "total_pages": self.progress.successful_urls,
                "total_chunks": self.progress.chunks_stored,
                "total_errors": self.progress.failed_urls,
                "elapsed_time": self.progress.elapsed_time,
                "success_rate": self.progress.success_rate
            }
        }


class ResumableStreamingCrawler(StreamingCrawler):
    """
    Extended streaming crawler with checkpoint-based resumption capability
    """
    
    def __init__(self, crawler, chunk_processor: StreamingChunkProcessor, checkpoint_dir: str = "./checkpoints"):
        super().__init__(crawler, chunk_processor)
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(checkpoint_dir, exist_ok=True)
        
    async def save_checkpoint(self, crawl_id: str):
        """Save current progress to a checkpoint file"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{crawl_id}_checkpoint.json")
        checkpoint_data = {
            "crawl_id": crawl_id,
            "progress": self.progress.to_dict(),
            "checkpoint": self.progress.last_checkpoint,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        with open(checkpoint_file, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
            
    async def load_checkpoint(self, crawl_id: str) -> Optional[Dict[str, Any]]:
        """Load checkpoint data if it exists"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{crawl_id}_checkpoint.json")
        if os.path.exists(checkpoint_file):
            with open(checkpoint_file, 'r') as f:
                return json.load(f)
        return None
    
    async def resume_crawl(self, crawl_id: str, max_depth: int = 3, max_concurrent: int = 10):
        """Resume a crawl from checkpoint"""
        checkpoint = await self.load_checkpoint(crawl_id)
        if not checkpoint:
            raise ValueError(f"No checkpoint found for crawl_id: {crawl_id}")
        
        # Restore progress
        last_checkpoint = checkpoint["checkpoint"]
        visited = set(last_checkpoint["visited"])
        current_urls = set(last_checkpoint["next_urls"])
        start_depth = last_checkpoint["depth"]
        
        print(f"📥 Resuming crawl from depth {start_depth} with {len(current_urls)} URLs")
        
        # Continue crawling from checkpoint
        async for update in self._continue_crawl(
            current_urls, visited, start_depth, max_depth, max_concurrent
        ):
            yield update
            
            # Save checkpoint periodically
            if update["type"] == "checkpoint":
                await self.save_checkpoint(crawl_id)
    
    async def _continue_crawl(self, current_urls, visited, start_depth, max_depth, max_concurrent):
        """Continue crawling from a specific state"""
        # Implementation continues from the given state
        # Similar to crawl_recursive_streaming but starting from checkpoint
        pass  # To be implemented based on specific requirements