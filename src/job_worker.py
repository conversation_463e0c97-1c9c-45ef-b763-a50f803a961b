"""
Background Job Worker System for MCP Crawl4AI RAG Server

This module implements the background job processing system using Supabase PGMQ.
Workers poll the job queue and execute crawl operations asynchronously, providing
real-time progress updates and handling failures gracefully.
"""

import asyncio
import json
import logging
import signal
import sys
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path

from supabase import Client
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

# Add the src directory to the path so we can import local modules
src_path = Path(__file__).resolve().parent
sys.path.insert(0, str(src_path))

from job_manager import JobManager, JobType, JobStatus, get_job_manager
from utils import get_supabase_client, add_documents_to_supabase, add_code_examples_to_supabase, update_source_info

# Import knowledge graph modules if available
try:
    knowledge_graphs_path = Path(__file__).resolve().parent.parent / 'knowledge_graphs'
    sys.path.append(str(knowledge_graphs_path))
    from parse_repo_into_neo4j import DirectNeo4jExtractor
    KNOWLEDGE_GRAPH_AVAILABLE = True
except ImportError:
    KNOWLEDGE_GRAPH_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JobWorker:
    """
    Background worker that processes crawl jobs from the PGMQ queue.
    
    This worker handles different types of crawl jobs, provides real-time
    progress updates, and integrates with the existing crawler infrastructure.
    """
    
    def __init__(self, worker_id: str = "worker-1"):
        """Initialize the job worker."""
        self.worker_id = worker_id
        self.supabase = get_supabase_client()
        self.job_manager = get_job_manager()
        self.crawler: Optional[AsyncWebCrawler] = None
        self.running = False
        self.current_job_id: Optional[str] = None
        self.queue_name = "crawl_jobs_queue"
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info(f"Initialized job worker {worker_id}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    async def start(self):
        """Start the job worker main loop."""
        logger.info(f"Starting job worker {self.worker_id}")
        self.running = True
        
        # Initialize crawler
        await self._init_crawler()
        
        try:
            while self.running:
                try:
                    # Poll for jobs from the queue
                    job_message = await self._poll_job_queue()
                    
                    if job_message:
                        await self._process_job(job_message)
                    else:
                        # No jobs available, wait before polling again
                        await asyncio.sleep(5)
                        
                except Exception as e:
                    logger.error(f"Error in worker main loop: {str(e)}")
                    logger.error(traceback.format_exc())
                    await asyncio.sleep(10)  # Wait longer on error
                    
        finally:
            await self._cleanup()
            logger.info(f"Job worker {self.worker_id} stopped")
    
    async def _init_crawler(self):
        """Initialize the web crawler."""
        try:
            browser_config = BrowserConfig(
                headless=True,
                verbose=False
            )
            
            self.crawler = AsyncWebCrawler(
                config=browser_config,
                verbose=False
            )
            
            # Initialize crawler
            await self.crawler.__aenter__()
            logger.info("Crawler initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize crawler: {str(e)}")
            raise
    
    async def _cleanup(self):
        """Clean up resources."""
        if self.crawler:
            try:
                await self.crawler.__aexit__(None, None, None)
                logger.info("Crawler cleaned up")
            except Exception as e:
                logger.error(f"Error cleaning up crawler: {str(e)}")
    
    async def _poll_job_queue(self) -> Optional[Dict[str, Any]]:
        """Poll the PGMQ queue for new jobs."""
        try:
            # Use PGMQ read function to get next message
            result = self.supabase.rpc(
                "pgmq_read",
                {
                    "queue_name": self.queue_name,
                    "vt": 300,  # Visibility timeout in seconds (5 minutes)
                    "qty": 1    # Number of messages to read
                }
            ).execute()
            
            if result.data and len(result.data) > 0:
                message = result.data[0]
                return {
                    "msg_id": message["msg_id"],
                    "read_ct": message["read_ct"],
                    "enqueued_at": message["enqueued_at"],
                    "message": json.loads(message["message"])
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error polling job queue: {str(e)}")
            return None
    
    async def _process_job(self, job_message: Dict[str, Any]):
        """Process a single job from the queue."""
        msg_id = job_message["msg_id"]
        job_data = job_message["message"]
        job_id = job_data["job_id"]
        
        logger.info(f"Processing job {job_id} (message {msg_id})")
        self.current_job_id = job_id
        
        try:
            # Mark job as running
            await self.job_manager.mark_job_running(job_id)
            await self.job_manager.update_job_progress(
                job_id, "Starting job processing", 0, "Job picked up by worker"
            )
            
            # Route to appropriate handler based on job type
            job_type = JobType(job_data["job_type"])
            parameters = job_data["parameters"]
            
            if job_type == JobType.SINGLE_PAGE:
                await self._handle_single_page_job(job_id, parameters)
            elif job_type == JobType.SMART_CRAWL:
                await self._handle_smart_crawl_job(job_id, parameters)
            elif job_type == JobType.SITEMAP_CRAWL:
                await self._handle_sitemap_crawl_job(job_id, parameters)
            elif job_type == JobType.TEXT_FILE_CRAWL:
                await self._handle_text_file_crawl_job(job_id, parameters)
            elif job_type == JobType.REPOSITORY_PARSE:
                await self._handle_repository_parse_job(job_id, parameters)
            else:
                await self.job_manager.fail_job(job_id, f"Unknown job type: {job_type.value}")
            
            # Delete message from queue after successful processing
            self.supabase.rpc("pgmq_delete", {
                "queue_name": self.queue_name,
                "msg_id": msg_id
            }).execute()
            
            logger.info(f"Successfully processed job {job_id}")
            
        except Exception as e:
            logger.error(f"Error processing job {job_id}: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Mark job as failed and leave message in queue for potential retry
            await self.job_manager.fail_job(job_id, f"Job processing failed: {str(e)}")
            
        finally:
            self.current_job_id = None
    
    async def _handle_single_page_job(self, job_id: str, parameters: Dict[str, Any]):
        """Handle single page crawl job."""
        url = parameters["url"]
        chunk_size = parameters.get("chunk_size", 5000)
        
        await self.job_manager.update_job_progress(
            job_id, "Crawling single page", 10, f"Starting crawl of {url}"
        )
        
        # Crawl the page
        run_config = CrawlerRunConfig(
            word_count_threshold=10,
            extraction_strategy="NoExtractionStrategy",
            chunking_strategy="RegexChunking",
            cache_mode=CacheMode.BYPASS
        )
        
        await self.job_manager.update_job_progress(
            job_id, "Fetching page content", 30, "Downloading and processing page"
        )
        
        result = await self.crawler.arun(url=url, config=run_config)
        
        if not result.success:
            raise Exception(f"Failed to crawl page: {result.error_message}")
        
        await self.job_manager.update_job_progress(
            job_id, "Processing content", 60, "Chunking and extracting content"
        )
        
        # Process and store content
        if result.markdown:
            chunks = self._chunk_content(result.markdown, chunk_size)
            
            await self.job_manager.update_job_progress(
                job_id, "Storing content", 80, f"Storing {len(chunks)} chunks"
            )
            
            # Store in Supabase
            await add_documents_to_supabase(url, chunks, source_id=self._get_source_id(url))
            
            # Update source info
            await update_source_info(
                source_id=self._get_source_id(url),
                summary=f"Single page crawl of {url}",
                word_count=len(result.markdown.split())
            )
            
            await self.job_manager.complete_job(
                job_id,
                result_summary={
                    "url": url,
                    "chunks_created": len(chunks),
                    "content_length": len(result.markdown),
                    "word_count": len(result.markdown.split())
                },
                pages_crawled=1,
                chunks_created=len(chunks)
            )
        else:
            raise Exception("No content extracted from page")
    
    async def _handle_smart_crawl_job(self, job_id: str, parameters: Dict[str, Any]):
        """Handle smart crawl job (multi-page intelligent crawl)."""
        url = parameters["url"]
        max_depth = parameters.get("max_depth", 3)
        max_concurrent = parameters.get("max_concurrent", 10)
        chunk_size = parameters.get("chunk_size", 5000)
        
        await self.job_manager.update_job_progress(
            job_id, "Analyzing URL type", 5, f"Determining crawl strategy for {url}"
        )
        
        # Determine crawl type based on URL
        if url.endswith('.xml') or 'sitemap' in url.lower():
            await self._crawl_sitemap_internal(job_id, url, chunk_size)
        elif url.endswith('.txt'):
            await self._crawl_text_file_internal(job_id, url, chunk_size)
        else:
            await self._crawl_recursive_internal(job_id, url, max_depth, max_concurrent, chunk_size)
    
    async def _handle_sitemap_crawl_job(self, job_id: str, parameters: Dict[str, Any]):
        """Handle sitemap crawl job."""
        url = parameters["url"]
        chunk_size = parameters.get("chunk_size", 5000)
        await self._crawl_sitemap_internal(job_id, url, chunk_size)
    
    async def _handle_text_file_crawl_job(self, job_id: str, parameters: Dict[str, Any]):
        """Handle text file crawl job."""
        url = parameters["url"]
        chunk_size = parameters.get("chunk_size", 5000)
        await self._crawl_text_file_internal(job_id, url, chunk_size)
    
    async def _handle_repository_parse_job(self, job_id: str, parameters: Dict[str, Any]):
        """Handle GitHub repository parsing job."""
        if not KNOWLEDGE_GRAPH_AVAILABLE:
            raise Exception("Knowledge graph functionality not available")
        
        repo_url = parameters["url"]
        
        await self.job_manager.update_job_progress(
            job_id, "Initializing repository parser", 10, f"Setting up parser for {repo_url}"
        )
        
        try:
            extractor = DirectNeo4jExtractor()
            
            await self.job_manager.update_job_progress(
                job_id, "Cloning repository", 30, "Downloading repository content"
            )
            
            result = await extractor.parse_repository(repo_url)
            
            await self.job_manager.update_job_progress(
                job_id, "Parsing code structure", 70, "Extracting classes and methods"
            )
            
            await self.job_manager.complete_job(
                job_id,
                result_summary={
                    "repository_url": repo_url,
                    "files_processed": result.get("files_processed", 0),
                    "classes_extracted": result.get("classes_extracted", 0),
                    "methods_extracted": result.get("methods_extracted", 0)
                },
                pages_crawled=result.get("files_processed", 0),
                chunks_created=result.get("total_extractions", 0)
            )
            
        except Exception as e:
            raise Exception(f"Repository parsing failed: {str(e)}")
    
    async def _crawl_sitemap_internal(self, job_id: str, sitemap_url: str, chunk_size: int):
        """Internal sitemap crawling implementation."""
        await self.job_manager.update_job_progress(
            job_id, "Parsing sitemap", 20, f"Extracting URLs from {sitemap_url}"
        )
        
        # Fetch and parse sitemap
        import requests
        import xml.etree.ElementTree as ET
        
        try:
            response = requests.get(sitemap_url, timeout=30)
            response.raise_for_status()
            
            root = ET.fromstring(response.content)
            urls = []
            
            # Handle different sitemap formats
            for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                if loc_elem is not None and loc_elem.text:
                    urls.append(loc_elem.text)
            
            if not urls:
                # Try without namespace
                for url_elem in root.findall('.//url'):
                    loc_elem = url_elem.find('loc')
                    if loc_elem is not None and loc_elem.text:
                        urls.append(loc_elem.text)
            
            await self.job_manager.update_job_progress(
                job_id, "Crawling sitemap URLs", 40, f"Found {len(urls)} URLs to crawl"
            )
            
            # Crawl URLs in batches
            total_chunks = 0
            crawled_pages = 0
            
            for i, url in enumerate(urls):
                try:
                    progress = 40 + (50 * (i + 1) // len(urls))
                    await self.job_manager.update_job_progress(
                        job_id, f"Crawling URL {i+1}/{len(urls)}", progress, f"Processing {url}"
                    )
                    
                    run_config = CrawlerRunConfig(
                        word_count_threshold=10,
                        extraction_strategy="NoExtractionStrategy",
                        chunking_strategy="RegexChunking",
                        cache_mode=CacheMode.BYPASS
                    )
                    
                    result = await self.crawler.arun(url=url, config=run_config)
                    
                    if result.success and result.markdown:
                        chunks = self._chunk_content(result.markdown, chunk_size)
                        await add_documents_to_supabase(
                            url, chunks, source_id=self._get_source_id(sitemap_url)
                        )
                        total_chunks += len(chunks)
                        crawled_pages += 1
                    
                except Exception as e:
                    logger.warning(f"Failed to crawl URL {url}: {str(e)}")
                    continue
            
            # Update source info
            await update_source_info(
                source_id=self._get_source_id(sitemap_url),
                summary=f"Sitemap crawl of {sitemap_url} ({crawled_pages} pages)",
                word_count=total_chunks * 100  # Rough estimate
            )
            
            await self.job_manager.complete_job(
                job_id,
                result_summary={
                    "sitemap_url": sitemap_url,
                    "total_urls_found": len(urls),
                    "successfully_crawled": crawled_pages,
                    "total_chunks": total_chunks
                },
                pages_crawled=crawled_pages,
                chunks_created=total_chunks
            )
            
        except Exception as e:
            raise Exception(f"Sitemap crawling failed: {str(e)}")
    
    async def _crawl_text_file_internal(self, job_id: str, text_url: str, chunk_size: int):
        """Internal text file crawling implementation."""
        await self.job_manager.update_job_progress(
            job_id, "Downloading text file", 30, f"Fetching content from {text_url}"
        )
        
        try:
            import requests
            response = requests.get(text_url, timeout=30)
            response.raise_for_status()
            
            content = response.text
            
            await self.job_manager.update_job_progress(
                job_id, "Processing text content", 60, "Chunking text content"
            )
            
            chunks = self._chunk_content(content, chunk_size)
            
            await self.job_manager.update_job_progress(
                job_id, "Storing content", 80, f"Storing {len(chunks)} chunks"
            )
            
            await add_documents_to_supabase(
                text_url, chunks, source_id=self._get_source_id(text_url)
            )
            
            await update_source_info(
                source_id=self._get_source_id(text_url),
                summary=f"Text file crawl of {text_url}",
                word_count=len(content.split())
            )
            
            await self.job_manager.complete_job(
                job_id,
                result_summary={
                    "url": text_url,
                    "content_length": len(content),
                    "chunks_created": len(chunks),
                    "word_count": len(content.split())
                },
                pages_crawled=1,
                chunks_created=len(chunks)
            )
            
        except Exception as e:
            raise Exception(f"Text file crawling failed: {str(e)}")
    
    async def _crawl_recursive_internal(self, job_id: str, start_url: str, max_depth: int, max_concurrent: int, chunk_size: int):
        """Internal recursive crawling implementation."""
        await self.job_manager.update_job_progress(
            job_id, "Starting recursive crawl", 10, f"Crawling {start_url} with depth {max_depth}"
        )
        
        # This is a simplified version - in production you'd want the full
        # recursive crawling logic from the main crawler
        visited = set()
        current_urls = {start_url}
        total_chunks = 0
        crawled_pages = 0
        
        for depth in range(max_depth):
            if not current_urls or not self.running:
                break
            
            progress = 10 + (70 * (depth + 1) // max_depth)
            await self.job_manager.update_job_progress(
                job_id, f"Crawling depth {depth + 1}/{max_depth}", progress,
                f"Processing {len(current_urls)} URLs at depth {depth + 1}"
            )
            
            next_urls = set()
            
            for url in current_urls:
                if url in visited:
                    continue
                
                try:
                    run_config = CrawlerRunConfig(
                        word_count_threshold=10,
                        extraction_strategy="NoExtractionStrategy",
                        chunking_strategy="RegexChunking",
                        cache_mode=CacheMode.BYPASS
                    )
                    
                    result = await self.crawler.arun(url=url, config=run_config)
                    visited.add(url)
                    
                    if result.success and result.markdown:
                        chunks = self._chunk_content(result.markdown, chunk_size)
                        await add_documents_to_supabase(
                            url, chunks, source_id=self._get_source_id(start_url)
                        )
                        total_chunks += len(chunks)
                        crawled_pages += 1
                        
                        # Extract links for next depth (simplified)
                        if hasattr(result, 'links') and result.links:
                            for link in result.links.get("internal", []):
                                if isinstance(link, dict) and "href" in link:
                                    next_urls.add(link["href"])
                
                except Exception as e:
                    logger.warning(f"Failed to crawl URL {url}: {str(e)}")
                    continue
            
            current_urls = next_urls - visited
        
        await update_source_info(
            source_id=self._get_source_id(start_url),
            summary=f"Recursive crawl of {start_url} (depth {max_depth}, {crawled_pages} pages)",
            word_count=total_chunks * 100  # Rough estimate
        )
        
        await self.job_manager.complete_job(
            job_id,
            result_summary={
                "start_url": start_url,
                "max_depth": max_depth,
                "pages_crawled": crawled_pages,
                "total_chunks": total_chunks
            },
            pages_crawled=crawled_pages,
            chunks_created=total_chunks
        )
    
    def _chunk_content(self, content: str, chunk_size: int) -> List[str]:
        """Chunk content into manageable pieces."""
        if not content:
            return []
        
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            
            # Try to break at sentence boundaries
            if end < len(content):
                last_period = content.rfind('. ', start, end)
                if last_period > start + chunk_size * 0.3:
                    end = last_period + 1
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end
        
        return chunks
    
    def _get_source_id(self, url: str) -> str:
        """Extract source ID from URL."""
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return parsed.netloc

async def main():
    """Main entry point for the job worker."""
    import os
    
    worker_id = os.getenv("WORKER_ID", f"worker-{os.getpid()}")
    
    worker = JobWorker(worker_id)
    
    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error(f"Worker failed: {str(e)}")
        logger.error(traceback.format_exc())
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))