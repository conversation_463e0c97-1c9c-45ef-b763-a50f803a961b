"""
Monitoring and Resilience Module for Streaming Crawls

Provides comprehensive monitoring, metrics collection, and resilience features
for large-scale web crawling operations.
"""

import time
import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque, defaultdict
import statistics
import os
import logging
from enum import Enum


class HealthStatus(Enum):
    """Health status levels for monitoring"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    CRITICAL = "critical"
    FAILED = "failed"


@dataclass
class PerformanceMetrics:
    """Track detailed performance metrics"""
    
    # Timing metrics
    crawl_times: deque = field(default_factory=lambda: deque(maxlen=100))
    chunk_times: deque = field(default_factory=lambda: deque(maxlen=100))
    store_times: deque = field(default_factory=lambda: deque(maxlen=100))
    
    # Rate metrics
    pages_per_minute: deque = field(default_factory=lambda: deque(maxlen=10))
    chunks_per_minute: deque = field(default_factory=lambda: deque(maxlen=10))
    bytes_per_second: deque = field(default_factory=lambda: deque(maxlen=10))
    
    # Error tracking
    error_counts: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    consecutive_errors: int = 0
    last_error_time: Optional[float] = None
    
    # Resource usage
    memory_usage: deque = field(default_factory=lambda: deque(maxlen=50))
    active_connections: int = 0
    queue_depth: int = 0
    
    def add_crawl_time(self, duration: float):
        """Record crawl duration"""
        self.crawl_times.append(duration)
        
    def add_chunk_time(self, duration: float):
        """Record chunk processing time"""
        self.chunk_times.append(duration)
        
    def add_store_time(self, duration: float):
        """Record storage operation time"""
        self.store_times.append(duration)
        
    def record_error(self, error_type: str):
        """Record an error occurrence"""
        self.error_counts[error_type] += 1
        self.consecutive_errors += 1
        self.last_error_time = time.time()
        
    def reset_consecutive_errors(self):
        """Reset consecutive error counter on success"""
        self.consecutive_errors = 0
        
    @property
    def avg_crawl_time(self) -> float:
        """Average crawl time in seconds"""
        return statistics.mean(self.crawl_times) if self.crawl_times else 0
        
    @property
    def avg_chunk_time(self) -> float:
        """Average chunk processing time"""
        return statistics.mean(self.chunk_times) if self.chunk_times else 0
        
    @property
    def avg_store_time(self) -> float:
        """Average storage time"""
        return statistics.mean(self.store_times) if self.store_times else 0
        
    @property
    def error_rate(self) -> float:
        """Error rate over last 100 operations"""
        total_errors = sum(self.error_counts.values())
        total_operations = len(self.crawl_times)
        return (total_errors / total_operations * 100) if total_operations > 0 else 0
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary"""
        return {
            "avg_crawl_time": self.avg_crawl_time,
            "avg_chunk_time": self.avg_chunk_time,
            "avg_store_time": self.avg_store_time,
            "error_rate": self.error_rate,
            "consecutive_errors": self.consecutive_errors,
            "active_connections": self.active_connections,
            "queue_depth": self.queue_depth,
            "total_errors": sum(self.error_counts.values()),
            "error_breakdown": dict(self.error_counts)
        }


class CircuitBreaker:
    """
    Circuit breaker pattern for handling cascading failures
    """
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: int = 60,
                 half_open_requests: int = 3):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_requests = half_open_requests
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
        self.half_open_attempts = 0
        
    def record_success(self):
        """Record a successful operation"""
        if self.state == "half-open":
            self.half_open_attempts += 1
            if self.half_open_attempts >= self.half_open_requests:
                self.state = "closed"
                self.failure_count = 0
                self.half_open_attempts = 0
        else:
            self.failure_count = 0
            
    def record_failure(self):
        """Record a failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            
        if self.state == "half-open":
            self.state = "open"
            self.half_open_attempts = 0
            
    def can_proceed(self) -> bool:
        """Check if operation can proceed"""
        if self.state == "closed":
            return True
            
        if self.state == "open":
            if self.last_failure_time and \
               time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "half-open"
                self.half_open_attempts = 0
                return True
            return False
            
        # half-open state
        return self.half_open_attempts < self.half_open_requests
        
    @property
    def status(self) -> Dict[str, Any]:
        """Get circuit breaker status"""
        return {
            "state": self.state,
            "failure_count": self.failure_count,
            "can_proceed": self.can_proceed()
        }


class AdaptiveThrottler:
    """
    Adaptive throttling based on system performance
    """
    
    def __init__(self,
                 min_delay: float = 0.1,
                 max_delay: float = 5.0,
                 target_success_rate: float = 0.95):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.target_success_rate = target_success_rate
        
        self.current_delay = min_delay
        self.success_window = deque(maxlen=20)
        
    def record_result(self, success: bool):
        """Record operation result"""
        self.success_window.append(1 if success else 0)
        self._adjust_delay()
        
    def _adjust_delay(self):
        """Adjust delay based on success rate"""
        if len(self.success_window) < 10:
            return
            
        success_rate = sum(self.success_window) / len(self.success_window)
        
        if success_rate < self.target_success_rate:
            # Increase delay (slow down)
            self.current_delay = min(self.current_delay * 1.2, self.max_delay)
        elif success_rate > self.target_success_rate and len(self.success_window) == 20:
            # Decrease delay (speed up) only with full window
            self.current_delay = max(self.current_delay * 0.9, self.min_delay)
            
    async def wait(self):
        """Wait for the current delay period"""
        await asyncio.sleep(self.current_delay)
        
    @property
    def status(self) -> Dict[str, Any]:
        """Get throttler status"""
        success_rate = sum(self.success_window) / len(self.success_window) \
                      if self.success_window else 0
        return {
            "current_delay": self.current_delay,
            "success_rate": success_rate,
            "window_size": len(self.success_window)
        }


class CrawlMonitor:
    """
    Comprehensive monitoring system for crawl operations
    """
    
    def __init__(self, 
                 alert_callback: Optional[Callable] = None,
                 log_file: Optional[str] = None):
        self.metrics = PerformanceMetrics()
        self.circuit_breakers = {}
        self.throttlers = {}
        self.alert_callback = alert_callback
        
        # Setup logging
        if log_file:
            logging.basicConfig(
                filename=log_file,
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
        self.logger = logging.getLogger(__name__)
        
        # Alert thresholds
        self.thresholds = {
            "error_rate": 10.0,  # percent
            "consecutive_errors": 10,
            "avg_crawl_time": 60.0,  # seconds
            "memory_usage": 1024 * 1024 * 1024,  # 1GB
            "queue_depth": 1000
        }
        
        # Health check state
        self.last_health_check = time.time()
        self.health_status = HealthStatus.HEALTHY
        
    def get_circuit_breaker(self, component: str) -> CircuitBreaker:
        """Get or create circuit breaker for component"""
        if component not in self.circuit_breakers:
            self.circuit_breakers[component] = CircuitBreaker()
        return self.circuit_breakers[component]
        
    def get_throttler(self, component: str) -> AdaptiveThrottler:
        """Get or create throttler for component"""
        if component not in self.throttlers:
            self.throttlers[component] = AdaptiveThrottler()
        return self.throttlers[component]
        
    async def record_crawl(self, url: str, duration: float, success: bool, error: Optional[str] = None):
        """Record crawl operation metrics"""
        self.metrics.add_crawl_time(duration)
        
        cb = self.get_circuit_breaker("crawler")
        throttler = self.get_throttler("crawler")
        
        if success:
            cb.record_success()
            self.metrics.reset_consecutive_errors()
        else:
            cb.record_failure()
            if error:
                self.metrics.record_error(error)
                
        throttler.record_result(success)
        
        # Log operation
        self.logger.info(f"Crawl: {url} - Success: {success} - Duration: {duration:.2f}s")
        
        # Check for alerts
        await self._check_alerts()
        
    async def record_chunk_processing(self, chunks: int, duration: float):
        """Record chunk processing metrics"""
        self.metrics.add_chunk_time(duration)
        self.metrics.chunks_per_minute.append(chunks / (duration / 60) if duration > 0 else 0)
        
    async def record_storage(self, duration: float, success: bool):
        """Record storage operation metrics"""
        self.metrics.add_store_time(duration)
        
        cb = self.get_circuit_breaker("storage")
        if success:
            cb.record_success()
        else:
            cb.record_failure()
            
    async def update_resource_usage(self, active_connections: int, queue_depth: int):
        """Update resource usage metrics"""
        self.metrics.active_connections = active_connections
        self.metrics.queue_depth = queue_depth
        
        # Check memory usage
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.metrics.memory_usage.append(memory_mb)
        except:
            pass
            
    async def _check_alerts(self):
        """Check if any thresholds are exceeded"""
        alerts = []
        
        # Check error rate
        if self.metrics.error_rate > self.thresholds["error_rate"]:
            alerts.append({
                "level": "warning",
                "metric": "error_rate",
                "value": self.metrics.error_rate,
                "threshold": self.thresholds["error_rate"]
            })
            
        # Check consecutive errors
        if self.metrics.consecutive_errors > self.thresholds["consecutive_errors"]:
            alerts.append({
                "level": "critical",
                "metric": "consecutive_errors",
                "value": self.metrics.consecutive_errors,
                "threshold": self.thresholds["consecutive_errors"]
            })
            
        # Check crawl time
        if self.metrics.avg_crawl_time > self.thresholds["avg_crawl_time"]:
            alerts.append({
                "level": "warning",
                "metric": "avg_crawl_time",
                "value": self.metrics.avg_crawl_time,
                "threshold": self.thresholds["avg_crawl_time"]
            })
            
        # Send alerts
        if alerts and self.alert_callback:
            await self.alert_callback(alerts)
            
        # Update health status
        if any(a["level"] == "critical" for a in alerts):
            self.health_status = HealthStatus.CRITICAL
        elif alerts:
            self.health_status = HealthStatus.DEGRADED
        else:
            self.health_status = HealthStatus.HEALTHY
            
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        return {
            "status": self.health_status.value,
            "metrics": self.metrics.to_dict(),
            "circuit_breakers": {
                name: cb.status for name, cb in self.circuit_breakers.items()
            },
            "throttlers": {
                name: t.status for name, t in self.throttlers.items()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get data for monitoring dashboard"""
        return {
            "health": self.get_health_status(),
            "performance": {
                "avg_crawl_time": self.metrics.avg_crawl_time,
                "avg_chunk_time": self.metrics.avg_chunk_time,
                "avg_store_time": self.metrics.avg_store_time,
                "pages_per_minute": statistics.mean(self.metrics.pages_per_minute) 
                                   if self.metrics.pages_per_minute else 0,
                "chunks_per_minute": statistics.mean(self.metrics.chunks_per_minute)
                                   if self.metrics.chunks_per_minute else 0
            },
            "errors": {
                "total": sum(self.metrics.error_counts.values()),
                "rate": self.metrics.error_rate,
                "consecutive": self.metrics.consecutive_errors,
                "breakdown": dict(self.metrics.error_counts)
            },
            "resources": {
                "active_connections": self.metrics.active_connections,
                "queue_depth": self.metrics.queue_depth,
                "avg_memory_mb": statistics.mean(self.metrics.memory_usage)
                               if self.metrics.memory_usage else 0
            }
        }