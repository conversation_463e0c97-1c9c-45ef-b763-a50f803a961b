"""
MCP server for web crawling with Crawl4AI.

This server provides tools to crawl websites using Crawl4AI, automatically detecting
the appropriate crawl method based on URL type (sitemap, txt file, or regular webpage).
Also includes AI hallucination detection and repository parsing tools using Neo4j knowledge graphs.
"""
from mcp.server.fastmcp import FastMCP, Context
from sentence_transformers import CrossEncoder
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse, urldefrag
from xml.etree import ElementTree
from dotenv import load_dotenv
from supabase import Client
from pathlib import Path
import requests
import asyncio
import json
import os
import re
import concurrent.futures
import sys

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher
from crawl4ai.async_crawler_strategy import (
    ConnectionTimeoutError, 
    HTTPStatusError, 
    HTTPCrawlerError
)

# Add knowledge_graphs folder to path for importing knowledge graph modules
knowledge_graphs_path = Path(__file__).resolve().parent.parent / 'knowledge_graphs'
sys.path.append(str(knowledge_graphs_path))

from utils import (
    get_supabase_client, 
    add_documents_to_supabase, 
    search_documents,
    extract_code_blocks,
    generate_code_example_summary,
    add_code_examples_to_supabase,
    update_source_info,
    extract_source_summary,
    search_code_examples
)

# Import job management system
from job_manager import JobManager, JobType, JobStatus, get_job_manager

# Add imports for datetime handling in job tools
from datetime import datetime, timezone
import logging

# Configure logging for job tools
logger = logging.getLogger(__name__)

# Global variables for shared resources (initialized in lifespan)
_crawler = None
_reranking_model = None
_knowledge_validator = None
_repo_extractor = None
_error_recovery = None

def get_crawler():
    """Safely get the crawler instance."""
    global _crawler
    if _crawler is None:
        raise RuntimeError("Crawler not initialized. Server may be starting up or shutting down.")
    return _crawler

def get_reranking_model():
    """Safely get the reranking model instance."""
    global _reranking_model
    return _reranking_model  # Can be None if reranking is disabled

def get_knowledge_validator():
    """Safely get the knowledge validator instance."""
    global _knowledge_validator
    return _knowledge_validator  # Can be None if knowledge graph is disabled

def get_repo_extractor():
    """Safely get the repository extractor instance."""
    global _repo_extractor
    return _repo_extractor  # Can be None if knowledge graph is disabled

def get_error_recovery():
    """Safely get the error recovery manager instance."""
    global _error_recovery
    if _error_recovery is None:
        raise RuntimeError("Error recovery manager not initialized. Server may be starting up or shutting down.")
    return _error_recovery

# Import knowledge graph modules
from knowledge_graph_validator import KnowledgeGraphValidator
from parse_repo_into_neo4j import DirectNeo4jExtractor
from ai_script_analyzer import AIScriptAnalyzer
from hallucination_reporter import HallucinationReporter

# Load environment variables from the project root .env file
project_root = Path(__file__).resolve().parent.parent
dotenv_path = project_root / '.env'

# Force override of existing environment variables
load_dotenv(dotenv_path, override=True)

# Helper functions for robust crawling with retry logic
async def robust_crawl_with_retry(crawler: AsyncWebCrawler, url: str, max_retries: int = 3, base_timeout: int = 30000) -> Any:
    """
    Crawl a URL with robust error handling and retry logic.
    
    Args:
        crawler: AsyncWebCrawler instance
        url: URL to crawl
        max_retries: Maximum number of retry attempts (default: 3)
        base_timeout: Base timeout in milliseconds (default: 30 seconds)
    
    Returns:
        CrawlResult or raises exception after max retries
    """
    for attempt in range(max_retries + 1):  # +1 for initial attempt
        # Progressive timeout increase: 30s, 45s, 60s, 90s
        timeout = base_timeout + (attempt * 15000)
        
        try:
            # Configure crawl with timeout protection and error handling
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                stream=False,
                page_timeout=timeout,  # Progressive timeout
                user_agent_mode="random",  # Randomize user agents
                simulate_user=True  # Simulate human behavior
            )
            
            print(f"🔄 Crawling attempt {attempt + 1}/{max_retries + 1} for {url} (timeout: {timeout/1000:.1f}s)")
            result = await crawler.arun(url=url, config=run_config)
            
            if result.success:
                print(f"✅ Successfully crawled {url} on attempt {attempt + 1}")
                return result
            else:
                print(f"❌ Crawl failed: {result.error_message}")
                if attempt == max_retries:
                    # Final attempt failed, return the failed result
                    return result
                
        except ConnectionTimeoutError as e:
            print(f"⏰ Timeout error on attempt {attempt + 1}: {e}")
            if attempt == max_retries:
                raise
            # Wait with exponential backoff: 2^attempt seconds
            await asyncio.sleep(2 ** attempt)
            
        except HTTPStatusError as e:
            print(f"🌐 HTTP error on attempt {attempt + 1}: HTTP {e.status_code}")
            # Don't retry client errors (4xx), only server errors (5xx)
            if hasattr(e, 'status_code') and 400 <= e.status_code < 500:
                raise
            if attempt == max_retries:
                raise
            await asyncio.sleep(2 ** attempt)
            
        except HTTPCrawlerError as e:
            print(f"🕷️ Crawler error on attempt {attempt + 1}: {e}")
            if attempt == max_retries:
                raise
            await asyncio.sleep(2 ** attempt)
            
        except Exception as e:
            print(f"🚨 Unexpected error on attempt {attempt + 1}: {e}")
            if attempt == max_retries:
                raise
            await asyncio.sleep(2 ** attempt)

async def robust_multi_crawl_with_monitoring(crawler: AsyncWebCrawler, urls: List[str], max_concurrent: int = 10, base_timeout: int = 30000) -> Dict[str, Any]:
    """
    Crawl multiple URLs with robust error handling and progress monitoring.
    
    Args:
        crawler: AsyncWebCrawler instance
        urls: List of URLs to crawl
        max_concurrent: Maximum concurrent sessions
        base_timeout: Base timeout in milliseconds
    
    Returns:
        Dictionary with successful results, failed URLs, and statistics
    """
    failed_urls = []
    successful_results = []
    
    # Configure crawl with timeout protection
    config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        stream=True,
        page_timeout=base_timeout,
        user_agent_mode="random",
        simulate_user=True
    )
    
    # Configure dispatcher with memory management
    dispatcher = MemoryAdaptiveDispatcher(
        memory_threshold_percent=85.0,
        max_session_permit=max_concurrent
    )
    
    print(f"🚀 Starting robust multi-crawl for {len(urls)} URLs (concurrent: {max_concurrent}, timeout: {base_timeout/1000:.1f}s)")
    
    try:
        async for result in await crawler.arun_many(
            urls=urls,
            config=config,
            dispatcher=dispatcher
        ):
            if result.success:
                successful_results.append(result)
                print(f"✅ Successfully crawled: {result.url} ({len(result.markdown)} chars)")
            else:
                failed_info = {
                    'url': result.url,
                    'error': result.error_message,
                    'status_code': getattr(result, 'status_code', None),
                    'retry_eligible': should_retry_error(result)
                }
                failed_urls.append(failed_info)
                print(f"❌ Failed to crawl: {result.url} - {result.error_message}")
                
                # Auto-retry specific errors
                if failed_info['retry_eligible']:
                    print(f"🔄 Scheduling retry for: {result.url}")
                    try:
                        retry_result = await robust_crawl_with_retry(crawler, result.url, max_retries=2)
                        if retry_result.success:
                            successful_results.append(retry_result)
                            print(f"✅ Retry successful: {result.url}")
                            # Remove from failed list
                            failed_urls.remove(failed_info)
                        else:
                            print(f"❌ Retry also failed: {result.url}")
                    except Exception as e:
                        print(f"🚨 Retry error for {result.url}: {e}")
    
    except Exception as e:
        print(f"🚨 Multi-crawl error: {e}")
        raise
    
    return {
        'successful_results': successful_results,
        'failed_urls': failed_urls,
        'stats': {
            'total_urls': len(urls),
            'successful': len(successful_results),
            'failed': len(failed_urls),
            'success_rate': len(successful_results) / len(urls) * 100 if urls else 0
        }
    }

def should_retry_error(result) -> bool:
    """Determine if an error should trigger an automatic retry."""
    if not hasattr(result, 'status_code') or not result.status_code:
        return True  # No status code, might be network issue
    
    # Retry server errors (5xx) but not client errors (4xx)
    if 500 <= result.status_code < 600:
        return True
    
    # Retry specific codes that might be temporary
    if result.status_code in [429, 503, 502, 504]:  # Rate limit, service unavailable, bad gateway, gateway timeout
        return True
    
    return False

class ErrorRecoveryManager:
    """Manages error recovery, partial results, and retry queues."""
    
    def __init__(self, supabase_client: Client):
        self.supabase_client = supabase_client
        self.retry_queue: List[Dict[str, Any]] = []
        self.partial_results: List[Dict[str, Any]] = []
    
    async def save_partial_results(self, results: List[Dict[str, Any]], source_id: str, operation_type: str = "crawl") -> Dict[str, Any]:
        """Save partial crawl results even if the full operation fails."""
        if not results:
            return {"saved": 0, "errors": 0}
        
        saved_count = 0
        errors = []
        
        print(f"💾 Saving {len(results)} partial results for {source_id}...")
        
        try:
            for result in results:
                try:
                    if result.get('markdown') and result.get('url'):
                        # Extract source_id
                        parsed_url = urlparse(result['url'])
                        source_id_current = parsed_url.netloc or parsed_url.path
                        
                        # Chunk the content
                        chunks = smart_chunk_markdown(result['markdown'])
                        
                        # Prepare data for Supabase
                        urls = []
                        chunk_numbers = []
                        contents = []
                        metadatas = []
                        total_word_count = 0
                        
                        for i, chunk in enumerate(chunks):
                            urls.append(result['url'])
                            chunk_numbers.append(i)
                            contents.append(chunk)
                            
                            # Extract metadata
                            meta = extract_section_info(chunk)
                            meta["chunk_index"] = i
                            meta["url"] = result['url']
                            meta["source"] = source_id_current
                            meta["operation_type"] = operation_type
                            meta["partial_save"] = True
                            meta["save_timestamp"] = asyncio.get_event_loop().time()
                            metadatas.append(meta)
                            
                            # Accumulate word count
                            total_word_count += meta.get("word_count", 0)
                        
                        # Create url_to_full_document mapping
                        url_to_full_document = {result['url']: result['markdown']}
                        
                        # Update source information
                        source_summary = extract_source_summary(source_id_current, result['markdown'][:5000])
                        update_source_info(self.supabase_client, source_id_current, source_summary, total_word_count)
                        
                        # Add documentation chunks to Supabase
                        add_documents_to_supabase(self.supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document)
                        
                        saved_count += 1
                        print(f"✅ Saved partial result for {result['url']} ({len(chunks)} chunks)")
                        
                except Exception as e:
                    error_msg = f"Error saving partial result for {result.get('url', 'unknown')}: {str(e)}"
                    errors.append(error_msg)
                    print(f"❌ {error_msg}")
        
        except Exception as e:
            error_msg = f"Critical error in save_partial_results: {str(e)}"
            errors.append(error_msg)
            print(f"🚨 {error_msg}")
        
        result = {
            "saved": saved_count,
            "errors": len(errors),
            "error_details": errors[:5] if errors else []  # Limit error details
        }
        
        print(f"💾 Partial save complete: {saved_count} saved, {len(errors)} errors")
        return result
    
    def schedule_retry(self, url: str, error: str, retry_count: int = 0, metadata: Dict[str, Any] = None) -> bool:
        """Schedule a URL for retry with intelligent backoff."""
        max_retries = 3
        
        if retry_count >= max_retries:
            print(f"🚫 Max retries reached for {url}, not scheduling retry")
            return False
        
        # Calculate backoff delay: 2^retry_count minutes, max 60 minutes
        delay_minutes = min(2 ** retry_count, 60)
        retry_time = asyncio.get_event_loop().time() + (delay_minutes * 60)
        
        retry_item = {
            'url': url,
            'error': error,
            'retry_count': retry_count + 1,
            'scheduled_time': retry_time,
            'delay_minutes': delay_minutes,
            'metadata': metadata or {}
        }
        
        self.retry_queue.append(retry_item)
        print(f"⏰ Scheduled retry #{retry_count + 1} for {url} in {delay_minutes} minutes")
        return True
    
    def get_ready_retries(self) -> List[Dict[str, Any]]:
        """Get URLs that are ready for retry based on scheduled time."""
        current_time = asyncio.get_event_loop().time()
        ready = [item for item in self.retry_queue if item['scheduled_time'] <= current_time]
        
        # Remove ready items from queue
        self.retry_queue = [item for item in self.retry_queue if item['scheduled_time'] > current_time]
        
        if ready:
            print(f"⚡ {len(ready)} URLs ready for retry")
        
        return ready
    
    def get_retry_stats(self) -> Dict[str, Any]:
        """Get statistics about the retry queue."""
        current_time = asyncio.get_event_loop().time()
        
        return {
            'total_queued': len(self.retry_queue),
            'ready_now': len([item for item in self.retry_queue if item['scheduled_time'] <= current_time]),
            'pending': len([item for item in self.retry_queue if item['scheduled_time'] > current_time]),
            'average_retry_count': sum(item['retry_count'] for item in self.retry_queue) / len(self.retry_queue) if self.retry_queue else 0
        }

# Helper functions for Neo4j validation and error handling
def validate_neo4j_connection() -> bool:
    """Check if Neo4j environment variables are configured."""
    return all([
        os.getenv("NEO4J_URI"),
        os.getenv("NEO4J_USER"),
        os.getenv("NEO4J_PASSWORD")
    ])

def format_neo4j_error(error: Exception) -> str:
    """Format Neo4j connection errors for user-friendly messages."""
    error_str = str(error).lower()
    if "authentication" in error_str or "unauthorized" in error_str:
        return "Neo4j authentication failed. Check NEO4J_USER and NEO4J_PASSWORD."
    elif "connection" in error_str or "refused" in error_str or "timeout" in error_str:
        return "Cannot connect to Neo4j. Check NEO4J_URI and ensure Neo4j is running."
    elif "database" in error_str:
        return "Neo4j database error. Check if the database exists and is accessible."
    else:
        return f"Neo4j error: {str(error)}"

def validate_script_path(script_path: str) -> Dict[str, Any]:
    """Validate script path and return error info if invalid."""
    if not script_path or not isinstance(script_path, str):
        return {"valid": False, "error": "Script path is required"}
    
    if not os.path.exists(script_path):
        return {"valid": False, "error": f"Script not found: {script_path}"}
    
    if not script_path.endswith('.py'):
        return {"valid": False, "error": "Only Python (.py) files are supported"}
    
    try:
        # Check if file is readable
        with open(script_path, 'r', encoding='utf-8') as f:
            f.read(1)  # Read first character to test
        return {"valid": True}
    except Exception as e:
        return {"valid": False, "error": f"Cannot read script file: {str(e)}"}

def validate_github_url(repo_url: str) -> Dict[str, Any]:
    """Validate GitHub repository URL."""
    if not repo_url or not isinstance(repo_url, str):
        return {"valid": False, "error": "Repository URL is required"}
    
    repo_url = repo_url.strip()
    
    # Basic GitHub URL validation
    if not ("github.com" in repo_url.lower() or repo_url.endswith(".git")):
        return {"valid": False, "error": "Please provide a valid GitHub repository URL"}
    
    # Check URL format
    if not (repo_url.startswith("https://") or repo_url.startswith("git@")):
        return {"valid": False, "error": "Repository URL must start with https:// or git@"}
    
    return {"valid": True, "repo_name": repo_url.split('/')[-1].replace('.git', '')}

# Create a dataclass for our application context
@dataclass
class CrawlProgress:
    """Track crawl progress and statistics."""
    total_urls: int = 0
    successful: int = 0
    failed: int = 0
    retried: int = 0
    partial_results: List[Dict[str, Any]] = None
    failed_urls: List[Dict[str, Any]] = None
    start_time: Optional[float] = None
    last_update: Optional[float] = None
    
    def __post_init__(self):
        if self.partial_results is None:
            self.partial_results = []
        if self.failed_urls is None:
            self.failed_urls = []
        if self.start_time is None:
            self.start_time = asyncio.get_event_loop().time()
        self.last_update = self.start_time
    
    @property
    def success_rate(self) -> float:
        return (self.successful / self.total_urls * 100) if self.total_urls > 0 else 0
    
    @property
    def elapsed_time(self) -> float:
        return (self.last_update or asyncio.get_event_loop().time()) - (self.start_time or 0)
    
    def update_progress(self, successful: int = 0, failed: int = 0, retried: int = 0):
        self.successful += successful
        self.failed += failed
        self.retried += retried
        self.last_update = asyncio.get_event_loop().time()
    
    def add_partial_result(self, result: Dict[str, Any]):
        self.partial_results.append(result)
        self.update_progress(successful=1)
    
    def add_failed_url(self, url: str, error: str, retry_eligible: bool = False, metadata: Dict[str, Any] = None):
        failed_info = {
            'url': url,
            'error': error,
            'retry_eligible': retry_eligible,
            'timestamp': asyncio.get_event_loop().time(),
            'metadata': metadata or {}
        }
        self.failed_urls.append(failed_info)
        self.update_progress(failed=1)
    
    def get_summary(self) -> Dict[str, Any]:
        return {
            'total_urls': self.total_urls,
            'successful': self.successful,
            'failed': self.failed,
            'retried': self.retried,
            'success_rate': self.success_rate,
            'elapsed_time': self.elapsed_time,
            'partial_results_count': len(self.partial_results),
            'failed_urls_count': len(self.failed_urls),
            'retry_eligible_count': sum(1 for f in self.failed_urls if f.get('retry_eligible', False))
        }

@dataclass
class Crawl4AIContext:
    """Context for the Crawl4AI MCP server with error recovery capabilities."""
    crawler: AsyncWebCrawler
    supabase_client: Client
    reranking_model: Optional[CrossEncoder] = None
    knowledge_validator: Optional[Any] = None  # KnowledgeGraphValidator when available
    repo_extractor: Optional[Any] = None       # DirectNeo4jExtractor when available
    progress: Optional[CrawlProgress] = None  # Track progress and partial results
    error_recovery: Optional[ErrorRecoveryManager] = None  # Manage error recovery and retries

@asynccontextmanager
async def crawl4ai_lifespan(server: FastMCP) -> AsyncIterator[Crawl4AIContext]:
    """
    Manages the Crawl4AI client lifecycle with proper initialization barrier.
    
    Args:
        server: The FastMCP server instance
        
    Yields:
        Crawl4AIContext: The context containing the Crawl4AI crawler and Supabase client
    """
    print("🚀 Starting server initialization...")
    
    # Declare global variables for component access
    global _crawler, _reranking_model, _knowledge_validator, _repo_extractor, _error_recovery
    
    # Create initialization barrier to prevent race conditions
    initialization_complete = asyncio.Event()
    
    try:
        # Create browser configuration
        browser_config = BrowserConfig(
            headless=True,
            verbose=False
        )
        
        # Initialize the crawler
        print("⏳ Initializing AsyncWebCrawler...")
        crawler = AsyncWebCrawler(config=browser_config)
        await crawler.__aenter__()
        print("✅ AsyncWebCrawler initialized")
    
        # Initialize Supabase client
        print("⏳ Initializing Supabase client...")
        supabase_client = get_supabase_client()
        print("✅ Supabase client initialized")
        
        # Initialize cross-encoder model for reranking if enabled
        reranking_model = None
        if os.getenv("USE_RERANKING", "false") == "true":
            try:
                print("⏳ Loading reranking model...")
                reranking_model = CrossEncoder("cross-encoder/ms-marco-MiniLM-L-6-v2")
                print("✅ Reranking model loaded")
            except Exception as e:
                print(f"❌ Failed to load reranking model: {e}")
                reranking_model = None
    
        # Initialize Neo4j components if configured and enabled
        knowledge_validator = None
        repo_extractor = None
        
        # Check if knowledge graph functionality is enabled
        knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
        
        if knowledge_graph_enabled:
            neo4j_uri = os.getenv("NEO4J_URI")
            neo4j_user = os.getenv("NEO4J_USER")
            neo4j_password = os.getenv("NEO4J_PASSWORD")
            
            if neo4j_uri and neo4j_user and neo4j_password:
                try:
                    print("⏳ Initializing knowledge graph components...")
                    
                    # Initialize knowledge graph validator
                    knowledge_validator = KnowledgeGraphValidator(neo4j_uri, neo4j_user, neo4j_password)
                    await knowledge_validator.initialize()
                    print("✅ Knowledge graph validator initialized")
                    
                    # Initialize repository extractor
                    repo_extractor = DirectNeo4jExtractor(neo4j_uri, neo4j_user, neo4j_password)
                    await repo_extractor.initialize()
                    print("✅ Repository extractor initialized")
                    
                except Exception as e:
                    print(f"❌ Failed to initialize Neo4j components: {format_neo4j_error(e)}")
                    knowledge_validator = None
                    repo_extractor = None
            else:
                print("ℹ️ Neo4j credentials not configured - knowledge graph tools will be unavailable")
        else:
            print("ℹ️ Knowledge graph functionality disabled - set USE_KNOWLEDGE_GRAPH=true to enable")
        
        # Initialize error recovery manager
        print("⏳ Initializing error recovery manager...")
        error_recovery = ErrorRecoveryManager(supabase_client)
        print("✅ Error recovery manager initialized")
        
        # Store components in global variables for safe access
        _crawler = crawler
        _reranking_model = reranking_model
        _knowledge_validator = knowledge_validator
        _repo_extractor = repo_extractor
        _error_recovery = error_recovery
        
        # Signal that initialization is complete
        initialization_complete.set()
        print("🎉 Server initialization completed successfully!")
        
        yield Crawl4AIContext(
            crawler=crawler,
            supabase_client=supabase_client,
            reranking_model=reranking_model,
            knowledge_validator=knowledge_validator,
            repo_extractor=repo_extractor,
            progress=None,  # Progress will be initialized per operation
            error_recovery=error_recovery
        )
    except Exception as e:
        print(f"❌ Server initialization failed: {e}")
        # Ensure cleanup even if initialization fails
        if 'crawler' in locals():
            await crawler.__aexit__(None, None, None)
        raise
    finally:
        # Clean up all components
        if 'crawler' in locals():
            await crawler.__aexit__(None, None, None)
        if 'knowledge_validator' in locals() and knowledge_validator:
            try:
                await knowledge_validator.close()
                print("✓ Knowledge graph validator closed")
            except Exception as e:
                print(f"Error closing knowledge validator: {e}")
        if 'repo_extractor' in locals() and repo_extractor:
            try:
                await repo_extractor.close()
                print("✓ Repository extractor closed")
            except Exception as e:
                print(f"Error closing repository extractor: {e}")
        
        # Clear global variables
        _crawler = None
        _reranking_model = None
        _knowledge_validator = None
        _repo_extractor = None
        _error_recovery = None

# Initialize FastMCP server
mcp = FastMCP(
    "mcp-crawl4ai-rag",
    description="MCP server for RAG and web crawling with Crawl4AI",
    lifespan=crawl4ai_lifespan
)

@mcp.tool()
async def health_check(ctx: Context) -> str:
    """Health check endpoint to verify server status."""
    import time
    return json.dumps({
        "status": "healthy",
        "timestamp": time.time(),
        "server": "mcp-crawl4ai-rag",
        "message": "Server is ready"
    }, indent=2)

def rerank_results(model: CrossEncoder, query: str, results: List[Dict[str, Any]], content_key: str = "content") -> List[Dict[str, Any]]:
    """
    Rerank search results using a cross-encoder model.
    
    Args:
        model: The cross-encoder model to use for reranking
        query: The search query
        results: List of search results
        content_key: The key in each result dict that contains the text content
        
    Returns:
        Reranked list of results
    """
    if not model or not results:
        return results
    
    try:
        # Extract content from results
        texts = [result.get(content_key, "") for result in results]
        
        # Create pairs of [query, document] for the cross-encoder
        pairs = [[query, text] for text in texts]
        
        # Get relevance scores from the cross-encoder
        scores = model.predict(pairs)
        
        # Add scores to results and sort by score (descending)
        for i, result in enumerate(results):
            result["rerank_score"] = float(scores[i])
        
        # Sort by rerank score
        reranked = sorted(results, key=lambda x: x.get("rerank_score", 0), reverse=True)
        
        return reranked
    except Exception as e:
        print(f"Error during reranking: {e}")
        return results

def is_sitemap(url: str) -> bool:
    """
    Check if a URL is a sitemap.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is a sitemap, False otherwise
    """
    return url.endswith('sitemap.xml') or 'sitemap' in urlparse(url).path

def is_txt(url: str) -> bool:
    """
    Check if a URL is a text file.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is a text file, False otherwise
    """
    return url.endswith('.txt')

def parse_sitemap(sitemap_url: str) -> List[str]:
    """
    Parse a sitemap and extract URLs.
    
    Args:
        sitemap_url: URL of the sitemap
        
    Returns:
        List of URLs found in the sitemap
    """
    resp = requests.get(sitemap_url)
    urls = []

    if resp.status_code == 200:
        try:
            tree = ElementTree.fromstring(resp.content)
            urls = [loc.text for loc in tree.findall('.//{*}loc')]
        except Exception as e:
            print(f"Error parsing sitemap XML: {e}")

    return urls

def smart_chunk_markdown(text: str, chunk_size: int = 5000) -> List[str]:
    """Split text into chunks, respecting code blocks and paragraphs."""
    chunks = []
    start = 0
    text_length = len(text)

    while start < text_length:
        # Calculate end position
        end = start + chunk_size

        # If we're at the end of the text, just take what's left
        if end >= text_length:
            chunks.append(text[start:].strip())
            break

        # Try to find a code block boundary first (```)
        chunk = text[start:end]
        code_block = chunk.rfind('```')
        if code_block != -1 and code_block > chunk_size * 0.3:
            end = start + code_block

        # If no code block, try to break at a paragraph
        elif '\n\n' in chunk:
            # Find the last paragraph break
            last_break = chunk.rfind('\n\n')
            if last_break > chunk_size * 0.3:  # Only break if we're past 30% of chunk_size
                end = start + last_break

        # If no paragraph break, try to break at a sentence
        elif '. ' in chunk:
            # Find the last sentence break
            last_period = chunk.rfind('. ')
            if last_period > chunk_size * 0.3:  # Only break if we're past 30% of chunk_size
                end = start + last_period + 1

        # Extract chunk and clean it up
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # Move start position for next chunk
        start = end

    return chunks

def extract_section_info(chunk: str) -> Dict[str, Any]:
    """
    Extracts headers and stats from a chunk.
    
    Args:
        chunk: Markdown chunk
        
    Returns:
        Dictionary with headers and stats
    """
    headers = re.findall(r'^(#+)\s+(.+)$', chunk, re.MULTILINE)
    header_str = '; '.join([f'{h[0]} {h[1]}' for h in headers]) if headers else ''

    return {
        "headers": header_str,
        "char_count": len(chunk),
        "word_count": len(chunk.split())
    }

def process_code_example(args):
    """
    Process a single code example to generate its summary.
    This function is designed to be used with concurrent.futures.
    
    Args:
        args: Tuple containing (code, context_before, context_after)
        
    Returns:
        The generated summary
    """
    code, context_before, context_after = args
    return generate_code_example_summary(code, context_before, context_after)

@mcp.tool()
async def crawl_single_page(ctx: Context, url: str) -> str:
    """
    Crawl a single web page and store its content in Supabase.
    
    This tool is ideal for quickly retrieving content from a specific URL without following links.
    The content is stored in Supabase for later retrieval and querying.
    
    Args:
        ctx: The MCP server provided context
        url: URL of the web page to crawl
    
    Returns:
        Summary of the crawling operation and storage in Supabase
    """
    try:
        # Get components safely
        crawler = get_crawler()
        supabase_client = get_supabase_client()
        error_recovery = get_error_recovery()
        
        # Initialize progress tracking
        progress = CrawlProgress()
        progress.total_urls = 1
        progress.start_time = asyncio.get_event_loop().time()
        
        print(f"🚀 Starting single page crawl: {url}")
        
        # Use robust crawling with retry logic and timeout protection
        result = await robust_crawl_with_retry(crawler, url, max_retries=3, base_timeout=30000)
        
        if result.success and result.markdown:
            # Extract source_id
            parsed_url = urlparse(url)
            source_id = parsed_url.netloc or parsed_url.path
            
            # Chunk the content
            chunks = smart_chunk_markdown(result.markdown)
            
            # Prepare data for Supabase
            urls = []
            chunk_numbers = []
            contents = []
            metadatas = []
            total_word_count = 0
            
            for i, chunk in enumerate(chunks):
                urls.append(url)
                chunk_numbers.append(i)
                contents.append(chunk)
                
                # Extract metadata
                meta = extract_section_info(chunk)
                meta["chunk_index"] = i
                meta["url"] = url
                meta["source"] = source_id
                meta["crawl_time"] = str(asyncio.current_task().get_coro().__name__)
                metadatas.append(meta)
                
                # Accumulate word count
                total_word_count += meta.get("word_count", 0)
            
            # Create url_to_full_document mapping
            url_to_full_document = {url: result.markdown}
            
            # Update source information FIRST (before inserting documents)
            source_summary = extract_source_summary(source_id, result.markdown[:5000])  # Use first 5000 chars for summary
            update_source_info(supabase_client, source_id, source_summary, total_word_count)
            
            # Add documentation chunks to Supabase (AFTER source exists)
            add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document)
            
            # Extract and process code examples only if enabled
            extract_code_examples = os.getenv("USE_AGENTIC_RAG", "false") == "true"
            if extract_code_examples:
                code_blocks = extract_code_blocks(result.markdown)
                if code_blocks:
                    code_urls = []
                    code_chunk_numbers = []
                    code_examples = []
                    code_summaries = []
                    code_metadatas = []
                    
                    # Process code examples in parallel
                    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                        # Prepare arguments for parallel processing
                        summary_args = [(block['code'], block['context_before'], block['context_after']) 
                                        for block in code_blocks]
                        
                        # Generate summaries in parallel
                        summaries = list(executor.map(process_code_example, summary_args))
                    
                    # Prepare code example data
                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):
                        code_urls.append(url)
                        code_chunk_numbers.append(i)
                        code_examples.append(block['code'])
                        code_summaries.append(summary)
                        
                        # Create metadata for code example
                        code_meta = {
                            "chunk_index": i,
                            "url": url,
                            "source": source_id,
                            "char_count": len(block['code']),
                            "word_count": len(block['code'].split())
                        }
                        code_metadatas.append(code_meta)
                    
                    # Add code examples to Supabase
                    add_code_examples_to_supabase(
                        supabase_client, 
                        code_urls, 
                        code_chunk_numbers, 
                        code_examples, 
                        code_summaries, 
                        code_metadatas
                    )
            
            # Update progress tracking
            progress.update_progress(successful=1)
            progress.add_partial_result({'url': url, 'markdown': result.markdown})
            
            # Get final progress summary
            progress_summary = progress.get_summary()
            
            return json.dumps({
                "success": True,
                "url": url,
                "chunks_stored": len(chunks),
                "code_examples_stored": len(code_blocks) if code_blocks else 0,
                "content_length": len(result.markdown),
                "total_word_count": total_word_count,
                "source_id": source_id,
                "links_count": {
                    "internal": len(result.links.get("internal", [])),
                    "external": len(result.links.get("external", []))
                },
                "progress": progress_summary,
                "performance": {
                    "crawl_time_seconds": progress.elapsed_time,
                    "bytes_per_second": len(result.markdown) / max(progress.elapsed_time, 0.1),
                    "retry_used": False
                }
            }, indent=2)
        else:
            # Update progress and schedule retry if eligible
            progress.update_progress(failed=1)
            retry_eligible = should_retry_error(result)
            retry_scheduled = False
            
            if retry_eligible:
                retry_scheduled = error_recovery.schedule_retry(
                    url, result.error_message, 0, 
                    {"status_code": getattr(result, 'status_code', None)}
                )
            
            return json.dumps({
                "success": False,
                "url": url,
                "error": result.error_message,
                "status_code": getattr(result, 'status_code', None),
                "retry_eligible": retry_eligible,
                "retry_scheduled": retry_scheduled,
                "progress": progress.get_summary(),
                "retry_suggestion": "If this was a timeout, try again as the server may be temporarily slow"
            }, indent=2)
            
    except ConnectionTimeoutError as e:
        progress.update_progress(failed=1)
        retry_scheduled = error_recovery.schedule_retry(url, f"Connection timeout: {str(e)}", 0)
        
        return json.dumps({
            "success": False,
            "url": url,
            "error": f"Connection timeout: {str(e)}",
            "error_type": "timeout",
            "retry_scheduled": retry_scheduled,
            "progress": progress.get_summary(),
            "retry_suggestion": "The page took too long to load. This may be due to slow server response or network issues."
        }, indent=2)
        
    except HTTPStatusError as e:
        progress.update_progress(failed=1)
        status_code = getattr(e, 'status_code', None)
        retry_eligible = status_code and (500 <= status_code < 600 or status_code in [429, 503, 502, 504])
        retry_scheduled = False
        
        if retry_eligible:
            retry_scheduled = error_recovery.schedule_retry(
                url, f"HTTP error: {str(e)}", 0, 
                {"status_code": status_code}
            )
        
        return json.dumps({
            "success": False,
            "url": url,
            "error": f"HTTP error: {str(e)}",
            "error_type": "http_error",
            "status_code": status_code,
            "retry_eligible": retry_eligible,
            "retry_scheduled": retry_scheduled,
            "progress": progress.get_summary(),
            "retry_suggestion": "Check if the URL is correct and the server is accessible"
        }, indent=2)
        
    except HTTPCrawlerError as e:
        progress.update_progress(failed=1)
        retry_scheduled = error_recovery.schedule_retry(url, f"Crawler error: {str(e)}", 0)
        
        return json.dumps({
            "success": False,
            "url": url,
            "error": f"Crawler error: {str(e)}",
            "error_type": "crawler_error",
            "retry_scheduled": retry_scheduled,
            "progress": progress.get_summary(),
            "retry_suggestion": "The crawler encountered an issue. This might be due to page content or JavaScript errors."
        }, indent=2)
        
    except Exception as e:
        progress.update_progress(failed=1)
        retry_scheduled = error_recovery.schedule_retry(url, str(e), 0)
        
        return json.dumps({
            "success": False,
            "url": url,
            "error": str(e),
            "error_type": "unexpected_error",
            "retry_scheduled": retry_scheduled,
            "progress": progress.get_summary(),
            "retry_suggestion": "An unexpected error occurred. Check the URL and try again."
        }, indent=2)

@mcp.tool()
async def get_retry_status(ctx: Context) -> str:
    """
    Get the current retry queue status and process any ready retries.
    
    This tool provides visibility into the error recovery system and can trigger
    automatic processing of URLs that are ready for retry.
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with retry queue statistics and any processed retries
    """
    try:
        # Get error recovery manager safely
        error_recovery = get_error_recovery()
        
        # Get current retry statistics
        retry_stats = error_recovery.get_retry_stats()
        
        # Process any ready retries
        ready_retries = error_recovery.get_ready_retries()
        processed_retries = []
        
        if ready_retries:
            print(f"🔄 Processing {len(ready_retries)} ready retries...")
            crawler = get_crawler()
            
            for retry_item in ready_retries[:5]:  # Process maximum 5 retries at once
                try:
                    print(f"🔄 Retrying {retry_item['url']} (attempt #{retry_item['retry_count']})")
                    result = await robust_crawl_with_retry(
                        crawler, retry_item['url'], 
                        max_retries=1, base_timeout=45000  # Single retry with longer timeout
                    )
                    
                    processed_retries.append({
                        'url': retry_item['url'],
                        'retry_count': retry_item['retry_count'],
                        'success': result.success,
                        'error': result.error_message if not result.success else None,
                        'content_length': len(result.markdown) if result.success and result.markdown else 0
                    })
                    
                    if not result.success:
                        # Re-schedule if still failing and under max retries
                        error_recovery.schedule_retry(
                            retry_item['url'], 
                            result.error_message, 
                            retry_item['retry_count']
                        )
                    
                except Exception as e:
                    processed_retries.append({
                        'url': retry_item['url'],
                        'retry_count': retry_item['retry_count'],
                        'success': False,
                        'error': f"Retry processing error: {str(e)}",
                        'content_length': 0
                    })
        
        # Update statistics after processing
        final_stats = error_recovery.get_retry_stats()
        
        return json.dumps({
            "retry_queue_stats": {
                "before_processing": retry_stats,
                "after_processing": final_stats
            },
            "processed_retries": {
                "count": len(processed_retries),
                "successful": sum(1 for r in processed_retries if r['success']),
                "failed": sum(1 for r in processed_retries if not r['success']),
                "details": processed_retries
            },
            "recommendations": [
                "Use this tool periodically to process retry queue",
                "Successful retries will need manual content extraction",
                "Failed retries are automatically re-scheduled with longer delays"
            ]
        }, indent=2)
    
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": f"Error checking retry status: {str(e)}",
            "retry_queue_stats": {"error": "unavailable"}
        }, indent=2)

@mcp.tool()
async def smart_crawl_url(ctx: Context, url: str, max_depth: int = 3, max_concurrent: int = 10, chunk_size: int = 5000) -> str:
    """
    Intelligently crawl a URL based on its type and store content in Supabase.
    
    This tool automatically detects the URL type and applies the appropriate crawling method:
    - For sitemaps: Extracts and crawls all URLs in parallel
    - For text files (llms.txt): Directly retrieves the content
    - For regular webpages: Recursively crawls internal links up to the specified depth
    
    All crawled content is chunked and stored in Supabase for later retrieval and querying.
    
    Args:
        ctx: The MCP server provided context
        url: URL to crawl (can be a regular webpage, sitemap.xml, or .txt file)
        max_depth: Maximum recursion depth for regular URLs (default: 3)
        max_concurrent: Maximum number of concurrent browser sessions (default: 10)
        chunk_size: Maximum size of each content chunk in characters (default: 1000)
    
    Returns:
        JSON string with crawl summary and storage information
    """
    try:
        # Get components safely
        crawler = get_crawler()
        supabase_client = get_supabase_client()
        
        # Determine the crawl strategy
        crawl_results = []
        crawl_type = None
        
        if is_txt(url):
            # For text files, use simple crawl
            crawl_results = await crawl_markdown_file(crawler, url)
            crawl_type = "text_file"
        elif is_sitemap(url):
            # For sitemaps, extract URLs and crawl in parallel
            sitemap_urls = parse_sitemap(url)
            if not sitemap_urls:
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": "No URLs found in sitemap"
                }, indent=2)
            crawl_results = await crawl_batch(crawler, sitemap_urls, max_concurrent=max_concurrent)
            crawl_type = "sitemap"
        else:
            # For regular URLs, use recursive crawl
            crawl_results = await crawl_recursive_internal_links(crawler, [url], max_depth=max_depth, max_concurrent=max_concurrent)
            crawl_type = "webpage"
        
        if not crawl_results:
            return json.dumps({
                "success": False,
                "url": url,
                "error": "No content found"
            }, indent=2)
        
        # Process results and store in Supabase
        urls = []
        chunk_numbers = []
        contents = []
        metadatas = []
        chunk_count = 0
        
        # Track sources and their content
        source_content_map = {}
        source_word_counts = {}
        
        # Process documentation chunks
        for doc in crawl_results:
            source_url = doc['url']
            md = doc['markdown']
            chunks = smart_chunk_markdown(md, chunk_size=chunk_size)
            
            # Extract source_id
            parsed_url = urlparse(source_url)
            source_id = parsed_url.netloc or parsed_url.path
            
            # Store content for source summary generation
            if source_id not in source_content_map:
                source_content_map[source_id] = md[:5000]  # Store first 5000 chars
                source_word_counts[source_id] = 0
            
            for i, chunk in enumerate(chunks):
                urls.append(source_url)
                chunk_numbers.append(i)
                contents.append(chunk)
                
                # Extract metadata
                meta = extract_section_info(chunk)
                meta["chunk_index"] = i
                meta["url"] = source_url
                meta["source"] = source_id
                meta["crawl_type"] = crawl_type
                meta["crawl_time"] = str(asyncio.current_task().get_coro().__name__)
                metadatas.append(meta)
                
                # Accumulate word count
                source_word_counts[source_id] += meta.get("word_count", 0)
                
                chunk_count += 1
        
        # Create url_to_full_document mapping
        url_to_full_document = {}
        for doc in crawl_results:
            url_to_full_document[doc['url']] = doc['markdown']
        
        # Update source information for each unique source FIRST (before inserting documents)
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            source_summary_args = [(source_id, content) for source_id, content in source_content_map.items()]
            source_summaries = list(executor.map(lambda args: extract_source_summary(args[0], args[1]), source_summary_args))
        
        for (source_id, _), summary in zip(source_summary_args, source_summaries):
            word_count = source_word_counts.get(source_id, 0)
            update_source_info(supabase_client, source_id, summary, word_count)
        
        # Add documentation chunks to Supabase (AFTER sources exist)
        batch_size = 20
        add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document, batch_size=batch_size)
        
        # Extract and process code examples from all documents only if enabled
        extract_code_examples_enabled = os.getenv("USE_AGENTIC_RAG", "false") == "true"
        if extract_code_examples_enabled:
            all_code_blocks = []
            code_urls = []
            code_chunk_numbers = []
            code_examples = []
            code_summaries = []
            code_metadatas = []
            
            # Extract code blocks from all documents
            for doc in crawl_results:
                source_url = doc['url']
                md = doc['markdown']
                code_blocks = extract_code_blocks(md)
                
                if code_blocks:
                    # Process code examples in parallel
                    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                        # Prepare arguments for parallel processing
                        summary_args = [(block['code'], block['context_before'], block['context_after']) 
                                        for block in code_blocks]
                        
                        # Generate summaries in parallel
                        summaries = list(executor.map(process_code_example, summary_args))
                    
                    # Prepare code example data
                    parsed_url = urlparse(source_url)
                    source_id = parsed_url.netloc or parsed_url.path
                    
                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):
                        code_urls.append(source_url)
                        code_chunk_numbers.append(len(code_examples))  # Use global code example index
                        code_examples.append(block['code'])
                        code_summaries.append(summary)
                        
                        # Create metadata for code example
                        code_meta = {
                            "chunk_index": len(code_examples) - 1,
                            "url": source_url,
                            "source": source_id,
                            "char_count": len(block['code']),
                            "word_count": len(block['code'].split())
                        }
                        code_metadatas.append(code_meta)
            
            # Add all code examples to Supabase
            if code_examples:
                add_code_examples_to_supabase(
                    supabase_client, 
                    code_urls, 
                    code_chunk_numbers, 
                    code_examples, 
                    code_summaries, 
                    code_metadatas,
                    batch_size=batch_size
                )
        
        return json.dumps({
            "success": True,
            "url": url,
            "crawl_type": crawl_type,
            "pages_crawled": len(crawl_results),
            "chunks_stored": chunk_count,
            "code_examples_stored": len(code_examples),
            "sources_updated": len(source_content_map),
            "urls_crawled": [doc['url'] for doc in crawl_results][:5] + (["..."] if len(crawl_results) > 5 else [])
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "url": url,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def get_available_sources(ctx: Context) -> str:
    """
    Get all available sources from the sources table.
    
    This tool returns a list of all unique sources (domains) that have been crawled and stored
    in the database, along with their summaries and statistics. This is useful for discovering 
    what content is available for querying.

    Always use this tool before calling the RAG query or code example query tool
    with a specific source filter!
    
    Args:
        ctx: The MCP server provided context
    
    Returns:
        JSON string with the list of available sources and their details
    """
    try:
        # Get the Supabase client safely
        supabase_client = get_supabase_client()
        
        # Query the sources table directly
        result = supabase_client.from_('sources')\
            .select('*')\
            .order('source_id')\
            .execute()
        
        # Format the sources with their details
        sources = []
        if result.data:
            for source in result.data:
                sources.append({
                    "source_id": source.get("source_id"),
                    "summary": source.get("summary"),
                    "total_words": source.get("total_words"),
                    "created_at": source.get("created_at"),
                    "updated_at": source.get("updated_at")
                })
        
        return json.dumps({
            "success": True,
            "sources": sources,
            "count": len(sources)
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def perform_rag_query(ctx: Context, query: str, source: Optional[str] = None, match_count: int = 5) -> str:
    """
    Perform a RAG (Retrieval Augmented Generation) query on the stored content.
    
    This tool searches the vector database for content relevant to the query and returns
    the matching documents. Optionally filter by source domain.
    Get the source by using the get_available_sources tool before calling this search!
    
    Args:
        ctx: The MCP server provided context
        query: The search query
        source: Optional source domain to filter results (e.g., 'example.com')
        match_count: Maximum number of results to return (default: 5)
    
    Returns:
        JSON string with the search results
    """
    try:
        # Get the Supabase client safely
        supabase_client = get_supabase_client()
        
        # Check if hybrid search is enabled
        use_hybrid_search = os.getenv("USE_HYBRID_SEARCH", "false") == "true"
        
        # Prepare filter if source is provided and not empty
        filter_metadata = None
        if source and source.strip():
            filter_metadata = {"source": source}
        
        if use_hybrid_search:
            # Hybrid search: combine vector and keyword search
            
            # 1. Get vector search results (get more to account for filtering)
            vector_results = search_documents(
                client=supabase_client,
                query=query,
                match_count=match_count * 2,  # Get double to have room for filtering
                filter_metadata=filter_metadata
            )
            
            # 2. Get keyword search results using ILIKE
            keyword_query = supabase_client.from_('crawled_pages')\
                .select('id, url, chunk_number, content, metadata, source_id')\
                .ilike('content', f'%{query}%')
            
            # Apply source filter if provided
            if source and source.strip():
                keyword_query = keyword_query.eq('source_id', source)
            
            # Execute keyword search
            keyword_response = keyword_query.limit(match_count * 2).execute()
            keyword_results = keyword_response.data if keyword_response.data else []
            
            # 3. Combine results with preference for items appearing in both
            seen_ids = set()
            combined_results = []
            
            # First, add items that appear in both searches (these are the best matches)
            vector_ids = {r.get('id') for r in vector_results if r.get('id')}
            for kr in keyword_results:
                if kr['id'] in vector_ids and kr['id'] not in seen_ids:
                    # Find the vector result to get similarity score
                    for vr in vector_results:
                        if vr.get('id') == kr['id']:
                            # Boost similarity score for items in both results
                            vr['similarity'] = min(1.0, vr.get('similarity', 0) * 1.2)
                            combined_results.append(vr)
                            seen_ids.add(kr['id'])
                            break
            
            # Then add remaining vector results (semantic matches without exact keyword)
            for vr in vector_results:
                if vr.get('id') and vr['id'] not in seen_ids and len(combined_results) < match_count:
                    combined_results.append(vr)
                    seen_ids.add(vr['id'])
            
            # Finally, add pure keyword matches if we still need more results
            for kr in keyword_results:
                if kr['id'] not in seen_ids and len(combined_results) < match_count:
                    # Convert keyword result to match vector result format
                    combined_results.append({
                        'id': kr['id'],
                        'url': kr['url'],
                        'chunk_number': kr['chunk_number'],
                        'content': kr['content'],
                        'metadata': kr['metadata'],
                        'source_id': kr['source_id'],
                        'similarity': 0.5  # Default similarity for keyword-only matches
                    })
                    seen_ids.add(kr['id'])
            
            # Use combined results
            results = combined_results[:match_count]
            
        else:
            # Standard vector search only
            results = search_documents(
                client=supabase_client,
                query=query,
                match_count=match_count,
                filter_metadata=filter_metadata
            )
        
        # Apply reranking if enabled
        use_reranking = os.getenv("USE_RERANKING", "false") == "true"
        reranking_model = get_reranking_model()
        if use_reranking and reranking_model:
            results = rerank_results(reranking_model, query, results, content_key="content")
        
        # Format the results
        formatted_results = []
        for result in results:
            formatted_result = {
                "url": result.get("url"),
                "content": result.get("content"),
                "metadata": result.get("metadata"),
                "similarity": result.get("similarity")
            }
            # Include rerank score if available
            if "rerank_score" in result:
                formatted_result["rerank_score"] = result["rerank_score"]
            formatted_results.append(formatted_result)
        
        return json.dumps({
            "success": True,
            "query": query,
            "source_filter": source,
            "search_mode": "hybrid" if use_hybrid_search else "vector",
            "reranking_applied": use_reranking and reranking_model is not None,
            "results": formatted_results,
            "count": len(formatted_results)
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "query": query,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def search_code_examples(ctx: Context, query: str, source_id: Optional[str] = None, match_count: int = 5) -> str:
    """
    Search for code examples relevant to the query.
    
    This tool searches the vector database for code examples relevant to the query and returns
    the matching examples with their summaries. Optionally filter by source_id.
    Get the source_id by using the get_available_sources tool before calling this search!

    Use the get_available_sources tool first to see what sources are available for filtering.
    
    Args:
        ctx: The MCP server provided context
        query: The search query
        source_id: Optional source ID to filter results (e.g., 'example.com')
        match_count: Maximum number of results to return (default: 5)
    
    Returns:
        JSON string with the search results
    """
    # Check if code example extraction is enabled
    extract_code_examples_enabled = os.getenv("USE_AGENTIC_RAG", "false") == "true"
    if not extract_code_examples_enabled:
        return json.dumps({
            "success": False,
            "error": "Code example extraction is disabled. Perform a normal RAG search."
        }, indent=2)
    
    try:
        # Get the Supabase client safely
        try:
            supabase_client = get_supabase_client()
        except Exception as e:
            return json.dumps({
                "success": False,
                "query": query,
                "error": f"Failed to initialize Supabase client: {str(e)}"
            }, indent=2)
        
        # Check if hybrid search is enabled
        use_hybrid_search = os.getenv("USE_HYBRID_SEARCH", "false") == "true"
        
        # Prepare filter if source is provided and not empty
        filter_metadata = None
        if source_id and source_id.strip():
            filter_metadata = {"source": source_id}
        
        if use_hybrid_search:
            # Hybrid search: combine vector and keyword search
            
            # Import the search function from utils
            from utils import search_code_examples as search_code_examples_impl
            
            # 1. Get vector search results (get more to account for filtering)
            vector_results = search_code_examples_impl(
                client=supabase_client,
                query=query,
                match_count=match_count * 2,  # Get double to have room for filtering
                filter_metadata=filter_metadata
            )
            
            # 2. Get keyword search results using ILIKE on both content and summary
            keyword_query = supabase_client.from_('code_examples')\
                .select('id, url, chunk_number, content, summary, metadata, source_id')\
                .or_(f'content.ilike.%{query}%,summary.ilike.%{query}%')
            
            # Apply source filter if provided
            if source_id and source_id.strip():
                keyword_query = keyword_query.eq('source_id', source_id)
            
            # Execute keyword search
            keyword_response = keyword_query.limit(match_count * 2).execute()
            keyword_results = keyword_response.data if keyword_response.data else []
            
            # 3. Combine results with preference for items appearing in both
            seen_ids = set()
            combined_results = []
            
            # First, add items that appear in both searches (these are the best matches)
            vector_ids = {r.get('id') for r in vector_results if r.get('id')}
            for kr in keyword_results:
                if kr['id'] in vector_ids and kr['id'] not in seen_ids:
                    # Find the vector result to get similarity score
                    for vr in vector_results:
                        if vr.get('id') == kr['id']:
                            # Boost similarity score for items in both results
                            vr['similarity'] = min(1.0, vr.get('similarity', 0) * 1.2)
                            combined_results.append(vr)
                            seen_ids.add(kr['id'])
                            break
            
            # Then add remaining vector results (semantic matches without exact keyword)
            for vr in vector_results:
                if vr.get('id') and vr['id'] not in seen_ids and len(combined_results) < match_count:
                    combined_results.append(vr)
                    seen_ids.add(vr['id'])
            
            # Finally, add pure keyword matches if we still need more results
            for kr in keyword_results:
                if kr['id'] not in seen_ids and len(combined_results) < match_count:
                    # Convert keyword result to match vector result format
                    combined_results.append({
                        'id': kr['id'],
                        'url': kr['url'],
                        'chunk_number': kr['chunk_number'],
                        'content': kr['content'],
                        'summary': kr['summary'],
                        'metadata': kr['metadata'],
                        'source_id': kr['source_id'],
                        'similarity': 0.5  # Default similarity for keyword-only matches
                    })
                    seen_ids.add(kr['id'])
            
            # Use combined results
            results = combined_results[:match_count]
            
        else:
            # Standard vector search only
            from utils import search_code_examples as search_code_examples_impl
            
            results = search_code_examples_impl(
                client=supabase_client,
                query=query,
                match_count=match_count,
                filter_metadata=filter_metadata
            )
        
        # Apply reranking if enabled
        use_reranking = os.getenv("USE_RERANKING", "false") == "true"
        reranking_model = get_reranking_model()
        if use_reranking and reranking_model:
            results = rerank_results(reranking_model, query, results, content_key="content")
        
        # Format the results
        formatted_results = []
        for result in results:
            formatted_result = {
                "url": result.get("url"),
                "code": result.get("content"),
                "summary": result.get("summary"),
                "metadata": result.get("metadata"),
                "source_id": result.get("source_id"),
                "similarity": result.get("similarity")
            }
            # Include rerank score if available
            if "rerank_score" in result:
                formatted_result["rerank_score"] = result["rerank_score"]
            formatted_results.append(formatted_result)
        
        return json.dumps({
            "success": True,
            "query": query,
            "source_filter": source_id,
            "search_mode": "hybrid" if use_hybrid_search else "vector",
            "reranking_applied": use_reranking and reranking_model is not None,
            "results": formatted_results,
            "count": len(formatted_results)
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "query": query,
            "error": str(e)
        }, indent=2)

@mcp.tool()
async def check_ai_script_hallucinations(ctx: Context, script_path: str) -> str:
    """
    Check an AI-generated Python script for hallucinations using the knowledge graph.
    
    This tool analyzes a Python script for potential AI hallucinations by validating
    imports, method calls, class instantiations, and function calls against a Neo4j
    knowledge graph containing real repository data.
    
    The tool performs comprehensive analysis including:
    - Import validation against known repositories
    - Method call validation on classes from the knowledge graph
    - Class instantiation parameter validation
    - Function call parameter validation
    - Attribute access validation
    
    Args:
        ctx: The MCP server provided context
        script_path: Absolute path to the Python script to analyze
    
    Returns:
        JSON string with hallucination detection results, confidence scores, and recommendations
    """
    try:
        # Check if knowledge graph functionality is enabled
        knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
        if not knowledge_graph_enabled:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph functionality is disabled. Set USE_KNOWLEDGE_GRAPH=true in environment."
            }, indent=2)
        
        # Get the knowledge validator safely
        knowledge_validator = get_knowledge_validator()
        
        if not knowledge_validator:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph validator not available. Check Neo4j configuration in environment variables."
            }, indent=2)
        
        # Validate script path
        validation = validate_script_path(script_path)
        if not validation["valid"]:
            return json.dumps({
                "success": False,
                "script_path": script_path,
                "error": validation["error"]
            }, indent=2)
        
        # Step 1: Analyze script structure using AST
        analyzer = AIScriptAnalyzer()
        analysis_result = analyzer.analyze_script(script_path)
        
        if analysis_result.errors:
            print(f"Analysis warnings for {script_path}: {analysis_result.errors}")
        
        # Step 2: Validate against knowledge graph
        validation_result = await knowledge_validator.validate_script(analysis_result)
        
        # Step 3: Generate comprehensive report
        reporter = HallucinationReporter()
        report = reporter.generate_comprehensive_report(validation_result)
        
        # Format response with comprehensive information
        return json.dumps({
            "success": True,
            "script_path": script_path,
            "overall_confidence": validation_result.overall_confidence,
            "validation_summary": {
                "total_validations": report["validation_summary"]["total_validations"],
                "valid_count": report["validation_summary"]["valid_count"],
                "invalid_count": report["validation_summary"]["invalid_count"],
                "uncertain_count": report["validation_summary"]["uncertain_count"],
                "not_found_count": report["validation_summary"]["not_found_count"],
                "hallucination_rate": report["validation_summary"]["hallucination_rate"]
            },
            "hallucinations_detected": report["hallucinations_detected"],
            "recommendations": report["recommendations"],
            "analysis_metadata": {
                "total_imports": report["analysis_metadata"]["total_imports"],
                "total_classes": report["analysis_metadata"]["total_classes"],
                "total_methods": report["analysis_metadata"]["total_methods"],
                "total_attributes": report["analysis_metadata"]["total_attributes"],
                "total_functions": report["analysis_metadata"]["total_functions"]
            },
            "libraries_analyzed": report.get("libraries_analyzed", [])
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "script_path": script_path,
            "error": f"Analysis failed: {str(e)}"
        }, indent=2)

@mcp.tool()
async def query_knowledge_graph(ctx: Context, command: str) -> str:
    """
    Query and explore the Neo4j knowledge graph containing repository data.
    
    This tool provides comprehensive access to the knowledge graph for exploring repositories,
    classes, methods, functions, and their relationships. Perfect for understanding what data
    is available for hallucination detection and debugging validation results.
    
    **⚠️ IMPORTANT: Always start with the `repos` command first!**
    Before using any other commands, run `repos` to see what repositories are available
    in your knowledge graph. This will help you understand what data you can explore.
    
    ## Available Commands:
    
    **Repository Commands:**
    - `repos` - **START HERE!** List all repositories in the knowledge graph
    - `explore <repo_name>` - Get detailed overview of a specific repository
    
    **Class Commands:**  
    - `classes` - List all classes across all repositories (limited to 20)
    - `classes <repo_name>` - List classes in a specific repository
    - `class <class_name>` - Get detailed information about a specific class including methods and attributes
    
    **Method Commands:**
    - `method <method_name>` - Search for methods by name across all classes
    - `method <method_name> <class_name>` - Search for a method within a specific class
    
    **Custom Query:**
    - `query <cypher_query>` - Execute a custom Cypher query (results limited to 20 records)
    
    ## Knowledge Graph Schema:
    
    **Node Types:**
    - Repository: `(r:Repository {name: string})`
    - File: `(f:File {path: string, module_name: string})`
    - Class: `(c:Class {name: string, full_name: string})`
    - Method: `(m:Method {name: string, params_list: [string], params_detailed: [string], return_type: string, args: [string]})`
    - Function: `(func:Function {name: string, params_list: [string], params_detailed: [string], return_type: string, args: [string]})`
    - Attribute: `(a:Attribute {name: string, type: string})`
    
    **Relationships:**
    - `(r:Repository)-[:CONTAINS]->(f:File)`
    - `(f:File)-[:DEFINES]->(c:Class)`
    - `(c:Class)-[:HAS_METHOD]->(m:Method)`
    - `(c:Class)-[:HAS_ATTRIBUTE]->(a:Attribute)`
    - `(f:File)-[:DEFINES]->(func:Function)`
    
    ## Example Workflow:
    ```
    1. repos                                    # See what repositories are available
    2. explore pydantic-ai                      # Explore a specific repository
    3. classes pydantic-ai                      # List classes in that repository
    4. class Agent                              # Explore the Agent class
    5. method run_stream                        # Search for run_stream method
    6. method __init__ Agent                    # Find Agent constructor
    7. query "MATCH (c:Class)-[:HAS_METHOD]->(m:Method) WHERE m.name = 'run' RETURN c.name, m.name LIMIT 5"
    ```
    
    Args:
        ctx: The MCP server provided context
        command: Command string to execute (see available commands above)
    
    Returns:
        JSON string with query results, statistics, and metadata
    """
    try:
        # Check if knowledge graph functionality is enabled
        knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
        if not knowledge_graph_enabled:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph functionality is disabled. Set USE_KNOWLEDGE_GRAPH=true in environment."
            }, indent=2)
        
        # Get Neo4j driver safely
        repo_extractor = get_repo_extractor()
        if not repo_extractor or not repo_extractor.driver:
            return json.dumps({
                "success": False,
                "error": "Neo4j connection not available. Check Neo4j configuration in environment variables."
            }, indent=2)
        
        # Parse command
        command = command.strip()
        if not command:
            return json.dumps({
                "success": False,
                "command": "",
                "error": "Command cannot be empty. Available commands: repos, explore <repo>, classes [repo], class <name>, method <name> [class], query <cypher>"
            }, indent=2)
        
        parts = command.split()
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        async with repo_extractor.driver.session() as session:
            # Route to appropriate handler
            if cmd == "repos":
                return await _handle_repos_command(session, command)
            elif cmd == "explore":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Repository name required. Usage: explore <repo_name>"
                    }, indent=2)
                return await _handle_explore_command(session, command, args[0])
            elif cmd == "classes":
                repo_name = args[0] if args else None
                return await _handle_classes_command(session, command, repo_name)
            elif cmd == "class":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Class name required. Usage: class <class_name>"
                    }, indent=2)
                return await _handle_class_command(session, command, args[0])
            elif cmd == "method":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Method name required. Usage: method <method_name> [class_name]"
                    }, indent=2)
                method_name = args[0]
                class_name = args[1] if len(args) > 1 else None
                return await _handle_method_command(session, command, method_name, class_name)
            elif cmd == "query":
                if not args:
                    return json.dumps({
                        "success": False,
                        "command": command,
                        "error": "Cypher query required. Usage: query <cypher_query>"
                    }, indent=2)
                cypher_query = " ".join(args)
                return await _handle_query_command(session, command, cypher_query)
            else:
                return json.dumps({
                    "success": False,
                    "command": command,
                    "error": f"Unknown command '{cmd}'. Available commands: repos, explore <repo>, classes [repo], class <name>, method <name> [class], query <cypher>"
                }, indent=2)
                
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Query execution failed: {str(e)}"
        }, indent=2)


async def _handle_repos_command(session, command: str) -> str:
    """Handle 'repos' command - list all repositories"""
    query = "MATCH (r:Repository) RETURN r.name as name ORDER BY r.name"
    result = await session.run(query)
    
    repos = []
    async for record in result:
        repos.append(record['name'])
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "repositories": repos
        },
        "metadata": {
            "total_results": len(repos),
            "limited": False
        }
    }, indent=2)


async def _handle_explore_command(session, command: str, repo_name: str) -> str:
    """Handle 'explore <repo>' command - get repository overview"""
    # Check if repository exists
    repo_check_query = "MATCH (r:Repository {name: $repo_name}) RETURN r.name as name"
    result = await session.run(repo_check_query, repo_name=repo_name)
    repo_record = await result.single()
    
    if not repo_record:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Repository '{repo_name}' not found in knowledge graph"
        }, indent=2)
    
    # Get file count
    files_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)
    RETURN count(f) as file_count
    """
    result = await session.run(files_query, repo_name=repo_name)
    file_count = (await result.single())['file_count']
    
    # Get class count
    classes_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(c:Class)
    RETURN count(DISTINCT c) as class_count
    """
    result = await session.run(classes_query, repo_name=repo_name)
    class_count = (await result.single())['class_count']
    
    # Get function count
    functions_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(func:Function)
    RETURN count(DISTINCT func) as function_count
    """
    result = await session.run(functions_query, repo_name=repo_name)
    function_count = (await result.single())['function_count']
    
    # Get method count
    methods_query = """
    MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(c:Class)-[:HAS_METHOD]->(m:Method)
    RETURN count(DISTINCT m) as method_count
    """
    result = await session.run(methods_query, repo_name=repo_name)
    method_count = (await result.single())['method_count']
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "repository": repo_name,
            "statistics": {
                "files": file_count,
                "classes": class_count,
                "functions": function_count,
                "methods": method_count
            }
        },
        "metadata": {
            "total_results": 1,
            "limited": False
        }
    }, indent=2)


async def _handle_classes_command(session, command: str, repo_name: str = None) -> str:
    """Handle 'classes [repo]' command - list classes"""
    limit = 20
    
    if repo_name:
        query = """
        MATCH (r:Repository {name: $repo_name})-[:CONTAINS]->(f:File)-[:DEFINES]->(c:Class)
        RETURN c.name as name, c.full_name as full_name
        ORDER BY c.name
        LIMIT $limit
        """
        result = await session.run(query, repo_name=repo_name, limit=limit)
    else:
        query = """
        MATCH (c:Class)
        RETURN c.name as name, c.full_name as full_name
        ORDER BY c.name
        LIMIT $limit
        """
        result = await session.run(query, limit=limit)
    
    classes = []
    async for record in result:
        classes.append({
            'name': record['name'],
            'full_name': record['full_name']
        })
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "classes": classes,
            "repository_filter": repo_name
        },
        "metadata": {
            "total_results": len(classes),
            "limited": len(classes) >= limit
        }
    }, indent=2)


async def _handle_class_command(session, command: str, class_name: str) -> str:
    """Handle 'class <name>' command - explore specific class"""
    # Find the class
    class_query = """
    MATCH (c:Class)
    WHERE c.name = $class_name OR c.full_name = $class_name
    RETURN c.name as name, c.full_name as full_name
    LIMIT 1
    """
    result = await session.run(class_query, class_name=class_name)
    class_record = await result.single()
    
    if not class_record:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Class '{class_name}' not found in knowledge graph"
        }, indent=2)
    
    actual_name = class_record['name']
    full_name = class_record['full_name']
    
    # Get methods
    methods_query = """
    MATCH (c:Class)-[:HAS_METHOD]->(m:Method)
    WHERE c.name = $class_name OR c.full_name = $class_name
    RETURN m.name as name, m.params_list as params_list, m.params_detailed as params_detailed, m.return_type as return_type
    ORDER BY m.name
    """
    result = await session.run(methods_query, class_name=class_name)
    
    methods = []
    async for record in result:
        # Use detailed params if available, fall back to simple params
        params_to_use = record['params_detailed'] or record['params_list'] or []
        methods.append({
            'name': record['name'],
            'parameters': params_to_use,
            'return_type': record['return_type'] or 'Any'
        })
    
    # Get attributes
    attributes_query = """
    MATCH (c:Class)-[:HAS_ATTRIBUTE]->(a:Attribute)
    WHERE c.name = $class_name OR c.full_name = $class_name
    RETURN a.name as name, a.type as type
    ORDER BY a.name
    """
    result = await session.run(attributes_query, class_name=class_name)
    
    attributes = []
    async for record in result:
        attributes.append({
            'name': record['name'],
            'type': record['type'] or 'Any'
        })
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "class": {
                "name": actual_name,
                "full_name": full_name,
                "methods": methods,
                "attributes": attributes
            }
        },
        "metadata": {
            "total_results": 1,
            "methods_count": len(methods),
            "attributes_count": len(attributes),
            "limited": False
        }
    }, indent=2)


async def _handle_method_command(session, command: str, method_name: str, class_name: str = None) -> str:
    """Handle 'method <name> [class]' command - search for methods"""
    if class_name:
        query = """
        MATCH (c:Class)-[:HAS_METHOD]->(m:Method)
        WHERE (c.name = $class_name OR c.full_name = $class_name)
          AND m.name = $method_name
        RETURN c.name as class_name, c.full_name as class_full_name,
               m.name as method_name, m.params_list as params_list, 
               m.params_detailed as params_detailed, m.return_type as return_type, m.args as args
        """
        result = await session.run(query, class_name=class_name, method_name=method_name)
    else:
        query = """
        MATCH (c:Class)-[:HAS_METHOD]->(m:Method)
        WHERE m.name = $method_name
        RETURN c.name as class_name, c.full_name as class_full_name,
               m.name as method_name, m.params_list as params_list, 
               m.params_detailed as params_detailed, m.return_type as return_type, m.args as args
        ORDER BY c.name
        LIMIT 20
        """
        result = await session.run(query, method_name=method_name)
    
    methods = []
    async for record in result:
        # Use detailed params if available, fall back to simple params
        params_to_use = record['params_detailed'] or record['params_list'] or []
        methods.append({
            'class_name': record['class_name'],
            'class_full_name': record['class_full_name'],
            'method_name': record['method_name'],
            'parameters': params_to_use,
            'return_type': record['return_type'] or 'Any',
            'legacy_args': record['args'] or []
        })
    
    if not methods:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Method '{method_name}'" + (f" in class '{class_name}'" if class_name else "") + " not found"
        }, indent=2)
    
    return json.dumps({
        "success": True,
        "command": command,
        "data": {
            "methods": methods,
            "class_filter": class_name
        },
        "metadata": {
            "total_results": len(methods),
            "limited": len(methods) >= 20 and not class_name
        }
    }, indent=2)


async def _handle_query_command(session, command: str, cypher_query: str) -> str:
    """Handle 'query <cypher>' command - execute custom Cypher query"""
    try:
        # Execute the query with a limit to prevent overwhelming responses
        result = await session.run(cypher_query)
        
        records = []
        count = 0
        async for record in result:
            records.append(dict(record))
            count += 1
            if count >= 20:  # Limit results to prevent overwhelming responses
                break
        
        return json.dumps({
            "success": True,
            "command": command,
            "data": {
                "query": cypher_query,
                "results": records
            },
            "metadata": {
                "total_results": len(records),
                "limited": len(records) >= 20
            }
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": command,
            "error": f"Cypher query error: {str(e)}",
            "data": {
                "query": cypher_query
            }
        }, indent=2)


@mcp.tool()
async def parse_github_repository(ctx: Context, repo_url: str) -> str:
    """
    Parse a GitHub repository into the Neo4j knowledge graph.
    
    This tool clones a GitHub repository, analyzes its Python files, and stores
    the code structure (classes, methods, functions, imports) in Neo4j for use
    in hallucination detection. The tool:
    
    - Clones the repository to a temporary location
    - Analyzes Python files to extract code structure
    - Stores classes, methods, functions, and imports in Neo4j
    - Provides detailed statistics about the parsing results
    - Automatically handles module name detection for imports
    
    Args:
        ctx: The MCP server provided context
        repo_url: GitHub repository URL (e.g., 'https://github.com/user/repo.git')
    
    Returns:
        JSON string with parsing results, statistics, and repository information
    """
    try:
        # Check if knowledge graph functionality is enabled
        knowledge_graph_enabled = os.getenv("USE_KNOWLEDGE_GRAPH", "false") == "true"
        if not knowledge_graph_enabled:
            return json.dumps({
                "success": False,
                "error": "Knowledge graph functionality is disabled. Set USE_KNOWLEDGE_GRAPH=true in environment."
            }, indent=2)
        
        # Get the repository extractor safely
        repo_extractor = get_repo_extractor()
        
        if not repo_extractor:
            return json.dumps({
                "success": False,
                "error": "Repository extractor not available. Check Neo4j configuration in environment variables."
            }, indent=2)
        
        # Validate repository URL
        validation = validate_github_url(repo_url)
        if not validation["valid"]:
            return json.dumps({
                "success": False,
                "repo_url": repo_url,
                "error": validation["error"]
            }, indent=2)
        
        repo_name = validation["repo_name"]
        
        # Parse the repository (this includes cloning, analysis, and Neo4j storage)
        print(f"Starting repository analysis for: {repo_name}")
        await repo_extractor.analyze_repository(repo_url)
        print(f"Repository analysis completed for: {repo_name}")
        
        # Query Neo4j for statistics about the parsed repository
        async with repo_extractor.driver.session() as session:
            # Get comprehensive repository statistics
            stats_query = """
            MATCH (r:Repository {name: $repo_name})
            OPTIONAL MATCH (r)-[:CONTAINS]->(f:File)
            OPTIONAL MATCH (f)-[:DEFINES]->(c:Class)
            OPTIONAL MATCH (c)-[:HAS_METHOD]->(m:Method)
            OPTIONAL MATCH (f)-[:DEFINES]->(func:Function)
            OPTIONAL MATCH (c)-[:HAS_ATTRIBUTE]->(a:Attribute)
            WITH r, 
                 count(DISTINCT f) as files_count,
                 count(DISTINCT c) as classes_count,
                 count(DISTINCT m) as methods_count,
                 count(DISTINCT func) as functions_count,
                 count(DISTINCT a) as attributes_count
            
            // Get some sample module names
            OPTIONAL MATCH (r)-[:CONTAINS]->(sample_f:File)
            WITH r, files_count, classes_count, methods_count, functions_count, attributes_count,
                 collect(DISTINCT sample_f.module_name)[0..5] as sample_modules
            
            RETURN 
                r.name as repo_name,
                files_count,
                classes_count, 
                methods_count,
                functions_count,
                attributes_count,
                sample_modules
            """
            
            result = await session.run(stats_query, repo_name=repo_name)
            record = await result.single()
            
            if record:
                stats = {
                    "repository": record['repo_name'],
                    "files_processed": record['files_count'],
                    "classes_created": record['classes_count'],
                    "methods_created": record['methods_count'], 
                    "functions_created": record['functions_count'],
                    "attributes_created": record['attributes_count'],
                    "sample_modules": record['sample_modules'] or []
                }
            else:
                return json.dumps({
                    "success": False,
                    "repo_url": repo_url,
                    "error": f"Repository '{repo_name}' not found in database after parsing"
                }, indent=2)
        
        return json.dumps({
            "success": True,
            "repo_url": repo_url,
            "repo_name": repo_name,
            "message": f"Successfully parsed repository '{repo_name}' into knowledge graph",
            "statistics": stats,
            "ready_for_validation": True,
            "next_steps": [
                "Repository is now available for hallucination detection",
                f"Use check_ai_script_hallucinations to validate scripts against {repo_name}",
                "The knowledge graph contains classes, methods, and functions from this repository"
            ]
        }, indent=2)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "repo_url": repo_url,
            "error": f"Repository parsing failed: {str(e)}"
        }, indent=2)

async def crawl_markdown_file(crawler: AsyncWebCrawler, url: str) -> List[Dict[str, Any]]:
    """
    Crawl a .txt or markdown file with robust error handling.
    
    Args:
        crawler: AsyncWebCrawler instance
        url: URL of the file
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    try:
        result = await robust_crawl_with_retry(crawler, url, max_retries=3, base_timeout=30000)
        if result.success and result.markdown:
            return [{'url': url, 'markdown': result.markdown}]
        else:
            print(f"❌ Failed to crawl {url}: {result.error_message}")
            return []
    except Exception as e:
        print(f"🚨 Error crawling markdown file {url}: {str(e)}")
        return []

async def crawl_batch(crawler: AsyncWebCrawler, urls: List[str], max_concurrent: int = 10) -> List[Dict[str, Any]]:
    """
    Batch crawl multiple URLs in parallel with robust error handling.
    
    Args:
        crawler: AsyncWebCrawler instance
        urls: List of URLs to crawl
        max_concurrent: Maximum number of concurrent browser sessions
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    try:
        crawl_result = await robust_multi_crawl_with_monitoring(crawler, urls, max_concurrent, base_timeout=30000)
        
        # Extract successful results and convert to expected format
        results = []
        for result in crawl_result['successful_results']:
            if result.success and result.markdown:
                results.append({'url': result.url, 'markdown': result.markdown})
        
        # Log statistics
        stats = crawl_result['stats']
        print(f"📊 Batch crawl stats: {stats['successful']}/{stats['total_urls']} successful ({stats['success_rate']:.1f}%)")
        
        if crawl_result['failed_urls']:
            print(f"❌ Failed URLs: {len(crawl_result['failed_urls'])}")
            for failure in crawl_result['failed_urls'][:5]:  # Show first 5 failures
                print(f"   • {failure['url']}: {failure['error']}")
        
        return results
    
    except Exception as e:
        print(f"🚨 Batch crawl error: {str(e)}")
        return []

async def crawl_recursive_internal_links(crawler: AsyncWebCrawler, start_urls: List[str], max_depth: int = 3, max_concurrent: int = 10) -> List[Dict[str, Any]]:
    """
    Recursively crawl internal links from start URLs up to a maximum depth with robust error handling.
    
    Args:
        crawler: AsyncWebCrawler instance
        start_urls: List of starting URLs
        max_depth: Maximum recursion depth
        max_concurrent: Maximum number of concurrent browser sessions
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    visited = set()
    results_all = []

    def normalize_url(url):
        return urldefrag(url)[0]

    current_urls = set([normalize_url(u) for u in start_urls])
    print(f"🔗 Starting recursive crawl: depth={max_depth}, concurrent={max_concurrent}")

    try:
        for depth in range(max_depth):
            urls_to_crawl = [url for url in current_urls if url not in visited]
            if not urls_to_crawl:
                print(f"📄 Depth {depth}: No new URLs to crawl, stopping")
                break

            print(f"🕷️ Depth {depth + 1}/{max_depth}: Crawling {len(urls_to_crawl)} URLs")
            
            # Use robust multi-crawl for each level
            crawl_result = await robust_multi_crawl_with_monitoring(
                crawler, urls_to_crawl, max_concurrent, base_timeout=30000
            )
            
            next_level_urls = set()

            for result in crawl_result['successful_results']:
                norm_url = normalize_url(result.url)
                visited.add(norm_url)

                if result.success and result.markdown:
                    results_all.append({'url': result.url, 'markdown': result.markdown})
                    
                    # Extract internal links for next depth level
                    if hasattr(result, 'links') and result.links:
                        for link in result.links.get("internal", []):
                            if isinstance(link, dict) and "href" in link:
                                next_url = normalize_url(link["href"])
                                if next_url not in visited:
                                    next_level_urls.add(next_url)

            # Log progress
            stats = crawl_result['stats']
            print(f"📊 Depth {depth + 1} complete: {len(results_all)} total pages, {stats['success_rate']:.1f}% success rate")
            
            if crawl_result['failed_urls']:
                print(f"❌ Failed at depth {depth + 1}: {len(crawl_result['failed_urls'])} URLs")

            current_urls = next_level_urls

        print(f"✅ Recursive crawl complete: {len(results_all)} pages collected across {max_depth} depth levels")
        return results_all

    except Exception as e:
        print(f"🚨 Recursive crawl error: {str(e)}")
        return results_all  # Return what we've collected so far

# ============================================================================
# JOB MANAGEMENT MCP TOOLS
# ============================================================================

@mcp.tool()
async def start_crawl_job(
    ctx: Context, 
    job_type: str, 
    url: str, 
    max_depth: int = 3, 
    max_concurrent: int = 10, 
    chunk_size: int = 5000,
    priority: int = 5
) -> str:
    """
    Start a crawl job that runs in the background to avoid MCP timeout issues.
    
    This tool creates a job and queues it for background processing, returning
    immediately with a job ID. Use get_job_status to monitor progress.
    
    Args:
        job_type: Type of crawl job ('single_page', 'smart_crawl', 'sitemap_crawl', 'text_file_crawl', 'repository_parse')
        url: URL to crawl or repository URL for parsing
        max_depth: Maximum crawl depth for multi-page crawls (default: 3)
        max_concurrent: Maximum concurrent sessions (default: 10)
        chunk_size: Text chunk size in characters (default: 5000)
        priority: Job priority 1-10, higher is more important (default: 5)
        
    Returns:
        JSON string with job ID and creation status
    """
    try:
        job_manager = get_job_manager()
        
        # Validate job type
        valid_types = ['single_page', 'smart_crawl', 'sitemap_crawl', 'text_file_crawl', 'repository_parse']
        if job_type not in valid_types:
            return json.dumps({
                "success": False,
                "error": f"Invalid job_type. Must be one of: {', '.join(valid_types)}",
                "valid_types": valid_types
            })
        
        # Validate priority
        if not (1 <= priority <= 10):
            return json.dumps({
                "success": False,
                "error": "Priority must be between 1 and 10"
            })
        
        # Validate URL
        if not url or not url.strip():
            return json.dumps({
                "success": False,
                "error": "URL is required"
            })
        
        # Create job parameters
        parameters = {
            "url": url.strip(),
            "max_depth": max_depth,
            "max_concurrent": max_concurrent,
            "chunk_size": chunk_size
        }
        
        # Create the job
        job_id = await job_manager.create_job(
            job_type=JobType(job_type),
            parameters=parameters,
            priority=priority
        )
        
        return json.dumps({
            "success": True,
            "job_id": job_id,
            "job_type": job_type,
            "status": "queued",
            "message": f"Job created successfully and queued for processing",
            "parameters": parameters,
            "priority": priority,
            "next_steps": [
                f"Use get_job_status with job_id '{job_id}' to monitor progress",
                f"Use get_job_results with job_id '{job_id}' to retrieve results when completed"
            ]
        })
        
    except Exception as e:
        logger.error(f"Failed to start crawl job: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"Failed to create job: {str(e)}"
        })

@mcp.tool()
async def get_job_status(ctx: Context, job_id: str) -> str:
    """
    Get the current status and progress of a crawl job.
    
    This tool provides real-time status updates for background jobs,
    including progress percentage, current operation, and timing information.
    
    Args:
        job_id: Job ID returned from start_crawl_job
        
    Returns:
        JSON string with detailed job status and progress information
    """
    try:
        job_manager = get_job_manager()
        job_info = await job_manager.get_job_status(job_id)
        
        if not job_info:
            return json.dumps({
                "success": False,
                "error": "Job not found",
                "job_id": job_id
            })
        
        # Calculate timing information
        processing_time = None
        if job_info.started_at:
            if job_info.completed_at:
                processing_time = (job_info.completed_at - job_info.started_at).total_seconds() / 60.0
            else:
                processing_time = (datetime.now(timezone.utc) - job_info.started_at).total_seconds() / 60.0
        
        # Determine estimated completion time
        estimated_completion = None
        if job_info.status == JobStatus.RUNNING and job_info.progress_percent > 0:
            if processing_time:
                estimated_total_time = processing_time * (100 / job_info.progress_percent)
                estimated_remaining = max(0, estimated_total_time - processing_time)
                estimated_completion = f"{estimated_remaining:.1f} minutes"
        
        return json.dumps({
            "success": True,
            "job_id": job_id,
            "job_type": job_info.job_type.value,
            "status": job_info.status.value,
            "progress": {
                "percent": job_info.progress_percent,
                "current_operation": job_info.current_operation,
                "completed_operations": job_info.completed_operations,
                "total_operations": job_info.total_operations
            },
            "metrics": {
                "pages_crawled": job_info.pages_crawled,
                "pages_processed": job_info.pages_processed,
                "chunks_created": job_info.chunks_created
            },
            "timing": {
                "created_at": job_info.created_at.isoformat(),
                "started_at": job_info.started_at.isoformat() if job_info.started_at else None,
                "completed_at": job_info.completed_at.isoformat() if job_info.completed_at else None,
                "processing_time_minutes": f"{processing_time:.1f}" if processing_time else None,
                "estimated_completion": estimated_completion
            },
            "parameters": job_info.parameters,
            "priority": job_info.priority,
            "error": job_info.error_message,
            "next_steps": [
                "Use get_job_results to retrieve detailed results when status is 'completed'",
                "Use cancel_job to cancel if status is 'queued' or 'running'"
            ] if job_info.status not in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED] else [
                "Use get_job_results to retrieve detailed results and processing history"
            ]
        })
        
    except Exception as e:
        logger.error(f"Failed to get job status: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"Failed to get job status: {str(e)}",
            "job_id": job_id
        })

@mcp.tool()
async def get_job_results(ctx: Context, job_id: str) -> str:
    """
    Get detailed results for a completed crawl job.
    
    This tool retrieves comprehensive results including crawled content,
    processing statistics, and complete progress history for completed jobs.
    
    Args:
        job_id: Job ID returned from start_crawl_job
        
    Returns:
        JSON string with detailed job results and processing history
    """
    try:
        job_manager = get_job_manager()
        results = await job_manager.get_job_results(job_id)
        
        # If there's an error in the results, return it
        if "error" in results:
            return json.dumps({
                "success": False,
                "job_id": job_id,
                "error": results["error"],
                "status": results.get("status"),
                "progress": results.get("progress")
            })
        
        # Add summary statistics
        progress_history = results.get("progress_history", [])
        processing_stats = {
            "total_progress_updates": len(progress_history),
            "processing_milestones": [
                p for p in progress_history 
                if p.get("progress_percent", 0) in [25, 50, 75, 100]
            ]
        }
        
        return json.dumps({
            "success": True,
            "job_id": job_id,
            "status": results["status"],
            "job_type": results["job_type"],
            "summary": {
                "pages_crawled": results["pages_crawled"],
                "pages_processed": results["pages_processed"],
                "chunks_created": results["chunks_created"],
                "processing_time_minutes": results["processing_time_minutes"],
                "success_rate": f"{(results['pages_processed'] / max(results['pages_crawled'], 1) * 100):.1f}%" if results["pages_crawled"] > 0 else "0%"
            },
            "timing": {
                "created_at": results["created_at"],
                "completed_at": results["completed_at"]
            },
            "detailed_results": results["detailed_results"],
            "progress_history": progress_history,
            "processing_stats": processing_stats,
            "result_summary": results["summary"],
            "next_steps": [
                "Query the crawled content using perform_rag_query",
                "Search for code examples using search_code_examples",
                "View available sources using get_available_sources"
            ]
        })
        
    except Exception as e:
        logger.error(f"Failed to get job results: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"Failed to get job results: {str(e)}",
            "job_id": job_id
        })

@mcp.tool()
async def cancel_job(ctx: Context, job_id: str) -> str:
    """
    Cancel a queued or running crawl job.
    
    This tool attempts to gracefully cancel a job that is either queued
    for processing or currently running. Completed jobs cannot be cancelled.
    
    Args:
        job_id: Job ID returned from start_crawl_job
        
    Returns:
        JSON string with cancellation status
    """
    try:
        job_manager = get_job_manager()
        
        # Get current job status first
        job_info = await job_manager.get_job_status(job_id)
        if not job_info:
            return json.dumps({
                "success": False,
                "error": "Job not found",
                "job_id": job_id
            })
        
        # Check if job can be cancelled
        if job_info.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            return json.dumps({
                "success": False,
                "error": f"Cannot cancel job with status '{job_info.status.value}'",
                "job_id": job_id,
                "current_status": job_info.status.value,
                "message": "Only queued or running jobs can be cancelled"
            })
        
        # Attempt to cancel the job
        cancelled = await job_manager.cancel_job(job_id)
        
        if cancelled:
            return json.dumps({
                "success": True,
                "job_id": job_id,
                "message": "Job cancelled successfully",
                "previous_status": job_info.status.value,
                "previous_progress": job_info.progress_percent,
                "cancelled_at": datetime.now(timezone.utc).isoformat()
            })
        else:
            return json.dumps({
                "success": False,
                "error": "Failed to cancel job",
                "job_id": job_id,
                "current_status": job_info.status.value,
                "message": "Job may have completed or failed before cancellation could be processed"
            })
            
    except Exception as e:
        logger.error(f"Failed to cancel job: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"Failed to cancel job: {str(e)}",
            "job_id": job_id
        })

@mcp.tool()
async def list_jobs(
    ctx: Context, 
    status_filter: Optional[str] = None, 
    job_type_filter: Optional[str] = None,
    limit: int = 20,
    offset: int = 0
) -> str:
    """
    List crawl jobs with optional filtering and pagination.
    
    This tool provides a comprehensive view of all jobs in the system,
    with filtering options and queue statistics for monitoring purposes.
    
    Args:
        status_filter: Filter by job status ('queued', 'running', 'completed', 'failed', 'cancelled')
        job_type_filter: Filter by job type ('single_page', 'smart_crawl', etc.)
        limit: Maximum number of jobs to return (default: 20, max: 100)
        offset: Number of jobs to skip for pagination (default: 0)
        
    Returns:
        JSON string with jobs list, filtering info, and queue statistics
    """
    try:
        job_manager = get_job_manager()
        
        # Validate and convert filters
        status_enum = None
        if status_filter:
            try:
                status_enum = JobStatus(status_filter)
            except ValueError:
                valid_statuses = [s.value for s in JobStatus]
                return json.dumps({
                    "success": False,
                    "error": f"Invalid status_filter. Must be one of: {', '.join(valid_statuses)}",
                    "valid_statuses": valid_statuses
                })
        
        type_enum = None
        if job_type_filter:
            try:
                type_enum = JobType(job_type_filter)
            except ValueError:
                valid_types = [t.value for t in JobType]
                return json.dumps({
                    "success": False,
                    "error": f"Invalid job_type_filter. Must be one of: {', '.join(valid_types)}",
                    "valid_types": valid_types
                })
        
        # Validate pagination parameters
        limit = min(max(1, limit), 100)  # Clamp between 1 and 100
        offset = max(0, offset)
        
        # Get jobs list
        results = await job_manager.list_jobs(
            status_filter=status_enum,
            job_type_filter=type_enum,
            limit=limit,
            offset=offset
        )
        
        if "error" in results:
            return json.dumps({
                "success": False,
                "error": results["error"]
            })
        
        # Format jobs for display
        formatted_jobs = []
        for job in results["jobs"]:
            formatted_jobs.append({
                "job_id": job["id"],
                "job_type": job["job_type"],
                "status": job["status"],
                "progress_percent": job["progress_percent"],
                "current_operation": job["current_operation"],
                "pages_crawled": job["pages_crawled"],
                "chunks_created": job["chunks_created"],
                "created_at": job["created_at"],
                "processing_time_minutes": job["processing_time_minutes"],
                "latest_message": job["latest_message"]
            })
        
        return json.dumps({
            "success": True,
            "jobs": formatted_jobs,
            "pagination": {
                "total_returned": len(formatted_jobs),
                "limit": limit,
                "offset": offset,
                "has_more": len(formatted_jobs) == limit  # Assume more if we got exactly limit
            },
            "filters": {
                "status_filter": status_filter,
                "job_type_filter": job_type_filter
            },
            "queue_statistics": results["queue_stats"],
            "summary": {
                "active_jobs": results["queue_stats"]["queued_jobs"] + results["queue_stats"]["running_jobs"],
                "completed_jobs": results["queue_stats"]["completed_jobs"],
                "failed_jobs": results["queue_stats"]["failed_jobs"],
                "total_jobs": results["queue_stats"]["total_jobs"]
            }
        })
        
    except Exception as e:
        logger.error(f"Failed to list jobs: {str(e)}")
        return json.dumps({
            "success": False,
            "error": f"Failed to list jobs: {str(e)}"
        })

def main():
    import os
    
    logger.info(f"🚀 Starting MCP server in container mode")
    logger.info("🔗 Using stdio transport (containers work with MCP client connections)")
    
    # For containers, we always use stdio transport since the MCP protocol
    # handles the connection management, and clients can connect via HTTP/SSE
    # to the container's exposed port through docker port mapping
    mcp.run()

if __name__ == "__main__":
    main()