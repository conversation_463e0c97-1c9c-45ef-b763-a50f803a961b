#!/usr/bin/env python3
"""
BGE Embedding Service
Self-hosted BAAI bge-base-en-v1.5 embedding server for MCP Crawl4AI RAG
"""

import os
import time
import logging
from typing import List, Dict, Any
from flask import Flask, request, jsonify
import torch
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Global model instance
model = None

def initialize_model():
    """Initialize the BGE model with GPU support"""
    global model
    
    try:
        # Configure GPU
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'
        
        # Import FlagEmbedding
        from FlagEmbedding import FlagModel
        
        logger.info("Loading BAAI bge-base-en-v1.5 model...")
        start_time = time.time()
        
        # Initialize model with GPU support and optimization
        model = FlagModel(
            'BAAI/bge-base-en-v1.5', 
            devices=0,  # Use first GPU
            use_fp16=True,  # Enable mixed precision for better performance
            query_instruction_for_retrieval="Represent this sentence for searching relevant passages:"
        )
        
        load_time = time.time() - start_time
        logger.info(f"Model loaded successfully in {load_time:.2f} seconds!")
        
        # Verify GPU usage
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            logger.info(f"Using GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            logger.warning("CUDA not available, falling back to CPU")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize model: {e}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        gpu_available = torch.cuda.is_available()
        gpu_memory_used = 0
        gpu_memory_total = 0
        
        if gpu_available:
            gpu_memory_used = torch.cuda.memory_allocated(0) / (1024**2)  # MB
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**2)  # MB
        
        return jsonify({
            'status': 'healthy',
            'model': 'BAAI/bge-base-en-v1.5',
            'dimensions': 768,
            'model_loaded': model is not None,
            'cuda_available': gpu_available,
            'gpu_memory_used_mb': gpu_memory_used,
            'gpu_memory_total_mb': gpu_memory_total,
            'gpu_utilization_percent': (gpu_memory_used / gpu_memory_total * 100) if gpu_memory_total > 0 else 0
        })
    except Exception as e:
        return jsonify({'status': 'error', 'error': str(e)}), 500

@app.route('/embed', methods=['POST'])
def embed_text():
    """Generate embeddings for input texts"""
    try:
        if model is None:
            return jsonify({'error': 'Model not initialized'}), 500
        
        data = request.json
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
            
        texts = data.get('texts', [])
        if not texts:
            return jsonify({'error': 'No texts provided'}), 400
        
        if not isinstance(texts, list):
            return jsonify({'error': 'texts must be a list'}), 400
        
        # Validate input
        if len(texts) > 100:  # Reasonable batch size limit
            return jsonify({'error': 'Too many texts (max 100 per request)'}), 400
        
        start_time = time.time()
        
        # Generate embeddings
        embeddings = model.encode(texts, batch_size=32)
        
        # Convert to list for JSON serialization
        embeddings_list = embeddings.tolist()
        
        processing_time = time.time() - start_time
        
        logger.info(f"Generated {len(embeddings_list)} embeddings in {processing_time:.3f}s")
        
        return jsonify({
            'embeddings': embeddings_list,
            'dimensions': len(embeddings_list[0]) if embeddings_list else 768,
            'count': len(embeddings_list),
            'processing_time_seconds': processing_time,
            'model': 'BAAI/bge-base-en-v1.5'
        })
        
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/embed/single', methods=['POST'])
def embed_single_text():
    """Generate embedding for a single text (convenience endpoint)"""
    try:
        if model is None:
            return jsonify({'error': 'Model not initialized'}), 500
        
        data = request.json
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
            
        text = data.get('text', '')
        if not text:
            return jsonify({'error': 'No text provided'}), 400
        
        start_time = time.time()
        
        # Generate embedding for single text
        embedding = model.encode([text])[0]
        
        processing_time = time.time() - start_time
        
        return jsonify({
            'embedding': embedding.tolist(),
            'dimensions': len(embedding),
            'processing_time_seconds': processing_time,
            'model': 'BAAI/bge-base-en-v1.5'
        })
        
    except Exception as e:
        logger.error(f"Error generating single embedding: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/metrics', methods=['GET'])
def get_metrics():
    """Get system metrics"""
    try:
        metrics = {
            'model_loaded': model is not None,
            'cuda_available': torch.cuda.is_available()
        }
        
        if torch.cuda.is_available():
            metrics.update({
                'gpu_memory_allocated_mb': torch.cuda.memory_allocated(0) / (1024**2),
                'gpu_memory_cached_mb': torch.cuda.memory_reserved(0) / (1024**2),
                'gpu_memory_total_mb': torch.cuda.get_device_properties(0).total_memory / (1024**2),
                'gpu_name': torch.cuda.get_device_name(0)
            })
        
        return jsonify(metrics)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/', methods=['GET'])
def root():
    """Root endpoint with API information"""
    return jsonify({
        'service': 'BGE Embedding Server',
        'model': 'BAAI/bge-base-en-v1.5',
        'version': '1.0.0',
        'endpoints': {
            '/health': 'Health check',
            '/embed': 'Batch embedding generation',
            '/embed/single': 'Single text embedding',
            '/metrics': 'System metrics'
        },
        'dimensions': 768
    })

def main():
    """Main application entry point"""
    # Initialize model
    if not initialize_model():
        logger.error("Failed to initialize model, exiting...")
        exit(1)
    
    # Get configuration from environment
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '8080'))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting BGE Embedding Server on {host}:{port}")
    
    # Run the application
    app.run(host=host, port=port, debug=debug, threaded=True)

# Initialize model when module loads (for Gunicorn)
if __name__ != '__main__':
    if not initialize_model():
        logger.error("Failed to initialize model during module load")
    else:
        logger.info("Model initialized successfully during module load")

if __name__ == '__main__':
    main()