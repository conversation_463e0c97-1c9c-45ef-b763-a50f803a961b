#!/bin/bash

# Start Job System Script for MCP Crawl4AI RAG Server
# This script manages both the MCP server and background job workers

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SRC_DIR="$PROJECT_ROOT/src"
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/pids"

# Create necessary directories
mkdir -p "$LOG_DIR" "$PID_DIR"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

print_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Function to check if a process is running
is_running() {
    local pidfile="$1"
    if [[ -f "$pidfile" ]]; then
        local pid=$(cat "$pidfile")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pidfile"
            return 1
        fi
    fi
    return 1
}

# Function to stop a process
stop_process() {
    local name="$1"
    local pidfile="$2"
    
    if is_running "$pidfile"; then
        local pid=$(cat "$pidfile")
        print_status "Stopping $name (PID: $pid)..."
        
        # Send SIGTERM first
        kill -TERM "$pid" 2>/dev/null || true
        
        # Wait up to 10 seconds for graceful shutdown
        for i in {1..10}; do
            if ! kill -0 "$pid" 2>/dev/null; then
                print_status "$name stopped gracefully"
                rm -f "$pidfile"
                return 0
            fi
            sleep 1
        done
        
        # Force kill if still running
        print_warning "$name did not stop gracefully, force killing..."
        kill -KILL "$pid" 2>/dev/null || true
        rm -f "$pidfile"
        print_status "$name force stopped"
    else
        print_info "$name is not running"
    fi
}

# Function to start MCP server
start_mcp_server() {
    local pidfile="$PID_DIR/mcp_server.pid"
    local logfile="$LOG_DIR/mcp_server.log"
    
    if is_running "$pidfile"; then
        print_warning "MCP server is already running (PID: $(cat $pidfile))"
        return 0
    fi
    
    print_status "Starting MCP server..."
    
    cd "$SRC_DIR"
    export TRANSPORT=sse
    
    # Start MCP server in background
    nohup python crawl4ai_mcp.py > "$logfile" 2>&1 &
    local pid=$!
    
    echo "$pid" > "$pidfile"
    
    # Wait a moment and check if it started successfully
    sleep 2
    if is_running "$pidfile"; then
        print_status "MCP server started successfully (PID: $pid)"
        print_info "Logs: $logfile"
        return 0
    else
        print_error "MCP server failed to start"
        cat "$logfile" | tail -20
        return 1
    fi
}

# Function to start job worker
start_job_worker() {
    local worker_id="${1:-worker-1}"
    local pidfile="$PID_DIR/job_worker_${worker_id}.pid"
    local logfile="$LOG_DIR/job_worker_${worker_id}.log"
    
    if is_running "$pidfile"; then
        print_warning "Job worker $worker_id is already running (PID: $(cat $pidfile))"
        return 0
    fi
    
    print_status "Starting job worker $worker_id..."
    
    cd "$SRC_DIR"
    export WORKER_ID="$worker_id"
    
    # Start job worker in background
    nohup python job_worker.py > "$logfile" 2>&1 &
    local pid=$!
    
    echo "$pid" > "$pidfile"
    
    # Wait a moment and check if it started successfully  
    sleep 2
    if is_running "$pidfile"; then
        print_status "Job worker $worker_id started successfully (PID: $pid)"
        print_info "Logs: $logfile"
        return 0
    else
        print_error "Job worker $worker_id failed to start"
        cat "$logfile" | tail -20
        return 1
    fi
}

# Function to show status
show_status() {
    echo ""
    print_info "=== Job System Status ==="
    
    # MCP Server status
    local mcp_pidfile="$PID_DIR/mcp_server.pid"
    if is_running "$mcp_pidfile"; then
        local pid=$(cat "$mcp_pidfile")
        print_status "MCP Server: RUNNING (PID: $pid)"
    else
        print_warning "MCP Server: STOPPED"
    fi
    
    # Job workers status
    local worker_count=0
    for pidfile in "$PID_DIR"/job_worker_*.pid; do
        if [[ -f "$pidfile" ]]; then
            local worker_name=$(basename "$pidfile" .pid)
            if is_running "$pidfile"; then
                local pid=$(cat "$pidfile")
                print_status "$worker_name: RUNNING (PID: $pid)"
                ((worker_count++))
            else
                print_warning "$worker_name: STOPPED"
            fi
        fi
    done
    
    if [[ $worker_count -eq 0 ]]; then
        print_warning "No job workers running"
    fi
    
    echo ""
}

# Function to show logs
show_logs() {
    local service="$1"
    local follow="$2"
    
    case "$service" in
        "mcp"|"server")
            local logfile="$LOG_DIR/mcp_server.log"
            ;;
        "worker"|"worker-1")
            local logfile="$LOG_DIR/job_worker_worker-1.log"
            ;;
        "all")
            print_info "=== MCP Server Logs ==="
            tail -50 "$LOG_DIR/mcp_server.log" 2>/dev/null || echo "No MCP server logs found"
            echo ""
            print_info "=== Job Worker Logs ==="
            tail -50 "$LOG_DIR"/job_worker_*.log 2>/dev/null || echo "No worker logs found"
            return 0
            ;;
        *)
            print_error "Unknown service: $service"
            print_info "Available services: mcp, worker, all"
            return 1
            ;;
    esac
    
    if [[ ! -f "$logfile" ]]; then
        print_error "Log file not found: $logfile"
        return 1
    fi
    
    if [[ "$follow" == "follow" ]] || [[ "$follow" == "-f" ]]; then
        print_info "Following logs for $service (Ctrl+C to stop)..."
        tail -f "$logfile"
    else
        print_info "Last 50 lines of $service logs:"
        tail -50 "$logfile"
    fi
}

# Function to stop all services
stop_all() {
    print_status "Stopping all services..."
    
    # Stop job workers first
    for pidfile in "$PID_DIR"/job_worker_*.pid; do
        if [[ -f "$pidfile" ]]; then
            local worker_name=$(basename "$pidfile" .pid)
            stop_process "$worker_name" "$pidfile"
        fi
    done
    
    # Stop MCP server
    stop_process "MCP Server" "$PID_DIR/mcp_server.pid"
    
    print_status "All services stopped"
}

# Function to start all services
start_all() {
    print_status "Starting all services..."
    
    # Start MCP server first
    if ! start_mcp_server; then
        print_error "Failed to start MCP server, aborting"
        return 1
    fi
    
    # Start default job worker
    if ! start_job_worker "worker-1"; then
        print_error "Failed to start job worker, stopping MCP server"
        stop_process "MCP Server" "$PID_DIR/mcp_server.pid"
        return 1
    fi
    
    print_status "All services started successfully"
    show_status
}

# Function to restart services
restart_all() {
    print_status "Restarting all services..."
    stop_all
    sleep 2
    start_all
}

# Function to check database schema
check_database() {
    print_info "Checking database schema..."
    
    cd "$SRC_DIR"
    python -c "
import sys
import os
sys.path.insert(0, '.')

try:
    from job_manager import get_job_manager
    
    # Test database connection
    job_manager = get_job_manager()
    print('✓ Database connection successful')
    
    # Test PGMQ queue
    result = job_manager.supabase.rpc('pgmq_send', {
        'queue_name': 'test_queue',
        'msg': '{\"test\": true}',
        'delay': 0
    })
    print('✓ PGMQ extension available')
    
    print('✓ Database schema appears to be set up correctly')
    
except Exception as e:
    print(f'✗ Database check failed: {str(e)}')
    print('Please ensure you have run the job_management_schema.sql in your Supabase database')
    sys.exit(1)
"
    
    if [[ $? -eq 0 ]]; then
        print_status "Database check passed"
    else
        print_error "Database check failed"
        return 1
    fi
}

# Main command handling
case "${1:-help}" in
    "start")
        start_all
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        restart_all
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "${2:-all}" "${3}"
        ;;
    "check-db")
        check_database
        ;;
    "start-mcp")
        start_mcp_server
        ;;
    "start-worker")
        start_job_worker "${2:-worker-1}"
        ;;
    "stop-mcp")
        stop_process "MCP Server" "$PID_DIR/mcp_server.pid"
        ;;
    "stop-worker")
        worker_id="${2:-worker-1}"
        stop_process "job_worker_$worker_id" "$PID_DIR/job_worker_${worker_id}.pid"
        ;;
    "help"|*)
        echo ""
        echo "MCP Crawl4AI RAG Server - Job System Management"
        echo ""
        echo "Usage: $0 <command> [options]"
        echo ""
        echo "Commands:"
        echo "  start           Start all services (MCP server + job worker)"
        echo "  stop            Stop all services"
        echo "  restart         Restart all services"
        echo "  status          Show status of all services"
        echo "  logs <service>  Show logs (service: mcp, worker, all)"
        echo "  check-db        Check database schema and connectivity"
        echo ""
        echo "Individual service commands:"
        echo "  start-mcp       Start only MCP server"
        echo "  start-worker [id]  Start job worker (default: worker-1)"
        echo "  stop-mcp        Stop MCP server"
        echo "  stop-worker [id]   Stop job worker (default: worker-1)"
        echo ""
        echo "Examples:"
        echo "  $0 start                    # Start everything"
        echo "  $0 logs mcp                 # Show MCP server logs"
        echo "  $0 logs worker follow       # Follow worker logs"
        echo "  $0 start-worker worker-2    # Start additional worker"
        echo ""
        ;;
esac