#!/usr/bin/env python3
"""Setup script for Integrated Research CLI Assistant."""

import os
import sys
import subprocess
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


def check_python_version() -> bool:
    """Check if Python version is 3.12+."""
    if sys.version_info < (3, 12):
        console.print(f"[red]❌ Python 3.12+ required, found {sys.version_info.major}.{sys.version_info.minor}[/red]")
        return False
    console.print(f"[green]✅ Python {sys.version_info.major}.{sys.version_info.minor}[/green]")
    return True


def check_integrated_structure() -> bool:
    """Check if the integrated project structure is correct."""
    console.print("\n[blue]🔍 Checking integrated project structure...[/blue]")
    
    current_dir = Path(__file__).parent.parent
    required_files = [
        "src/crawl4ai_mcp.py",           # Main MCP server
        "src/cli/research.py",           # CLI main
        "src/cli/mcp_connection.py",     # Connection manager
        "src/cli/config.py",             # Config manager
        "pyproject.toml",                 # Package config
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = current_dir / file_path
        if full_path.exists():
            console.print(f"[green]✅ {file_path}[/green]")
        else:
            console.print(f"[red]❌ {file_path}[/red]")
            missing_files.append(file_path)
    
    if missing_files:
        console.print(f"\n[red]Missing files: {', '.join(missing_files)}[/red]")
        return False
    
    return True


def install_dependencies() -> bool:
    """Install package dependencies."""
    console.print("\n[blue]📦 Installing dependencies...[/blue]")
    
    try:
        current_dir = Path(__file__).parent.parent
        
        # Install in development mode with CLI dependencies
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-e", ".[cli]"
        ], cwd=current_dir, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            console.print("[green]✅ Dependencies installed successfully[/green]")
            return True
        else:
            console.print(f"[red]❌ Installation failed: {result.stderr}[/red]")
            return False
            
    except subprocess.TimeoutExpired:
        console.print("[red]❌ Installation timed out[/red]")
        return False
    except Exception as e:
        console.print(f"[red]❌ Installation error: {e}[/red]")
        return False


def test_integrated_cli() -> bool:
    """Test the integrated CLI functionality."""
    console.print("\n[blue]🧪 Testing integrated CLI...[/blue]")
    
    try:
        # Import CLI components
        sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
        
        from cli.config import config_manager
        from cli.mcp_connection import MCPConnectionManager
        
        # Test config manager
        config = config_manager.get_config()
        console.print(f"[green]✅ Config manager working[/green]")
        
        # Test server detection
        is_valid, errors = config_manager.validate_config()
        if is_valid:
            console.print(f"[green]✅ Server auto-detection working[/green]")
            console.print(f"[dim]  Server path: {config.server_path}[/dim]")
        else:
            console.print(f"[yellow]⚠️  Configuration issues:[/yellow]")
            for error in errors:
                console.print(f"  • {error}")
        
        # Test CLI entry points
        try:
            result = subprocess.run([
                sys.executable, "-c", 
                "from crawl4ai_mcp.cli.research import main; print('CLI import successful')"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                console.print("[green]✅ CLI entry points working[/green]")
            else:
                console.print(f"[yellow]⚠️  CLI import issues: {result.stderr}[/yellow]")
        except Exception as e:
            console.print(f"[yellow]⚠️  CLI entry point test failed: {e}[/yellow]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ CLI test failed: {e}[/red]")
        return False


def show_usage_instructions():
    """Show usage instructions for the integrated CLI."""
    console.print("\n[bold blue]📚 Integrated CLI Usage:[/bold blue]\n")
    
    console.print("[bold green]🔧 Available Commands:[/bold green]")
    commands = [
        ("crawl4ai-research info", "Show server information"),
        ("crawl4ai-research crawl <url>", "Crawl a website"),
        ("crawl4ai-research rag 'query'", "Search with RAG"),
        ("crawl4ai-research sources", "List available sources"),
        ("research-cli info", "Alternative command name"),
    ]
    
    for command, description in commands:
        console.print(f"[cyan]{command:35}[/cyan] {description}")
    
    console.print("\n[bold green]🐍 Python API Usage:[/bold green]")
    console.print("""[cyan]from crawl4ai_mcp.cli import MCPConnectionManager

async with MCPConnectionManager() as manager:
    result = await manager.call_tool("health_check")
    print(result)[/cyan]""")
    
    console.print("\n[bold green]🔧 Development Mode:[/bold green]")
    console.print("[cyan]cd src && python -m cli.research info[/cyan]")
    
    console.print("\n[dim]💡 All server dependencies are automatically included![/dim]")


def main():
    """Main setup function for integrated CLI."""
    console.print(Panel(
        "[bold blue]Integrated Research CLI Setup[/bold blue]\n\n"
        "[green]Setting up Research CLI integrated with crawl4ai-mcp server[/green]",
        style="blue",
        padding=(1, 2)
    ))
    
    # Step 1: Check Python version
    console.print("\n[bold]1. Checking Python version...[/bold]")
    if not check_python_version():
        return 1
    
    # Step 2: Check project structure
    console.print("\n[bold]2. Checking project structure...[/bold]")
    if not check_integrated_structure():
        console.print("\n[red]❌ Project structure incomplete[/red]")
        console.print("Make sure you're running this from the mcp-crawl4ai-rag directory")
        console.print("and that all CLI files have been properly integrated.")
        return 1
    
    # Step 3: Install dependencies
    console.print("\n[bold]3. Installing dependencies...[/bold]")
    if not install_dependencies():
        console.print("\n[red]❌ Dependency installation failed[/red]")
        console.print("Try manually: pip install -e .[cli]")
        return 1
    
    # Step 4: Test integrated CLI
    console.print("\n[bold]4. Testing integrated CLI...[/bold]")
    if not test_integrated_cli():
        console.print("\n[yellow]⚠️  Some CLI tests failed, but basic setup is complete[/yellow]")
    
    # Step 5: Show usage instructions
    show_usage_instructions()
    
    # Success message
    console.print(Panel(
        "[bold green]🎉 Integrated Setup Complete![/bold green]\n\n"
        "[green]Your Research CLI is now integrated with the MCP server![/green]\n\n"
        "[bold]Quick Start:[/bold]\n"
        "[cyan]crawl4ai-research info[/cyan]\n\n"
        "[dim]All dependencies are shared - no duplicate installs needed![/dim]",
        style="green",
        padding=(1, 2)
    ))
    
    return 0


if __name__ == "__main__":
    exit(main())