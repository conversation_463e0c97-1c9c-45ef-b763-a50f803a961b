#!/bin/bash
#
# Production startup script for MCP Crawl4AI RAG Server
# Includes health checks, monitoring, and graceful shutdown
#

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PID_DIR="/tmp/crawl4ai"
LOG_DIR="$PROJECT_DIR/logs"
CONFIG_FILE="${CONFIG_FILE:-$PROJECT_DIR/config.yaml}"

# Create directories
mkdir -p "$PID_DIR" "$LOG_DIR"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed"
        exit 1
    fi
    
    # Check uv (optional but recommended)
    if command -v uv &> /dev/null; then
        log_info "Using uv for faster operations"
        PIP_CMD="uv pip"
    else
        PIP_CMD="pip"
    fi
    
    # Check required services
    if ! systemctl is-active --quiet postgresql; then
        log_warning "PostgreSQL is not running"
    fi
    
    # Check environment variables
    if [ -z "$SUPABASE_URL" ]; then
        log_error "SUPABASE_URL is not set"
        exit 1
    fi
    
    if [ -z "$SUPABASE_SERVICE_KEY" ]; then
        log_error "SUPABASE_SERVICE_KEY is not set"
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

# Run diagnostics
run_diagnostics() {
    log_info "Running system diagnostics..."
    
    cd "$PROJECT_DIR"
    python3 -c "
import sys
sys.path.insert(0, 'src')
import asyncio
from operational_tools import DiagnosticTool
from config_manager import get_config

async def run():
    config = get_config()
    tool = DiagnosticTool(config)
    result = await tool.run_diagnostics()
    
    # Check critical issues
    if not result['dependencies']['crawl4ai']['installed']:
        print('ERROR: crawl4ai not installed')
        sys.exit(1)
    
    if not result['dependencies']['supabase']['installed']:
        print('ERROR: supabase not installed')
        sys.exit(1)
        
    print('Diagnostics passed')

asyncio.run(run())
" || {
    log_error "Diagnostics failed"
    exit 1
}
    
    log_success "Diagnostics completed"
}

# Start BGE service
start_bge_service() {
    if [ "${USE_DIRECT_EMBEDDING:-true}" != "true" ]; then
        log_info "Starting BGE embedding service..."
        
        BGE_PID_FILE="$PID_DIR/bge-service.pid"
        BGE_LOG_FILE="$LOG_DIR/bge-service.log"
        
        # Check if already running
        if [ -f "$BGE_PID_FILE" ] && kill -0 $(cat "$BGE_PID_FILE") 2>/dev/null; then
            log_warning "BGE service already running"
        else
            cd "$PROJECT_DIR/services/bge-embedding-server"
            docker-compose up -d > "$BGE_LOG_FILE" 2>&1
            
            # Wait for service to be ready
            log_info "Waiting for BGE service to be ready..."
            for i in {1..30}; do
                if curl -s http://localhost:8080/health > /dev/null; then
                    log_success "BGE service is ready"
                    break
                fi
                sleep 2
            done
        fi
    fi
}

# Start MCP server
start_mcp_server() {
    log_info "Starting MCP server..."
    
    MCP_PID_FILE="$PID_DIR/mcp-server.pid"
    MCP_LOG_FILE="$LOG_DIR/mcp-server.log"
    
    # Check if already running
    if [ -f "$MCP_PID_FILE" ] && kill -0 $(cat "$MCP_PID_FILE") 2>/dev/null; then
        log_warning "MCP server already running (PID: $(cat "$MCP_PID_FILE"))"
        return
    fi
    
    cd "$PROJECT_DIR"
    
    # Start server with configuration
    CRAWL4AI_CONFIG_FILE="$CONFIG_FILE" \
    CRAWL4AI_ENVIRONMENT="production" \
    CRAWL4AI_ENABLE_MONITORING="true" \
    CRAWL4AI_LOG_FILE="$MCP_LOG_FILE" \
    nohup python3 src/crawl4ai_mcp.py > "$MCP_LOG_FILE" 2>&1 &
    
    MCP_PID=$!
    echo $MCP_PID > "$MCP_PID_FILE"
    
    # Wait for server to be ready
    log_info "Waiting for MCP server to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:8051/health > /dev/null; then
            log_success "MCP server is ready (PID: $MCP_PID)"
            break
        fi
        sleep 2
    done
}

# Start job workers
start_job_workers() {
    if [ "${CRAWL4AI_ENABLE_JOB_SYSTEM:-true}" == "true" ]; then
        log_info "Starting job workers..."
        
        WORKER_COUNT="${CRAWL4AI_WORKER_COUNT:-2}"
        
        for i in $(seq 1 $WORKER_COUNT); do
            WORKER_ID="enhanced-worker-$i"
            WORKER_PID_FILE="$PID_DIR/worker-$i.pid"
            WORKER_LOG_FILE="$LOG_DIR/worker-$i.log"
            
            # Check if already running
            if [ -f "$WORKER_PID_FILE" ] && kill -0 $(cat "$WORKER_PID_FILE") 2>/dev/null; then
                log_warning "Worker $i already running"
                continue
            fi
            
            cd "$PROJECT_DIR"
            
            # Start worker
            WORKER_ID="$WORKER_ID" \
            CRAWL4AI_CONFIG_FILE="$CONFIG_FILE" \
            nohup python3 src/enhanced_job_worker.py > "$WORKER_LOG_FILE" 2>&1 &
            
            WORKER_PID=$!
            echo $WORKER_PID > "$WORKER_PID_FILE"
            
            log_success "Started worker $i (PID: $WORKER_PID)"
        done
    fi
}

# Health check
health_check() {
    log_info "Running health check..."
    
    cd "$PROJECT_DIR"
    python3 -c "
import sys
sys.path.insert(0, 'src')
import asyncio
from operational_tools import HealthChecker
from config_manager import get_config

async def run():
    config = get_config()
    checker = HealthChecker(config)
    health = await checker.check_health()
    
    print(f'Status: {health.status}')
    print(f'CPU: {health.cpu_percent:.1f}%')
    print(f'Memory: {health.memory_percent:.1f}%')
    print(f'Checks: {health.checks_passed}/{health.checks_passed + health.checks_failed}')
    
    if health.status == 'unhealthy':
        sys.exit(1)

asyncio.run(run())
" || {
    log_error "Health check failed"
    return 1
}
    
    log_success "Health check passed"
}

# Stop all services
stop_all() {
    log_info "Stopping all services..."
    
    # Stop MCP server
    if [ -f "$PID_DIR/mcp-server.pid" ]; then
        PID=$(cat "$PID_DIR/mcp-server.pid")
        if kill -0 $PID 2>/dev/null; then
            log_info "Stopping MCP server (PID: $PID)..."
            kill -TERM $PID
            sleep 2
            if kill -0 $PID 2>/dev/null; then
                kill -KILL $PID
            fi
        fi
        rm -f "$PID_DIR/mcp-server.pid"
    fi
    
    # Stop workers
    for pid_file in $PID_DIR/worker-*.pid; do
        if [ -f "$pid_file" ]; then
            PID=$(cat "$pid_file")
            if kill -0 $PID 2>/dev/null; then
                log_info "Stopping worker (PID: $PID)..."
                kill -TERM $PID
            fi
            rm -f "$pid_file"
        fi
    done
    
    # Stop BGE service
    if [ "${USE_DIRECT_EMBEDDING:-true}" != "true" ]; then
        cd "$PROJECT_DIR/services/bge-embedding-server"
        docker-compose down
    fi
    
    log_success "All services stopped"
}

# Monitor services
monitor_services() {
    log_info "Starting service monitor..."
    
    while true; do
        # Check MCP server
        if [ -f "$PID_DIR/mcp-server.pid" ]; then
            PID=$(cat "$PID_DIR/mcp-server.pid")
            if ! kill -0 $PID 2>/dev/null; then
                log_error "MCP server crashed, restarting..."
                start_mcp_server
            fi
        fi
        
        # Check workers
        for i in $(seq 1 ${CRAWL4AI_WORKER_COUNT:-2}); do
            PID_FILE="$PID_DIR/worker-$i.pid"
            if [ -f "$PID_FILE" ]; then
                PID=$(cat "$PID_FILE")
                if ! kill -0 $PID 2>/dev/null; then
                    log_error "Worker $i crashed, restarting..."
                    rm -f "$PID_FILE"
                    start_job_workers
                fi
            fi
        done
        
        # Run health check
        if ! health_check > /dev/null 2>&1; then
            log_warning "Health check failed"
        fi
        
        sleep 60
    done
}

# Signal handlers
trap 'log_info "Received shutdown signal"; stop_all; exit 0' SIGINT SIGTERM

# Main command handling
case "${1:-start}" in
    start)
        log_info "Starting MCP Crawl4AI production environment..."
        check_dependencies
        run_diagnostics
        start_bge_service
        start_mcp_server
        start_job_workers
        health_check
        log_success "All services started successfully"
        
        if [ "${2:-}" == "--monitor" ]; then
            monitor_services
        fi
        ;;
        
    stop)
        stop_all
        ;;
        
    restart)
        stop_all
        sleep 2
        $0 start
        ;;
        
    status)
        log_info "Service Status:"
        
        # MCP Server
        if [ -f "$PID_DIR/mcp-server.pid" ] && kill -0 $(cat "$PID_DIR/mcp-server.pid") 2>/dev/null; then
            echo -e "  MCP Server: ${GREEN}Running${NC} (PID: $(cat "$PID_DIR/mcp-server.pid"))"
        else
            echo -e "  MCP Server: ${RED}Stopped${NC}"
        fi
        
        # Workers
        for i in $(seq 1 ${CRAWL4AI_WORKER_COUNT:-2}); do
            PID_FILE="$PID_DIR/worker-$i.pid"
            if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
                echo -e "  Worker $i: ${GREEN}Running${NC} (PID: $(cat "$PID_FILE"))"
            else
                echo -e "  Worker $i: ${RED}Stopped${NC}"
            fi
        done
        
        # Health check
        if health_check > /dev/null 2>&1; then
            echo -e "  Health: ${GREEN}Healthy${NC}"
        else
            echo -e "  Health: ${RED}Unhealthy${NC}"
        fi
        ;;
        
    health)
        health_check
        ;;
        
    logs)
        tail -f $LOG_DIR/*.log
        ;;
        
    diagnostics)
        run_diagnostics
        ;;
        
    *)
        echo "Usage: $0 {start|stop|restart|status|health|logs|diagnostics} [--monitor]"
        exit 1
        ;;
esac