#!/bin/bash

# Start BGE Embedding Service and MCP Server
# Usage: ./start_services.sh [bge|mcp|both|both-bg|logs|test|stop|help]

set -e

BGE_DIR="services/bge-embedding-server"
BGE_URL="http://localhost:8080"
LOG_DIR="logs"
MCP_LOG_FILE="$LOG_DIR/mcp_server.log"
BGE_LOG_FILE="$LOG_DIR/bge_service.log"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

show_help() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  bge     - Start only BGE embedding service"
    echo "  mcp     - Start only MCP server and show live logs"
    echo "  both    - Start both services and show live logs (default)"
    echo "  both-bg - Start both services in background"
    echo "  logs    - Show live logs from running services"
    echo "  restart - Gracefully restart MCP server"
    echo "  status  - Show status of all services"
    echo "  test    - Run integration tests"
    echo "  stop    - Stop all services"
    echo "  help    - Show this help"
    echo ""
    echo "Log Management:"
    echo "  Logs are saved to the logs/ directory:"
    echo "    - logs/mcp_server.log (MCP server logs)"
    echo "    - logs/bge_service.log (BGE service logs)"
    echo "  Use 'tail -f logs/*.log' to follow all logs"
    echo "  Use '$0 logs' to view live logs with color coding"
    echo ""
    echo "Prerequisites:"
    echo "  - Docker and docker-compose installed"
    echo "  - NVIDIA Docker runtime for GPU support"
    echo "  - .env file configured"
}

wait_for_bge_service() {
    echo "Waiting for BGE service to be ready..."
    for i in {1..30}; do
        if curl -s "$BGE_URL/health" > /dev/null 2>&1; then
            echo "✅ BGE service is ready!"
            return 0
        fi
        echo "⏳ Attempt $i/30: BGE service not ready yet..."
        sleep 2
    done
    echo "❌ BGE service did not start within 60 seconds"
    return 1
}

check_mcp_running() {
    # Check if MCP server is already running
    local pids=($(pgrep -f "crawl4ai_mcp.py"))
    if [ ${#pids[@]} -gt 0 ]; then
        echo "⚠️  Found ${#pids[@]} MCP server process(es) running:"
        for pid in "${pids[@]}"; do
            echo "   PID $pid: $(ps -p $pid -o cmd --no-headers 2>/dev/null || echo 'Process info unavailable')"
        done
        return 0
    fi
    return 1
}

check_port_available() {
    if lsof -Pi :8051 -sTCP:LISTEN -t >/dev/null 2>&1; then
        local port_pid=$(lsof -Pi :8051 -sTCP:LISTEN -t)
        echo "❌ Port 8051 is already in use by PID: $port_pid"
        return 1
    fi
    return 0
}

graceful_stop_mcp_server() {
    echo "🛑 Gracefully stopping MCP server..."
    
    # Get all MCP server processes
    local pids=($(pgrep -f "crawl4ai_mcp.py"))
    
    if [ ${#pids[@]} -eq 0 ]; then
        echo "ℹ️  No MCP server processes found"
        # Clean up stale PID file
        [ -f "$LOG_DIR/mcp_server.pid" ] && rm -f "$LOG_DIR/mcp_server.pid"
        return 0
    fi
    
    echo "Found ${#pids[@]} MCP server process(es) to stop..."
    
    for pid in "${pids[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            echo "Sending SIGTERM to PID $pid..."
            kill -TERM "$pid" 2>/dev/null || true
        fi
    done
    
    # Wait up to 15 seconds for graceful shutdown
    echo "Waiting for graceful shutdown..."
    for i in {1..15}; do
        local remaining_pids=($(pgrep -f "crawl4ai_mcp.py"))
        if [ ${#remaining_pids[@]} -eq 0 ]; then
            echo "✅ All MCP server processes stopped gracefully"
            break
        fi
        sleep 1
    done
    
    # Force kill any remaining processes
    local remaining_pids=($(pgrep -f "crawl4ai_mcp.py"))
    if [ ${#remaining_pids[@]} -gt 0 ]; then
        echo "⚠️  Force stopping ${#remaining_pids[@]} remaining process(es)..."
        for pid in "${remaining_pids[@]}"; do
            kill -KILL "$pid" 2>/dev/null || true
            echo "   Force stopped PID $pid"
        done
    fi
    
    # Clean up PID file
    [ -f "$LOG_DIR/mcp_server.pid" ] && rm -f "$LOG_DIR/mcp_server.pid"
    
    echo "✅ MCP server cleanup completed"
}

show_service_status() {
    echo "📊 Service Status Report"
    echo "======================"
    
    # Check MCP Server
    echo "🔍 MCP Server:"
    local mcp_pids=($(pgrep -f "crawl4ai_mcp.py"))
    if [ ${#mcp_pids[@]} -gt 0 ]; then
        echo "   Status: ✅ Running (${#mcp_pids[@]} process(es))"
        for pid in "${mcp_pids[@]}"; do
            local start_time=$(ps -p $pid -o lstart --no-headers 2>/dev/null || echo "Unknown")
            echo "   PID $pid - Started: ${start_time}"
        done
        
        # Check port
        if lsof -Pi :8051 -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo "   Port 8051: ✅ Listening"
        else
            echo "   Port 8051: ❌ Not listening (possible startup issue)"
        fi
    else
        echo "   Status: ❌ Not running"
        if [ -f "$LOG_DIR/mcp_server.pid" ]; then
            echo "   Warning: Stale PID file found - will be cleaned up"
        fi
    fi
    
    # Check BGE Service
    echo "\n🔍 BGE Service:"
    if docker-compose -f "$BGE_DIR/docker-compose.yml" ps -q bge-service >/dev/null 2>&1; then
        local container_status=$(docker-compose -f "$BGE_DIR/docker-compose.yml" ps bge-service --format "table {{.State}}" | tail -n +2)
        echo "   Status: ✅ Container ${container_status}"
        
        if curl -s "$BGE_URL/health" > /dev/null 2>&1; then
            echo "   Health: ✅ Responding at $BGE_URL"
        else
            echo "   Health: ❌ Not responding at $BGE_URL"
        fi
    else
        echo "   Status: ❌ Container not running"
    fi
    
    # Check log files
    echo "\n📋 Log Files:"
    if [ -f "$MCP_LOG_FILE" ]; then
        local log_size=$(du -h "$MCP_LOG_FILE" | cut -f1)
        local log_lines=$(wc -l < "$MCP_LOG_FILE")
        echo "   MCP Log: ✅ $MCP_LOG_FILE ($log_size, $log_lines lines)"
    else
        echo "   MCP Log: ❌ Not found"
    fi
}

start_bge_service() {
    echo "🚀 Starting BGE embedding service..."
    cd "$BGE_DIR"
    
    # Build and start BGE service
    docker-compose up --build -d
    
    cd - > /dev/null
    
    # Wait for service to be ready
    wait_for_bge_service
}

start_mcp_server() {
    echo "🚀 Starting MCP server..."
    
    # Check for existing processes first
    if check_mcp_running; then
        echo "❌ Cannot start - MCP server already running"
        echo "   Use: $0 stop     (to stop)"
        echo "   Use: $0 restart  (to restart)"
        echo "   Use: $0 status   (to check status)"
        exit 1
    fi
    
    # Check if port is available
    if ! check_port_available; then
        echo "❌ Cannot start - Port 8051 is not available"
        echo "   Use: $0 stop     (to stop services)"
        echo "   Use: $0 status   (to check what's using the port)"
        exit 1
    fi
    
    # Check if USE_DIRECT_EMBEDDING is enabled
    use_direct_embedding=$(grep "^USE_DIRECT_EMBEDDING=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "true")
    
    # Only check BGE service if not using direct embedding
    if [ "$use_direct_embedding" != "true" ]; then
        if ! curl -s "$BGE_URL/health" > /dev/null 2>&1; then
            echo "❌ BGE HTTP service is not running and USE_DIRECT_EMBEDDING=false"
            echo "Either start BGE service with: $0 bge"
            echo "Or set USE_DIRECT_EMBEDDING=true in your .env file for direct GPU embedding"
            exit 1
        fi
        echo "✅ Using BGE HTTP service at $BGE_URL"
    else
        echo "✅ Using direct GPU embedding (BGE HTTP service not required)"
    fi
    
    # Start MCP server with logging
    echo "Starting MCP server with uv... (logs: $MCP_LOG_FILE)"
    echo "$(date): Starting MCP server (PID: $$)" >> "$MCP_LOG_FILE"
    
    # Use exec to replace the shell process, so logs stream immediately
    exec uv run src/crawl4ai_mcp.py 2>&1 | tee -a "$MCP_LOG_FILE"
}

start_mcp_server_background() {
    echo "🚀 Starting MCP server in background..."
    
    # Check for existing processes first
    if check_mcp_running; then
        echo "❌ Cannot start - MCP server already running"
        echo "   Use: $0 stop     (to stop)"
        echo "   Use: $0 restart  (to restart)"
        echo "   Use: $0 status   (to check status)"
        exit 1
    fi
    
    # Check if port is available
    if ! check_port_available; then
        echo "❌ Cannot start - Port 8051 is not available"
        echo "   Use: $0 stop     (to stop services)"
        echo "   Use: $0 status   (to check what's using the port)"
        exit 1
    fi
    
    # Check if USE_DIRECT_EMBEDDING is enabled
    use_direct_embedding=$(grep "^USE_DIRECT_EMBEDDING=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "true")
    
    # Only check BGE service if not using direct embedding
    if [ "$use_direct_embedding" != "true" ]; then
        if ! curl -s "$BGE_URL/health" > /dev/null 2>&1; then
            echo "❌ BGE HTTP service is not running and USE_DIRECT_EMBEDDING=false"
            echo "Either start BGE service with: $0 bge"
            echo "Or set USE_DIRECT_EMBEDDING=true in your .env file for direct GPU embedding"
            exit 1
        fi
        echo "✅ Using BGE HTTP service at $BGE_URL"
    else
        echo "✅ Using direct GPU embedding (BGE HTTP service not required)"
    fi
    
    # Start MCP server in background with logging
    echo "Starting MCP server in background... (logs: $MCP_LOG_FILE)"
    echo "$(date): Starting MCP server in background" >> "$MCP_LOG_FILE"
    
    nohup uv run src/crawl4ai_mcp.py >> "$MCP_LOG_FILE" 2>&1 &
    MCP_PID=$!
    echo "MCP server started with PID: $MCP_PID"
    echo "$MCP_PID" > "$LOG_DIR/mcp_server.pid"
    
    # Wait a moment for server to initialize
    sleep 3
    
    # Check if server is still running
    if kill -0 "$MCP_PID" 2>/dev/null; then
        echo "✅ MCP server is running (PID: $MCP_PID)"
        
        # Verify port is listening (additional validation)
        sleep 2
        if lsof -Pi :8051 -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo "✅ Server is listening on port 8051"
        else
            echo "⚠️  Server started but port 8051 not yet ready (may still be initializing)"
        fi
    else
        echo "❌ MCP server failed to start. Check logs: $MCP_LOG_FILE"
        # Clean up PID file if process failed
        [ -f "$LOG_DIR/mcp_server.pid" ] && rm -f "$LOG_DIR/mcp_server.pid"
        return 1
    fi
}

show_logs() {
    echo "📋 Showing live logs from all services..."
    echo "Press Ctrl+C to exit log viewer"
    echo ""
    
    # Check if any MCP processes are running
    if ! check_mcp_running; then
        echo "⚠️  No MCP server processes detected"
    fi
    
    # Check what log files exist
    log_files=()
    if [ -f "$MCP_LOG_FILE" ]; then
        log_files+=("$MCP_LOG_FILE")
        echo "📄 MCP Server logs: $MCP_LOG_FILE"
    fi
    
    # Check for BGE service logs (Docker compose logs)
    if docker-compose -f "$BGE_DIR/docker-compose.yml" ps -q bge-service >/dev/null 2>&1; then
        echo "📄 BGE Service logs: Docker container logs"
        echo ""
        
        if [ ${#log_files[@]} -gt 0 ]; then
            # Show both MCP logs and Docker logs
            {
                echo "=== MCP Server Logs ==="
                tail -f "${log_files[@]}" &
                TAIL_PID=$!
                
                echo "=== BGE Service Logs ==="
                docker-compose -f "$BGE_DIR/docker-compose.yml" logs -f bge-service &
                DOCKER_PID=$!
                
                # Wait for either process to exit
                wait
            }
        else
            # Show only Docker logs
            docker-compose -f "$BGE_DIR/docker-compose.yml" logs -f bge-service
        fi
    elif [ ${#log_files[@]} -gt 0 ]; then
        echo ""
        echo "Following logs with color coding..."
        
        # Use multitail if available, otherwise fall back to tail
        if command -v multitail >/dev/null 2>&1; then
            multitail "${log_files[@]}"
        else
            # Color-coded tail for multiple files
            tail -f "${log_files[@]}" | while IFS= read -r line; do
                if [[ "$line" == "==> "* ]]; then
                    # File headers in cyan
                    echo -e "\033[36m$line\033[0m"
                elif [[ "$line" == *"ERROR"* ]] || [[ "$line" == *"error"* ]]; then
                    # Errors in red
                    echo -e "\033[31m$line\033[0m"
                elif [[ "$line" == *"WARNING"* ]] || [[ "$line" == *"warning"* ]]; then
                    # Warnings in yellow
                    echo -e "\033[33m$line\033[0m"
                elif [[ "$line" == *"INFO"* ]] || [[ "$line" == *"✅"* ]]; then
                    # Info in green
                    echo -e "\033[32m$line\033[0m"
                else
                    # Regular output
                    echo "$line"
                fi
            done
        fi
    else
        echo "❌ No log files found. Start some services first:"
        echo "  $0 mcp    - Start MCP server"
        echo "  $0 bge    - Start BGE service"
        echo "  $0 both   - Start both services"
        return 1
    fi
}

run_tests() {
    echo "🧪 Running BGE integration tests..."
    python test_bge_integration.py
}

stop_services() {
    echo "🛑 Stopping all services..."
    
    # Stop MCP server gracefully
    graceful_stop_mcp_server
    
    # Stop BGE service
    if [ -d "$BGE_DIR" ]; then
        echo "🛑 Stopping BGE service..."
        cd "$BGE_DIR"
        docker-compose down
        cd - > /dev/null
        echo "✅ BGE service stopped"
    fi
    
    echo "✅ All services stopped"
}

restart_mcp_server() {
    echo "🔄 Restarting MCP server..."
    
    # Stop MCP server gracefully
    graceful_stop_mcp_server
    
    # Wait a moment before restart
    sleep 2
    
    # Start MCP server in background
    start_mcp_server_background
}

case "${1:-both}" in
    "bge")
        start_bge_service
        echo "✅ BGE service is running at $BGE_URL"
        echo "Use '$0 mcp' to start the MCP server"
        ;;
    "mcp")
        start_mcp_server
        ;;
    "both")
        # Check if USE_DIRECT_EMBEDDING is enabled to decide on BGE service
        use_direct_embedding=$(grep "^USE_DIRECT_EMBEDDING=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "true")
        
        if [ "$use_direct_embedding" != "true" ]; then
            start_bge_service
            echo ""
        else
            echo "ℹ️  Skipping BGE service (USE_DIRECT_EMBEDDING=true)"
        fi
        
        start_mcp_server
        ;;
    "both-bg")
        # Start both services in background
        use_direct_embedding=$(grep "^USE_DIRECT_EMBEDDING=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "true")
        
        if [ "$use_direct_embedding" != "true" ]; then
            start_bge_service
            echo ""
        else
            echo "ℹ️  Skipping BGE service (USE_DIRECT_EMBEDDING=true)"
        fi
        
        start_mcp_server_background
        echo ""
        echo "✅ Services started in background"
        echo "📋 View logs with: $0 logs"
        echo "📊 Check status with: $0 status"
        ;;
    "logs")
        show_logs
        ;;
    "restart")
        restart_mcp_server
        ;;
    "status")
        show_service_status
        ;;
    "test")
        run_tests
        ;;
    "stop")
        stop_services
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        show_help
        exit 1
        ;;
esac