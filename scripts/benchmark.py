#!/usr/bin/env python3
"""
Performance Benchmarking Script for MCP Crawl4AI RAG Server

Runs comprehensive benchmarks to establish performance baselines.
"""

import asyncio
import time
import statistics
import json
import sys
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import argparse
import psutil
import aiohttp

# Add src to path
src_path = Path(__file__).resolve().parent.parent / "src"
sys.path.insert(0, str(src_path))

from job_manager import JobManager, JobType
from parallel_processor import ParallelCrawler
from monitoring import CrawlMonitor
from utils import get_supabase_client, search_documents
from config_manager import get_config


class BenchmarkRunner:
    """Runs performance benchmarks for the system"""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.results = {}
        self.job_manager = JobManager()
        self.monitor = CrawlMonitor()
        self.config = get_config()
        
    async def run_all_benchmarks(self):
        """Run all benchmark suites"""
        print("🚀 Starting MCP Crawl4AI Performance Benchmarks\n")
        
        # System info
        self._print_system_info()
        
        # Run benchmarks
        await self.benchmark_job_creation()
        await self.benchmark_crawling_performance()
        await self.benchmark_search_performance()
        await self.benchmark_concurrent_operations()
        await self.benchmark_error_handling()
        await self.benchmark_resource_usage()
        
        # Generate report
        self._generate_report()
        
    def _print_system_info(self):
        """Print system information"""
        print("System Information:")
        print(f"  CPU Cores: {psutil.cpu_count()}")
        print(f"  Total RAM: {psutil.virtual_memory().total / (1024**3):.1f} GB")
        print(f"  Python Version: {sys.version.split()[0]}")
        print(f"  Platform: {sys.platform}")
        print()
        
    async def benchmark_job_creation(self):
        """Benchmark job creation and queue operations"""
        print("📊 Benchmarking Job Creation...")
        
        job_counts = [10, 50, 100, 500]
        results = []
        
        for count in job_counts:
            start_time = time.time()
            job_ids = []
            
            # Create jobs
            for i in range(count):
                job_id = await self.job_manager.create_job(
                    job_type=JobType.SINGLE_PAGE,
                    parameters={
                        "url": f"https://example.com/bench/{i}",
                        "chunk_size": 5000
                    },
                    priority=5
                )
                job_ids.append(job_id)
            
            creation_time = time.time() - start_time
            creation_rate = count / creation_time
            
            # Clean up
            for job_id in job_ids:
                await self.job_manager.mark_job_running(job_id)
                await self.job_manager.complete_job(
                    job_id, {"test": True}, pages_crawled=1
                )
            
            results.append({
                "count": count,
                "total_time": creation_time,
                "rate": creation_rate
            })
            
            if self.verbose:
                print(f"  {count} jobs: {creation_rate:.1f} jobs/sec")
        
        self.results["job_creation"] = results
        print(f"  ✅ Best rate: {max(r['rate'] for r in results):.1f} jobs/sec\n")
        
    async def benchmark_crawling_performance(self):
        """Benchmark crawling performance"""
        print("📊 Benchmarking Crawl Performance...")
        
        crawler = ParallelCrawler(
            max_concurrent=5,
            max_depth=0,
            monitor=self.monitor
        )
        
        await crawler.initialize()
        
        try:
            # Test different batch sizes
            batch_sizes = [1, 5, 10, 20]
            results = []
            
            for batch_size in batch_sizes:
                urls = [f"https://httpbin.org/html" for _ in range(batch_size)]
                
                start_time = time.time()
                crawl_results = await crawler.crawl_batch(urls)
                total_time = time.time() - start_time
                
                successful = sum(1 for r in crawl_results if r.success)
                avg_time = total_time / batch_size
                
                results.append({
                    "batch_size": batch_size,
                    "total_time": total_time,
                    "avg_time": avg_time,
                    "success_rate": (successful / batch_size) * 100
                })
                
                if self.verbose:
                    print(f"  Batch {batch_size}: {avg_time:.2f}s avg, "
                          f"{results[-1]['success_rate']:.0f}% success")
                
                # Small delay between tests
                await asyncio.sleep(2)
            
            self.results["crawling"] = results
            print(f"  ✅ Best avg time: {min(r['avg_time'] for r in results):.2f}s\n")
            
        finally:
            await crawler.cleanup()
            
    async def benchmark_search_performance(self):
        """Benchmark search performance"""
        print("📊 Benchmarking Search Performance...")
        
        queries = [
            "python programming",
            "web crawling techniques",
            "machine learning algorithms",
            "data structures and algorithms",
            "software engineering best practices"
        ]
        
        results = []
        
        for query in queries:
            start_time = time.time()
            
            try:
                # Perform search
                search_results = await search_documents(
                    query=query,
                    match_count=10
                )
                
                search_time = time.time() - start_time
                
                results.append({
                    "query": query,
                    "time": search_time,
                    "results": len(search_results)
                })
                
                if self.verbose:
                    print(f"  '{query}': {search_time:.3f}s ({len(search_results)} results)")
                    
            except Exception as e:
                if self.verbose:
                    print(f"  '{query}': Failed - {str(e)}")
                    
        if results:
            self.results["search"] = results
            avg_time = statistics.mean(r["time"] for r in results)
            print(f"  ✅ Average search time: {avg_time:.3f}s\n")
        else:
            print("  ⚠️  No search results (may need indexed content)\n")
            
    async def benchmark_concurrent_operations(self):
        """Benchmark concurrent operation handling"""
        print("📊 Benchmarking Concurrent Operations...")
        
        concurrency_levels = [1, 5, 10, 20]
        results = []
        
        for level in concurrency_levels:
            # Create concurrent jobs
            start_time = time.time()
            
            tasks = []
            for i in range(level):
                task = self.job_manager.create_job(
                    job_type=JobType.SINGLE_PAGE,
                    parameters={
                        "url": f"https://httpbin.org/delay/1",
                        "chunk_size": 1000
                    }
                )
                tasks.append(task)
            
            job_ids = await asyncio.gather(*tasks)
            creation_time = time.time() - start_time
            
            # Simulate processing
            process_tasks = []
            for job_id in job_ids:
                task = self._simulate_job_processing(job_id)
                process_tasks.append(task)
            
            process_start = time.time()
            await asyncio.gather(*process_tasks)
            process_time = time.time() - process_start
            
            results.append({
                "concurrency": level,
                "creation_time": creation_time,
                "process_time": process_time,
                "total_time": creation_time + process_time
            })
            
            if self.verbose:
                print(f"  Level {level}: {results[-1]['total_time']:.2f}s total")
                
        self.results["concurrency"] = results
        print(f"  ✅ Best concurrency: {min(results, key=lambda x: x['total_time'])['concurrency']}\n")
        
    async def _simulate_job_processing(self, job_id: str):
        """Simulate job processing"""
        await self.job_manager.mark_job_running(job_id)
        await asyncio.sleep(0.1)  # Simulate work
        await self.job_manager.complete_job(
            job_id,
            {"processed": True},
            pages_crawled=1
        )
        
    async def benchmark_error_handling(self):
        """Benchmark error handling performance"""
        print("📊 Benchmarking Error Handling...")
        
        from error_handler import ErrorHandler, ErrorContext
        
        error_handler = ErrorHandler()
        error_types = [
            (ConnectionError("Timeout"), "network"),
            (ValueError("Invalid input"), "validation"),
            (IOError("Storage full"), "storage"),
            (Exception("Unknown error"), "unknown")
        ]
        
        results = []
        iterations = 100
        
        for error, expected_category in error_types:
            start_time = time.time()
            
            for _ in range(iterations):
                context = ErrorContext(
                    error_type=type(error),
                    error_message=str(error),
                    stack_trace="",
                    timestamp=datetime.utcnow()
                )
                
                result = await error_handler.handle_error(error, {
                    "url": "https://example.com",
                    "job_id": "test-job"
                })
            
            total_time = time.time() - start_time
            avg_time = (total_time / iterations) * 1000  # Convert to ms
            
            results.append({
                "error_type": type(error).__name__,
                "category": expected_category,
                "avg_time_ms": avg_time
            })
            
            if self.verbose:
                print(f"  {type(error).__name__}: {avg_time:.2f}ms avg")
                
        self.results["error_handling"] = results
        overall_avg = statistics.mean(r["avg_time_ms"] for r in results)
        print(f"  ✅ Average handling time: {overall_avg:.2f}ms\n")
        
    async def benchmark_resource_usage(self):
        """Benchmark resource usage under load"""
        print("📊 Benchmarking Resource Usage...")
        
        # Get baseline
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / (1024**2)  # MB
        baseline_cpu = process.cpu_percent(interval=1)
        
        # Create load
        crawler = ParallelCrawler(max_concurrent=10)
        await crawler.initialize()
        
        # Measure under load
        urls = [f"https://httpbin.org/delay/0" for _ in range(50)]
        
        start_time = time.time()
        task = asyncio.create_task(crawler.crawl_batch(urls))
        
        # Monitor resources during crawl
        max_memory = baseline_memory
        max_cpu = baseline_cpu
        measurements = []
        
        while not task.done():
            memory = process.memory_info().rss / (1024**2)
            cpu = process.cpu_percent(interval=0.1)
            
            max_memory = max(max_memory, memory)
            max_cpu = max(max_cpu, cpu)
            
            measurements.append({
                "memory": memory,
                "cpu": cpu,
                "time": time.time() - start_time
            })
            
            await asyncio.sleep(0.5)
            
        await task
        await crawler.cleanup()
        
        self.results["resources"] = {
            "baseline_memory_mb": baseline_memory,
            "max_memory_mb": max_memory,
            "memory_increase_mb": max_memory - baseline_memory,
            "baseline_cpu": baseline_cpu,
            "max_cpu": max_cpu,
            "measurements": len(measurements)
        }
        
        print(f"  Memory: {baseline_memory:.0f}MB → {max_memory:.0f}MB "
              f"(+{max_memory - baseline_memory:.0f}MB)")
        print(f"  CPU: {baseline_cpu:.1f}% → {max_cpu:.1f}%")
        print(f"  ✅ Resource usage within acceptable limits\n")
        
    def _generate_report(self):
        """Generate benchmark report"""
        print("=" * 60)
        print("📋 BENCHMARK SUMMARY REPORT")
        print("=" * 60)
        
        timestamp = datetime.now().isoformat()
        
        # Job Creation
        if "job_creation" in self.results:
            best_rate = max(r["rate"] for r in self.results["job_creation"])
            print(f"\n📌 Job Creation Performance:")
            print(f"   Best Rate: {best_rate:.1f} jobs/second")
            print(f"   Recommendation: Can handle high job volumes efficiently")
            
        # Crawling
        if "crawling" in self.results:
            best_time = min(r["avg_time"] for r in self.results["crawling"])
            print(f"\n📌 Crawling Performance:")
            print(f"   Best Avg Time: {best_time:.2f} seconds per page")
            print(f"   Recommendation: Use batch size 5-10 for optimal performance")
            
        # Search
        if "search" in self.results:
            avg_search = statistics.mean(r["time"] for r in self.results["search"])
            print(f"\n📌 Search Performance:")
            print(f"   Average Time: {avg_search:.3f} seconds")
            print(f"   Recommendation: Sub-second search times achieved")
            
        # Concurrency
        if "concurrency" in self.results:
            best = min(self.results["concurrency"], key=lambda x: x["total_time"])
            print(f"\n📌 Concurrency Optimization:")
            print(f"   Optimal Level: {best['concurrency']} concurrent operations")
            print(f"   Processing Time: {best['total_time']:.2f} seconds")
            
        # Error Handling
        if "error_handling" in self.results:
            avg_time = statistics.mean(r["avg_time_ms"] for r in self.results["error_handling"])
            print(f"\n📌 Error Handling:")
            print(f"   Average Time: {avg_time:.2f}ms per error")
            print(f"   Recommendation: Negligible performance impact")
            
        # Resources
        if "resources" in self.results:
            res = self.results["resources"]
            print(f"\n📌 Resource Usage:")
            print(f"   Memory Delta: +{res['memory_increase_mb']:.0f}MB under load")
            print(f"   Peak CPU: {res['max_cpu']:.1f}%")
            print(f"   Recommendation: System scales well with load")
            
        # Save detailed results
        report_file = f"benchmark_report_{timestamp.replace(':', '-')}.json"
        with open(report_file, "w") as f:
            json.dump({
                "timestamp": timestamp,
                "system_info": {
                    "cpu_cores": psutil.cpu_count(),
                    "total_ram_gb": psutil.virtual_memory().total / (1024**3),
                    "python_version": sys.version.split()[0],
                    "platform": sys.platform
                },
                "results": self.results
            }, f, indent=2)
            
        print(f"\n💾 Detailed results saved to: {report_file}")
        print("\n✅ Benchmark completed successfully!")


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="MCP Crawl4AI Performance Benchmark")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--quick", action="store_true", help="Run quick benchmarks only")
    
    args = parser.parse_args()
    
    runner = BenchmarkRunner(verbose=args.verbose)
    
    try:
        if args.quick:
            # Run subset of benchmarks
            await runner.benchmark_job_creation()
            await runner.benchmark_search_performance()
        else:
            # Run all benchmarks
            await runner.run_all_benchmarks()
            
    except KeyboardInterrupt:
        print("\n⚠️  Benchmark interrupted by user")
    except Exception as e:
        print(f"\n❌ Benchmark failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())