# The transport for the MCP server - either 'sse' or 'stdio' (defaults to sse if left empty)
TRANSPORT=

# Host to bind to if using sse as the transport (leave empty if using stdio)
# Set this to 0.0.0.0 if using Docker, otherwise set to localhost (if using uv)
HOST=

# Port to listen on if using sse as the transport (leave empty if using stdio)
PORT=

# Embedding Configuration
# Enable direct GPU embedding (recommended for better performance)
# Set to "true" for 2-5x faster embedding generation, "false" for HTTP service
# Set to "false" when using shared BGE HTTP service for multiple applications
USE_DIRECT_EMBEDDING=false

# BGE model name for direct embedding (default: BAAI/bge-base-en-v1.5)
EMBEDDING_MODEL_NAME=BAAI/bge-base-en-v1.5

# Batch size for embedding generation (default: 32)
# Lower values use less GPU memory, higher values are more efficient
EMBEDDING_BATCH_SIZE=32

# Device for embedding generation: "auto", "cuda", or "cpu" (default: auto)
# "auto" will use GPU if available, otherwise CPU
EMBEDDING_DEVICE=auto

# BGE HTTP Service Configuration (primary when USE_DIRECT_EMBEDDING=false)
# URL of the BGE embedding service - change to your BGE server IP/URL
BGE_SERVICE_URL=http://localhost:8080

# Timeout for BGE service requests in seconds (default: 30)
BGE_TIMEOUT=30

# OpenRouter Configuration for LLM functionality
# Get your API key from https://openrouter.ai/keys
OPENROUTER_API_KEY=

# OpenRouter API base URL (default: https://openrouter.ai/api/v1)
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Model quality level: "free", "balanced", or "premium" (default: balanced)
# This affects default model selection when no specific models are configured
MODEL_QUALITY_LEVEL=balanced

# Specific model configurations for different LLM stages
# You can override these with any OpenRouter-supported model
# Code summarization model (for generating code example summaries)
CODE_SUMMARIZATION_MODEL=google/gemini-flash-1.5

# Source summarization model (for generating library/framework summaries)
SOURCE_SUMMARIZATION_MODEL=mistralai/mistral-7b-instruct:free

# Contextual embeddings model (for enhancing document chunks with context)
CONTEXTUAL_EMBEDDINGS_MODEL=google/gemini-flash-1.5

# RAG strategies - set these to "true" or "false" (default to "false")
# USE_CONTEXTUAL_EMBEDDINGS: Re-enabled with OpenRouter integration
USE_CONTEXTUAL_EMBEDDINGS=false

# USE_HYBRID_SEARCH: Combines vector similarity search with keyword search for better results
USE_HYBRID_SEARCH=false

# USE_AGENTIC_RAG: Enables code example extraction, storage, and specialized code search functionality
USE_AGENTIC_RAG=false

# USE_RERANKING: Applies cross-encoder reranking to improve search result relevance
USE_RERANKING=false

# USE_KNOWLEDGE_GRAPH: Enables AI hallucination detection and repository parsing tools using Neo4j
# If you set this to true, you must also set the Neo4j environment variables below.
USE_KNOWLEDGE_GRAPH=false

# For the Supabase version (sample_supabase_agent.py), set your Supabase URL and Service Key.
# Get your SUPABASE_URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# Neo4j Configuration for Knowledge Graph Tools
# These are required for the AI hallucination detection and repository parsing tools
# Leave empty to disable knowledge graph functionality

# Neo4j connection URI - use bolt://localhost:7687 for local, neo4j:// for cloud instances
# IMPORTANT: If running the MCP server through Docker, change localhost to host.docker.internal
NEO4J_URI=bolt://localhost:7687

# Neo4j username (usually 'neo4j' for default installations)
NEO4J_USER=neo4j

# Neo4j password for your database instance
NEO4J_PASSWORD=