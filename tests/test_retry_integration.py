#!/usr/bin/env python3
"""Integration tests for retry logic with real-world scenarios"""

import pytest
import asyncio
import aiohttp
from unittest.mock import Mock, patch
from crawl4ai import AsyncWebCrawler
from src.crawl4ai_mcp import (
    robust_crawl_with_retry,
    robust_multi_crawl_with_monitoring,
    ErrorRecoveryManager
)

# Run with: python tests/test_retry_integration.py

class TestRetryIntegration:
    """Integration tests for retry mechanisms."""
    
    @pytest.mark.asyncio
    async def test_rate_limit_handling(self):
        """Test handling of rate-limited responses."""
        # Create a mock server that returns 429 on first attempt
        mock_responses = [
            aiohttp.ClientResponse(
                method='GET',
                url='https://example.com',
                writer=Mock(),
                continue100=None,
                timer=Mock(),
                request_info=Mock(),
                traces=[],
                loop=asyncio.get_event_loop(),
                session=Mock()
            )
        ]
        
        # Mock the response status
        mock_responses[0].status = 429
        mock_responses[0].headers = {'Retry-After': '5'}
        
        # TODO: Complete mock setup for rate limit testing
        # This would require more complex mocking of aiohttp internals
        pass
    
    @pytest.mark.asyncio
    async def test_timeout_escalation(self):
        """Test that timeouts escalate with each retry."""
        crawler = Mock(spec=AsyncWebCrawler)
        
        # Track timeout values used
        timeout_values = []
        
        async def mock_arun(url, config):
            timeout_values.append(config.page_timeout)
            if len(timeout_values) < 3:
                raise asyncio.TimeoutError()
            else:
                # Success on third attempt
                result = Mock()
                result.success = True
                result.markdown = "Success after retries"
                return result
        
        crawler.arun = mock_arun
        
        with patch('asyncio.sleep'):  # Speed up test
            result = await robust_crawl_with_retry(
                crawler,
                "https://example.com",
                max_retries=3,
                base_timeout=15000
            )
        
        assert result.success
        # Check timeout escalation: 15s, 22.5s, 30s, 37.5s
        assert timeout_values[0] == 15000
        assert timeout_values[1] == 22500
        assert timeout_values[2] == 30000
    
    @pytest.mark.asyncio
    async def test_error_recovery_flow(self):
        """Test the complete error recovery flow."""
        # Create mock components
        supabase_client = Mock()
        error_recovery = ErrorRecoveryManager(supabase_client)
        crawler = Mock(spec=AsyncWebCrawler)
        
        # First crawl fails
        failed_result = Mock()
        failed_result.success = False
        failed_result.error_message = "Timeout error"
        failed_result.status_code = 503
        
        # Retry succeeds
        success_result = Mock()
        success_result.success = True
        success_result.markdown = "Content retrieved on retry"
        
        crawler.arun.side_effect = [failed_result, success_result]
        
        # Schedule a retry
        error_recovery.schedule_retry("https://example.com", "Initial timeout", 0)
        
        # Process retries
        with patch('asyncio.sleep'):
            results = await error_recovery.process_ready_retries(crawler)
        
        assert results['processed'] == 1
        assert results['successful'] == 1
        assert results['failed'] == 0
    
    @pytest.mark.asyncio
    async def test_multi_url_retry_handling(self):
        """Test retry handling for multiple URLs."""
        crawler = Mock(spec=AsyncWebCrawler)
        
        # Create mixed results
        async def mock_arun_many(urls, config, dispatcher):
            for i, url in enumerate(urls):
                result = Mock()
                if i % 2 == 0:
                    # Even indices succeed
                    result.success = True
                    result.markdown = f"Content for {url}"
                    result.url = url
                else:
                    # Odd indices fail
                    result.success = False
                    result.error_message = "Server error"
                    result.status_code = 503
                    result.url = url
                yield result
        
        crawler.arun_many.return_value = mock_arun_many(
            ["https://example.com/1", "https://example.com/2", "https://example.com/3"],
            None, None
        )
        
        results = await robust_multi_crawl_with_monitoring(
            crawler,
            ["https://example.com/1", "https://example.com/2", "https://example.com/3"],
            max_concurrent=3
        )
        
        assert results['stats']['successful'] == 2
        assert results['stats']['failed'] == 1
        assert len(results['successful_results']) == 2
        assert len(results['failed_urls']) == 1
    
    @pytest.mark.asyncio
    async def test_silent_block_recovery(self):
        """Test recovery from silent blocks."""
        crawler = Mock(spec=AsyncWebCrawler)
        
        # Simulate silent block pattern
        empty_result = Mock()
        empty_result.success = True
        empty_result.markdown = ""  # Empty content
        empty_result.status_code = 200
        
        success_result = Mock()
        success_result.success = True
        success_result.markdown = "Finally got content"
        
        # Three empty responses, then success
        crawler.arun.side_effect = [
            empty_result,
            empty_result,
            empty_result,
            success_result
        ]
        
        # This should detect silent block and adjust strategy
        # In real implementation, would need to integrate SilentBlockDetector
        # For now, just verify retry attempts
        attempts = 0
        
        async def counting_crawl(*args, **kwargs):
            nonlocal attempts
            attempts += 1
            if attempts < 4:
                return empty_result
            return success_result
        
        crawler.arun = counting_crawl
        
        with patch('asyncio.sleep'):
            result = await robust_crawl_with_retry(
                crawler,
                "https://example.com",
                max_retries=4
            )
        
        assert result.success
        assert result.markdown == "Finally got content"
        assert attempts == 4

class TestErrorClassification:
    """Test error classification in real scenarios."""
    
    def test_cloudflare_error_codes(self):
        """Test that Cloudflare-specific error codes are handled."""
        from src.crawl4ai_mcp import ErrorClassifier
        classifier = ErrorClassifier()
        
        # Cloudflare error codes should be retryable
        cloudflare_codes = [520, 521, 522, 524]
        
        for code in cloudflare_codes:
            error = Mock()
            error.status_code = code
            assert classifier.is_retryable_error(error), f"Cloudflare error {code} should be retryable"
    
    def test_dns_error_classification(self):
        """Test DNS error classification."""
        from src.crawl4ai_mcp import ErrorClassifier
        classifier = ErrorClassifier()
        
        # Create DNS error
        dns_error = OSError("DNS resolution failed")
        dns_error.errno = 11001  # Windows DNS error code
        
        assert classifier.is_retryable_error(dns_error)
    
    def test_mixed_error_scenarios(self):
        """Test classification of various error combinations."""
        from src.crawl4ai_mcp import ErrorClassifier
        classifier = ErrorClassifier()
        
        # SSL certificate error - not retryable
        ssl_cert_error = Mock()
        ssl_cert_error.__class__.__name__ = 'SSLError'
        ssl_cert_error.__str__ = lambda self: "certificate verify failed"
        
        # Connection reset - retryable
        conn_reset = ConnectionError("Connection reset by peer")
        
        # Server overloaded - retryable
        server_overload = Mock()
        server_overload.status_code = 503
        
        assert not classifier.is_retryable_error(ssl_cert_error)
        assert classifier.is_retryable_error(conn_reset)
        assert classifier.is_retryable_error(server_overload)

class TestPerformanceOptimization:
    """Test performance optimizations in retry logic."""
    
    @pytest.mark.asyncio
    async def test_connection_reuse(self):
        """Test that connections are reused across retries."""
        from src.crawl4ai_mcp import ConnectionManager
        
        conn_manager = ConnectionManager()
        
        # Get session multiple times
        session1 = await conn_manager.get_session()
        session2 = await conn_manager.get_session()
        session3 = await conn_manager.get_session()
        
        # Should be the same session instance
        assert session1 is session2 is session3
        
        # Check stats
        stats = conn_manager.get_connection_stats()
        assert stats['new_connections'] == 1
        assert stats['reused_connections'] == 2
        
        # Cleanup
        await conn_manager.close_session()
    
    @pytest.mark.asyncio
    async def test_timeout_optimization(self):
        """Test timeout optimization based on response patterns."""
        crawler = Mock(spec=AsyncWebCrawler)
        
        # Track timeouts used
        timeout_configs = []
        
        async def mock_arun(url, config):
            timeout_configs.append(config.page_timeout)
            result = Mock()
            result.success = True
            result.markdown = "Content"
            return result
        
        crawler.arun = mock_arun
        
        # Test with different base timeouts
        await robust_crawl_with_retry(crawler, "https://fast-site.com", base_timeout=5000)
        await robust_crawl_with_retry(crawler, "https://slow-site.com", base_timeout=30000)
        
        assert timeout_configs[0] == 5000  # Fast site uses lower timeout
        assert timeout_configs[1] == 30000  # Slow site uses higher timeout

if __name__ == "__main__":
    # Run specific test
    import sys
    if len(sys.argv) > 1:
        pytest.main([__file__, "-v", "-k", sys.argv[1]])
    else:
        pytest.main([__file__, "-v"])