#!/usr/bin/env python3
"""
Performance test script for direct embedding integration.
Compares HTTP service vs direct GPU embedding performance.
"""

import os
import time
import statistics
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our utilities
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
from utils import create_embeddings_batch, get_embedding_manager

def generate_test_texts(count: int = 50) -> List[str]:
    """Generate test texts of varying lengths for benchmarking"""
    test_texts = [
        # Short texts (typical query length)
        "How to implement authentication in React?",
        "Python async await tutorial",
        "Database connection pooling best practices",
        
        # Medium texts (typical documentation chunks)
        """
        The React useEffect hook allows you to perform side effects in function components.
        It serves the same purpose as componentDidMount, componentDidUpdate, and
        componentWillUnmount combined in React class components. When you call useEffect,
        you're telling <PERSON>act to run your "effect" function after flushing changes to the DOM.
        """,
        
        """
        FastAPI is a modern, fast (high-performance), web framework for building APIs with Python 3.7+
        based on standard Python type hints. The key features are: Fast to code, fewer bugs,
        intuitive, easy, short, robust, and standards-based. FastAPI is based on OpenAPI and JSON Schema.
        """,
        
        # Long texts (large documentation sections)
        """
        Machine learning model deployment is a critical step in the machine learning lifecycle.
        After training and validating a model, the next step is to deploy it to a production environment
        where it can serve predictions to real users or applications. This process involves several
        considerations including scalability, latency, security, monitoring, and maintenance.
        
        There are various deployment strategies available, each with its own trade-offs.
        Batch prediction involves running the model on large datasets at scheduled intervals,
        which is suitable for scenarios where real-time predictions are not required.
        Online prediction, on the other hand, involves serving predictions in real-time as requests
        come in, which requires more sophisticated infrastructure but provides immediate results.
        
        Container-based deployment using Docker and Kubernetes has become increasingly popular
        due to its portability and scalability benefits. Serverless deployment options like
        AWS Lambda or Google Cloud Functions offer cost-effective solutions for workloads
        with variable traffic patterns.
        """,
    ]
    
    # Repeat and pad to reach desired count
    expanded_texts = []
    while len(expanded_texts) < count:
        expanded_texts.extend(test_texts)
    
    return expanded_texts[:count]

def test_direct_embedding(texts: List[str]) -> Dict[str, Any]:
    """Test direct embedding performance"""
    print("Testing direct GPU embedding...")
    
    # Force direct embedding mode
    os.environ['USE_DIRECT_EMBEDDING'] = 'true'
    
    start_time = time.time()
    try:
        embeddings = create_embeddings_batch(texts)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Get metrics from embedding manager
        embedding_manager = get_embedding_manager()
        metrics = embedding_manager.get_metrics() if embedding_manager.is_available() else {}
        
        return {
            'method': 'direct_gpu',
            'success': True,
            'processing_time': processing_time,
            'embeddings_count': len(embeddings),
            'avg_time_per_embedding': processing_time / len(embeddings) if embeddings else 0,
            'throughput': len(embeddings) / processing_time if processing_time > 0 else 0,
            'metrics': metrics
        }
    except Exception as e:
        end_time = time.time()
        return {
            'method': 'direct_gpu',
            'success': False,
            'error': str(e),
            'processing_time': end_time - start_time,
            'embeddings_count': 0,
            'avg_time_per_embedding': 0,
            'throughput': 0,
            'metrics': {}
        }

def test_http_embedding(texts: List[str]) -> Dict[str, Any]:
    """Test HTTP service embedding performance"""
    print("Testing HTTP service embedding...")
    
    # Force HTTP service mode
    os.environ['USE_DIRECT_EMBEDDING'] = 'false'
    
    start_time = time.time()
    try:
        from src.utils import _create_embeddings_batch_http
        embeddings = _create_embeddings_batch_http(texts)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        return {
            'method': 'http_service',
            'success': True,
            'processing_time': processing_time,
            'embeddings_count': len(embeddings),
            'avg_time_per_embedding': processing_time / len(embeddings) if embeddings else 0,
            'throughput': len(embeddings) / processing_time if processing_time > 0 else 0,
            'metrics': {}
        }
    except Exception as e:
        end_time = time.time()
        return {
            'method': 'http_service',
            'success': False,
            'error': str(e),
            'processing_time': end_time - start_time,
            'embeddings_count': 0,
            'avg_time_per_embedding': 0,
            'throughput': 0,
            'metrics': {}
        }

def run_benchmark(text_counts: List[int] = [10, 25, 50, 100], runs_per_test: int = 3):
    """Run comprehensive benchmark comparing both methods"""
    print("=" * 60)
    print("Direct GPU Embedding vs HTTP Service Benchmark")
    print("=" * 60)
    
    results = []
    
    for count in text_counts:
        print(f"\n--- Testing with {count} texts ---")
        test_texts = generate_test_texts(count)
        
        # Test direct embedding multiple times
        direct_times = []
        direct_success_count = 0
        
        for run in range(runs_per_test):
            print(f"Direct GPU run {run + 1}/{runs_per_test}...")
            result = test_direct_embedding(test_texts)
            if result['success']:
                direct_times.append(result['processing_time'])
                direct_success_count += 1
                if run == 0:  # Store metrics from first successful run
                    direct_metrics = result['metrics']
        
        # Test HTTP service multiple times
        http_times = []
        http_success_count = 0
        
        for run in range(runs_per_test):
            print(f"HTTP service run {run + 1}/{runs_per_test}...")
            result = test_http_embedding(test_texts)
            if result['success']:
                http_times.append(result['processing_time'])
                http_success_count += 1
        
        # Calculate statistics
        direct_avg = statistics.mean(direct_times) if direct_times else 0
        direct_std = statistics.stdev(direct_times) if len(direct_times) > 1 else 0
        
        http_avg = statistics.mean(http_times) if http_times else 0
        http_std = statistics.stdev(http_times) if len(http_times) > 1 else 0
        
        speedup = http_avg / direct_avg if direct_avg > 0 and http_avg > 0 else 0
        
        result_summary = {
            'text_count': count,
            'direct_gpu': {
                'success_rate': direct_success_count / runs_per_test,
                'avg_time': direct_avg,
                'std_time': direct_std,
                'throughput': count / direct_avg if direct_avg > 0 else 0,
                'metrics': direct_metrics if direct_success_count > 0 else {}
            },
            'http_service': {
                'success_rate': http_success_count / runs_per_test,
                'avg_time': http_avg,
                'std_time': http_std,
                'throughput': count / http_avg if http_avg > 0 else 0
            },
            'speedup': speedup
        }
        
        results.append(result_summary)
        
        # Print summary for this test
        print(f"\nResults for {count} texts:")
        if direct_success_count > 0:
            print(f"  Direct GPU:    {direct_avg:.3f}s ± {direct_std:.3f}s ({direct_success_count}/{runs_per_test} successful)")
        else:
            print(f"  Direct GPU:    FAILED ({direct_success_count}/{runs_per_test} successful)")
            
        if http_success_count > 0:
            print(f"  HTTP Service:  {http_avg:.3f}s ± {http_std:.3f}s ({http_success_count}/{runs_per_test} successful)")
        else:
            print(f"  HTTP Service:  FAILED ({http_success_count}/{runs_per_test} successful)")
            
        if speedup > 0:
            print(f"  Speedup:       {speedup:.1f}x faster with direct GPU")
    
    # Final summary
    print("\n" + "=" * 60)
    print("BENCHMARK SUMMARY")
    print("=" * 60)
    
    for result in results:
        count = result['text_count']
        speedup = result['speedup']
        direct_gpu = result['direct_gpu']
        http_service = result['http_service']
        
        print(f"\n{count} texts:")
        if direct_gpu['success_rate'] > 0:
            print(f"  Direct GPU:    {direct_gpu['avg_time']:.3f}s ({direct_gpu['throughput']:.1f} embeddings/sec)")
        if http_service['success_rate'] > 0:
            print(f"  HTTP Service:  {http_service['avg_time']:.3f}s ({http_service['throughput']:.1f} embeddings/sec)")
        if speedup > 0:
            print(f"  Performance:   {speedup:.1f}x speedup with direct GPU")
    
    # Print system information
    print("\n" + "=" * 60)
    print("SYSTEM INFORMATION")
    print("=" * 60)
    
    if results and results[0]['direct_gpu']['metrics']:
        metrics = results[0]['direct_gpu']['metrics']
        print(f"Direct embedding available: {metrics.get('direct_embedding_available', 'Unknown')}")
        print(f"Device: {metrics.get('device', 'Unknown')}")
        print(f"Model: {metrics.get('model_name', 'Unknown')}")
        print(f"Batch size: {metrics.get('batch_size', 'Unknown')}")
        
        if metrics.get('gpu_name'):
            print(f"GPU: {metrics['gpu_name']}")
            print(f"GPU Memory: {metrics.get('gpu_memory_total_mb', 0):.0f}MB total")
    
    return results

if __name__ == "__main__":
    import sys
    
    # Check if specific test is requested
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            print("Running quick test...")
            run_benchmark([10, 25], runs_per_test=2)
        elif sys.argv[1] == "direct":
            print("Testing direct GPU embedding only...")
            texts = generate_test_texts(25)
            result = test_direct_embedding(texts)
            print(f"Result: {result}")
        elif sys.argv[1] == "http":
            print("Testing HTTP service only...")
            texts = generate_test_texts(25)
            result = test_http_embedding(texts)
            print(f"Result: {result}")
        else:
            print("Usage: python test_direct_embedding.py [quick|direct|http]")
            sys.exit(1)
    else:
        # Run full benchmark
        run_benchmark()