#!/usr/bin/env python3
"""
Test script for BGE embedding service integration
"""

import os
import sys
import time
import requests
from dotenv import load_dotenv

# Add src to path for importing utils
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Load environment variables
load_dotenv()

def test_bge_service_health():
    """Test BGE service health endpoint"""
    print("🔍 Testing BGE service health...")
    
    bge_url = os.getenv("BGE_SERVICE_URL", "http://localhost:8080")
    
    try:
        response = requests.get(f"{bge_url}/health", timeout=10)
        response.raise_for_status()
        
        health_data = response.json()
        print(f"✅ BGE service is healthy!")
        print(f"   Model: {health_data.get('model', 'unknown')}")
        print(f"   Dimensions: {health_data.get('dimensions', 'unknown')}")
        print(f"   CUDA Available: {health_data.get('cuda_available', 'unknown')}")
        print(f"   GPU Memory Used: {health_data.get('gpu_memory_used_mb', 0):.1f}MB")
        
        return True
    except Exception as e:
        print(f"❌ BGE service health check failed: {e}")
        print(f"   Make sure the BGE service is running at {bge_url}")
        print(f"   Start it with: cd services/bge-embedding-server && docker-compose up")
        return False

def test_bge_embedding_generation():
    """Test BGE embedding generation via utils.py"""
    print("\n🔍 Testing BGE embedding generation...")
    
    try:
        from utils import create_embedding, create_embeddings_batch
        
        # Test single embedding
        print("   Testing single embedding...")
        start_time = time.time()
        embedding = create_embedding("This is a test sentence for BGE embedding generation.")
        duration = time.time() - start_time
        
        print(f"   ✅ Single embedding created in {duration:.3f}s")
        print(f"      Dimensions: {len(embedding)}")
        print(f"      First 5 values: {embedding[:5]}")
        
        # Test batch embedding
        print("   Testing batch embeddings...")
        test_texts = [
            "This is the first test sentence.",
            "Here is another sentence to embed.",
            "Finally, a third sentence for batch processing.",
            "BGE embeddings are working correctly.",
            "Self-hosted embedding service is operational."
        ]
        
        start_time = time.time()
        embeddings = create_embeddings_batch(test_texts)
        duration = time.time() - start_time
        
        print(f"   ✅ Batch embeddings created in {duration:.3f}s")
        print(f"      Count: {len(embeddings)}")
        print(f"      Dimensions per embedding: {len(embeddings[0])}")
        print(f"      Processing rate: {len(embeddings)/duration:.1f} embeddings/second")
        
        # Verify dimensions are 768 (BGE standard)
        if len(embedding) == 768 and len(embeddings[0]) == 768:
            print("   ✅ Correct embedding dimensions (768)")
        else:
            print(f"   ⚠️  Unexpected dimensions: {len(embedding)}, expected 768")
        
        return True
    except Exception as e:
        print(f"   ❌ Embedding generation failed: {e}")
        return False

def test_bge_service_direct():
    """Test BGE service directly via HTTP"""
    print("\n🔍 Testing BGE service HTTP API...")
    
    bge_url = os.getenv("BGE_SERVICE_URL", "http://localhost:8080")
    
    try:
        # Test single embedding endpoint
        response = requests.post(
            f"{bge_url}/embed/single",
            json={"text": "Direct HTTP API test"},
            timeout=10
        )
        response.raise_for_status()
        
        data = response.json()
        embedding = data.get('embedding', [])
        
        print(f"   ✅ Direct HTTP API test successful")
        print(f"      Model: {data.get('model', 'unknown')}")
        print(f"      Dimensions: {data.get('dimensions', 'unknown')}")
        print(f"      Processing time: {data.get('processing_time_seconds', 0):.3f}s")
        
        return True
    except Exception as e:
        print(f"   ❌ Direct HTTP API test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting BGE Integration Tests")
    print("=" * 50)
    
    # Test 1: BGE service health
    if not test_bge_service_health():
        print("\n❌ Cannot proceed with tests - BGE service is not running")
        return False
    
    # Test 2: Direct HTTP API
    if not test_bge_service_direct():
        print("\n❌ Direct API test failed")
        return False
    
    # Test 3: Integration via utils.py
    if not test_bge_embedding_generation():
        print("\n❌ Integration test failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All BGE integration tests passed!")
    print("\nNext steps:")
    print("1. Update your Supabase database schema: run crawled_pages.sql")
    print("2. Set BGE_SERVICE_URL in your .env file")
    print("3. Start the MCP server: uv run src/crawl4ai_mcp.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)