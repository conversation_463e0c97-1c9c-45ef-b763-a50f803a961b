"""
Test suite for Wave 4: Production Hardening components
"""

import asyncio
import pytest
import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).resolve().parent.parent / "src"
sys.path.insert(0, str(src_path))

from error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Error<PERSON>ontext, Error<PERSON>ategory, ErrorSeverity
from config_manager import ConfigManager, Config
from operational_tools import HealthChecker, PerformanceProfiler, DiagnosticTool
from datetime import datetime


class TestErrorHandler:
    """Test error handling and classification"""
    
    @pytest.fixture
    def classifier(self):
        return ErrorClassifier()
        
    @pytest.fixture
    def handler(self):
        return ErrorHandler()
        
    def test_error_classification_network(self, classifier):
        """Test network error classification"""
        context = ErrorContext(
            error_type=ConnectionError,
            error_message="Connection timeout",
            stack_trace="Traceback...",
            timestamp=datetime.utcnow(),
            url="https://example.com"
        )
        
        classification = classifier.classify(context)
        
        assert classification.category == ErrorCategory.NETWORK
        assert classification.severity == ErrorSeverity.MEDIUM
        assert classification.is_retryable == True
        assert classification.retry_delay is not None
        
    def test_error_classification_rate_limit(self, classifier):
        """Test rate limit error classification"""
        context = ErrorContext(
            error_type=Exception,
            error_message="429 Too Many Requests",
            stack_trace="",
            timestamp=datetime.utcnow()
        )
        
        classification = classifier.classify(context)
        
        assert classification.category == ErrorCategory.EXTERNAL_SERVICE
        assert classification.is_retryable == True
        assert classification.retry_delay > 0
        
    def test_error_classification_storage(self, classifier):
        """Test storage error classification"""
        context = ErrorContext(
            error_type=Exception,
            error_message="Database connection error",
            stack_trace="",
            timestamp=datetime.utcnow()
        )
        
        classification = classifier.classify(context)
        
        assert classification.category == ErrorCategory.STORAGE
        assert classification.severity == ErrorSeverity.CRITICAL
        
    @pytest.mark.asyncio
    async def test_error_handler_recovery(self, handler):
        """Test error handler recovery strategies"""
        error = ConnectionError("Network timeout")
        context_data = {
            "url": "https://example.com",
            "job_id": "test-job-123"
        }
        
        result = await handler.handle_error(error, context_data)
        
        assert result["handled"] == True
        assert "classification" in result
        assert "recovery_result" in result
        
    def test_error_pattern_detection(self, handler):
        """Test error pattern detection"""
        # Add multiple similar errors
        for i in range(5):
            context = ErrorContext(
                error_type=ConnectionError,
                error_message="Connection refused",
                stack_trace="",
                timestamp=datetime.utcnow()
            )
            handler._add_to_history(context)
            
        patterns = handler._detect_error_patterns()
        
        assert len(patterns) > 0
        assert any(p["type"] == "repeated_error" for p in patterns)


class TestConfigManager:
    """Test configuration management"""
    
    @pytest.fixture
    def config_manager(self, tmp_path):
        # Create a test config file
        config_file = tmp_path / "test_config.yaml"
        config_file.write_text("""
crawler:
  max_concurrent: 5
  max_depth: 2

monitoring:
  log_level: "DEBUG"
""")
        return ConfigManager(str(config_file))
        
    def test_config_loading(self, config_manager):
        """Test configuration loading from file"""
        assert config_manager.get("crawler.max_concurrent") == 5
        assert config_manager.get("crawler.max_depth") == 2
        assert config_manager.get("monitoring.log_level") == "DEBUG"
        
    def test_config_environment_override(self, monkeypatch):
        """Test environment variable override"""
        monkeypatch.setenv("CRAWL4AI_MAX_CONCURRENT", "20")
        monkeypatch.setenv("CRAWL4AI_LOG_LEVEL", "ERROR")
        
        config = ConfigManager()
        
        assert config.get("crawler.max_concurrent") == 20
        assert config.get("monitoring.log_level") == "ERROR"
        
    def test_config_validation_required(self):
        """Test required configuration validation"""
        # This should fail without required Supabase settings
        with pytest.raises(ValueError) as exc_info:
            ConfigManager()
            
        assert "SUPABASE_URL is required" in str(exc_info.value)
        
    def test_config_update(self, config_manager):
        """Test configuration updates"""
        config_manager.set("crawler.max_concurrent", 15)
        assert config_manager.get("crawler.max_concurrent") == 15
        
        config_manager.update({
            "crawler": {
                "max_depth": 5,
                "chunk_size": 10000
            }
        })
        
        assert config_manager.get("crawler.max_depth") == 5
        assert config_manager.get("crawler.chunk_size") == 10000
        
    def test_config_runtime_info(self, config_manager):
        """Test runtime configuration info"""
        info = config_manager.get_runtime_info()
        
        assert "environment" in info
        assert "features_enabled" in info
        assert "crawler_limits" in info


class TestOperationalTools:
    """Test operational tools"""
    
    @pytest.fixture
    def health_checker(self):
        return HealthChecker()
        
    @pytest.fixture
    def profiler(self):
        return PerformanceProfiler()
        
    @pytest.fixture
    def diagnostic_tool(self):
        return DiagnosticTool()
        
    @pytest.mark.asyncio
    async def test_health_check(self, health_checker):
        """Test health checking system"""
        health = await health_checker.check_health()
        
        assert health.status in ["healthy", "degraded", "unhealthy"]
        assert health.cpu_percent >= 0
        assert health.memory_percent >= 0
        assert health.disk_percent >= 0
        assert health.checks_passed >= 0
        assert health.checks_failed >= 0
        
    @pytest.mark.asyncio
    async def test_system_resource_check(self, health_checker):
        """Test system resource checking"""
        result = await health_checker._check_system_resources()
        
        assert "healthy" in result
        assert "cpu_percent" in result
        assert "memory_percent" in result
        assert "disk_percent" in result
        
    def test_performance_profiler(self, profiler):
        """Test performance profiling"""
        profiler.start_profile("test_operation")
        
        # Simulate some work
        import time
        time.sleep(0.1)
        
        result = profiler.end_profile("test_operation")
        
        assert result["name"] == "test_operation"
        assert result["duration_seconds"] >= 0.1
        assert "memory_delta_mb" in result
        
    @pytest.mark.asyncio
    async def test_async_profiling(self, profiler):
        """Test async function profiling"""
        async def test_function():
            await asyncio.sleep(0.1)
            return "result"
            
        result = await profiler.profile_async("async_test", test_function)
        
        assert result == "result"
        stats = profiler.get_profile_stats("async_test")
        assert stats["count"] == 1
        assert stats["avg_duration"] >= 0.1
        
    @pytest.mark.asyncio
    async def test_diagnostics(self, diagnostic_tool):
        """Test diagnostic tool"""
        diagnostics = await diagnostic_tool.run_diagnostics()
        
        assert "timestamp" in diagnostics
        assert "system" in diagnostics
        assert "python" in diagnostics
        assert "dependencies" in diagnostics
        assert "configuration" in diagnostics
        assert "network" in diagnostics
        assert "storage" in diagnostics
        
        # Check system info
        assert "platform" in diagnostics["system"]
        assert "cpu_count" in diagnostics["system"]
        assert "total_memory_gb" in diagnostics["system"]
        
        # Check Python info
        assert "version" in diagnostics["python"]
        assert "executable" in diagnostics["python"]
        
        # Check dependencies
        assert "crawl4ai" in diagnostics["dependencies"]
        assert "supabase" in diagnostics["dependencies"]


class TestGracefulDegradation:
    """Test graceful degradation"""
    
    @pytest.fixture
    def degradation(self):
        from error_handler import GracefulDegradation
        return GracefulDegradation()
        
    def test_degradation_levels(self, degradation):
        """Test degradation level transitions"""
        # Start at full
        config = degradation.get_current_config()
        assert config["level"] == "full"
        assert config["max_concurrent"] == 10
        
        # Degrade to reduced
        config = degradation.degrade()
        assert config["level"] == "reduced"
        assert config["max_concurrent"] == 5
        
        # Degrade to minimal
        config = degradation.degrade()
        assert config["level"] == "minimal"
        assert config["max_concurrent"] == 2
        
        # Degrade to emergency
        config = degradation.degrade()
        assert config["level"] == "emergency"
        assert config["max_concurrent"] == 1
        
        # Can't degrade further
        config = degradation.degrade()
        assert config["level"] == "emergency"
        
    def test_degradation_restore(self, degradation):
        """Test degradation restoration"""
        # Degrade twice
        degradation.degrade()
        degradation.degrade()
        
        assert degradation.current_level == "minimal"
        
        # Restore once
        config = degradation.restore()
        assert config["level"] == "reduced"
        
        # Restore again
        config = degradation.restore()
        assert config["level"] == "full"
        
        # Can't restore further
        config = degradation.restore()
        assert config["level"] == "full"
        
    def test_feature_checking(self, degradation):
        """Test feature availability checking"""
        # At full level
        assert degradation.is_feature_enabled("sitemap") == True
        assert degradation.is_feature_enabled("recursive") == True
        assert degradation.is_feature_enabled("knowledge_graph") == True
        
        # Degrade to minimal
        degradation.degrade()
        degradation.degrade()
        
        assert degradation.is_feature_enabled("sitemap") == False
        assert degradation.is_feature_enabled("recursive") == False
        assert degradation.is_feature_enabled("single_page") == True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])