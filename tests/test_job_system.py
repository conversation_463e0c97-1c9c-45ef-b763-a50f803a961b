"""
Test script for the enhanced job management system.
Tests job creation, processing, monitoring, and completion.
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path
src_path = Path(__file__).resolve().parent.parent / "src"
sys.path.insert(0, str(src_path))

from job_manager import JobManager, JobType
from utils import get_supabase_client


async def test_job_lifecycle():
    """Test the complete job lifecycle."""
    print("🚀 Testing Enhanced Job Management System")
    
    # Initialize
    job_manager = JobManager()
    supabase = get_supabase_client()
    
    # Test 1: Create a single page job
    print("\n📝 Test 1: Creating single page crawl job...")
    job_id = await job_manager.create_job(
        job_type=JobType.SINGLE_PAGE,
        parameters={
            "url": "https://example.com",
            "chunk_size": 1000
        },
        priority=8
    )
    print(f"✅ Created job: {job_id}")
    
    # Test 2: Check job status
    print("\n🔍 Test 2: Checking job status...")
    status = await job_manager.get_job_status(job_id)
    print(f"Status: {status['status']}")
    print(f"Progress: {status['progress_percent']}%")
    print(f"Current Operation: {status['current_operation']}")
    
    # Test 3: Update job progress
    print("\n📊 Test 3: Simulating job progress...")
    await job_manager.mark_job_running(job_id)
    
    for i in range(0, 101, 20):
        await job_manager.update_job_progress(
            job_id,
            f"Processing step {i//20 + 1}",
            i,
            f"Completed {i}% of the task"
        )
        await asyncio.sleep(1)
    
    # Test 4: Complete the job
    print("\n✅ Test 4: Completing the job...")
    await job_manager.complete_job(
        job_id,
        result_summary={
            "url": "https://example.com",
            "chunks_created": 5,
            "processing_time": 10.5
        },
        pages_crawled=1,
        chunks_created=5
    )
    
    # Test 5: Check final status
    print("\n📈 Test 5: Checking final status...")
    final_status = await job_manager.get_job_status(job_id)
    print(f"Final Status: {final_status['status']}")
    print(f"Pages Crawled: {final_status['pages_crawled']}")
    print(f"Chunks Created: {final_status['chunks_created']}")
    
    # Test 6: Check queue metrics
    print("\n📊 Test 6: Checking queue metrics...")
    result = supabase.rpc("pgmq_metrics", {"queue_name": "crawl_jobs_queue"}).execute()
    if result.data:
        print(f"Queue metrics: {json.dumps(result.data[0], indent=2)}")
    
    # Test 7: Get job monitoring dashboard
    print("\n📊 Test 7: Getting monitoring dashboard...")
    dashboard = supabase.from_("job_monitoring_dashboard").select("*").execute()
    if dashboard.data and len(dashboard.data) > 0:
        stats = dashboard.data[0]
        print(f"Queued Jobs: {stats['queued_count']}")
        print(f"Running Jobs: {stats['running_count']}")
        print(f"Completed Jobs: {stats['completed_count']}")
        print(f"Failed Jobs: {stats['failed_count']}")
        print(f"Success Rate: {stats['success_rate_percent']:.1f}%")
        print(f"Avg Completion Time: {stats['avg_completion_minutes']:.1f} minutes")
    
    print("\n✨ All tests completed successfully!")


async def test_parallel_job_creation():
    """Test creating multiple jobs in parallel."""
    print("\n🚀 Test 8: Creating multiple jobs in parallel...")
    
    job_manager = JobManager()
    
    # Create 5 jobs simultaneously
    job_tasks = []
    urls = [
        "https://docs.python.org",
        "https://reactjs.org",
        "https://nodejs.org",
        "https://vuejs.org",
        "https://angular.io"
    ]
    
    for url in urls:
        task = job_manager.create_job(
            job_type=JobType.SMART_CRAWL,
            parameters={
                "url": url,
                "max_depth": 2,
                "max_concurrent": 5,
                "chunk_size": 2000
            },
            priority=5
        )
        job_tasks.append(task)
    
    job_ids = await asyncio.gather(*job_tasks)
    print(f"✅ Created {len(job_ids)} jobs:")
    for i, job_id in enumerate(job_ids):
        print(f"   - Job {i+1}: {job_id} for {urls[i]}")
    
    # List all jobs
    print("\n📋 Listing recent jobs...")
    jobs = await job_manager.list_jobs(limit=10)
    for job in jobs[:5]:
        print(f"   - {job['id']}: {job['job_type']} - {job['status']}")


async def test_error_handling():
    """Test error handling and job failure."""
    print("\n🚀 Test 9: Testing error handling...")
    
    job_manager = JobManager()
    
    # Create a job that will fail
    job_id = await job_manager.create_job(
        job_type=JobType.SINGLE_PAGE,
        parameters={
            "url": "https://this-domain-definitely-does-not-exist-12345.com",
            "chunk_size": 1000
        }
    )
    
    # Simulate processing and failure
    await job_manager.mark_job_running(job_id)
    await asyncio.sleep(1)
    
    await job_manager.fail_job(
        job_id,
        "Failed to resolve domain: DNS lookup failed"
    )
    
    # Check failed job status
    status = await job_manager.get_job_status(job_id)
    print(f"❌ Job Status: {status['status']}")
    print(f"Error Message: {status['error_message']}")


async def main():
    """Run all tests."""
    try:
        # Run basic tests
        await test_job_lifecycle()
        
        # Run parallel tests
        await test_parallel_job_creation()
        
        # Run error handling tests
        await test_error_handling()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)