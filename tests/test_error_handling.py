#!/usr/bin/env python3
"""Test enhanced error handling and retry logic"""

import pytest
import asyncio
import ssl
from unittest.mock import Mock, patch
from crawl4ai import AsyncWebCrawler
from src.crawl4ai_mcp import (
    robust_crawl_with_retry, 
    ErrorClassifier, 
    SilentBlockDetector,
    ConnectionManager,
    ErrorRecoveryManager
)
from crawl4ai.async_crawler_strategy import ConnectionTimeoutError, HTTPStatusError

# Run with: python -m pytest tests/test_error_handling.py -v

@pytest.fixture
def mock_crawler():
    """Create a mock AsyncWebCrawler instance."""
    return Mock(spec=AsyncWebCrawler)

@pytest.fixture
def error_classifier():
    """Create an ErrorClassifier instance."""
    return ErrorClassifier()

@pytest.fixture
def silent_block_detector():
    """Create a SilentBlockDetector instance."""
    return SilentBlockDetector()

@pytest.fixture
def connection_manager():
    """Create a ConnectionManager instance."""
    return ConnectionManager()

@pytest.fixture
def mock_supabase_client():
    """Create a mock Supabase client."""
    return Mock()

@pytest.fixture
def error_recovery_manager(mock_supabase_client):
    """Create an ErrorRecoveryManager instance."""
    return ErrorRecoveryManager(mock_supabase_client)

class TestErrorClassifier:
    """Test the ErrorClassifier functionality."""
    
    def test_retryable_network_errors(self, error_classifier):
        """Test that network errors are classified as retryable."""
        # Test ConnectionError
        assert error_classifier.is_retryable_error(ConnectionError("Connection failed"))
        
        # Test ConnectionTimeoutError
        assert error_classifier.is_retryable_error(ConnectionTimeoutError("Timeout"))
        
        # Test asyncio.TimeoutError
        assert error_classifier.is_retryable_error(asyncio.TimeoutError())
        
    def test_retryable_http_status_codes(self, error_classifier):
        """Test that specific HTTP status codes are classified as retryable."""
        # Create mock errors with status codes
        retryable_codes = [403, 429, 500, 502, 503, 504, 520, 521, 522, 524]
        
        for code in retryable_codes:
            error = Mock()
            error.status_code = code
            assert error_classifier.is_retryable_error(error), f"Status code {code} should be retryable"
    
    def test_non_retryable_http_status_codes(self, error_classifier):
        """Test that client errors are not retryable."""
        non_retryable_codes = [400, 401, 404, 405, 406, 410, 422]
        
        for code in non_retryable_codes:
            error = Mock()
            error.status_code = code
            assert not error_classifier.is_retryable_error(error), f"Status code {code} should not be retryable"
    
    def test_ssl_error_classification(self, error_classifier):
        """Test SSL error classification."""
        # Retryable SSL timeout
        timeout_error = ssl.SSLError("SSL handshake timeout")
        assert error_classifier.is_retryable_error(timeout_error)
        
        # Non-retryable certificate error
        cert_error = ssl.SSLError("Certificate verify failed")
        assert not error_classifier.is_retryable_error(cert_error)
    
    def test_extract_retry_delay(self, error_classifier):
        """Test extraction of retry delay from headers."""
        # Test with integer Retry-After
        error = Mock()
        error.response = Mock()
        error.response.headers = {'Retry-After': '60'}
        assert error_classifier.extract_retry_delay(error) == 60
        
        # Test with no Retry-After header
        error.response.headers = {}
        assert error_classifier.extract_retry_delay(error) is None

class TestSilentBlockDetector:
    """Test the SilentBlockDetector functionality."""
    
    def test_silent_block_detection(self, silent_block_detector):
        """Test detection of silent blocks."""
        url = "https://example.com/test"
        
        # First empty response
        result1 = Mock(markdown="")
        assert not silent_block_detector.check_silent_block(url, result1)
        
        # Second empty response
        result2 = Mock(markdown="")
        assert not silent_block_detector.check_silent_block(url, result2)
        
        # Third empty response - should trigger detection
        result3 = Mock(markdown="")
        assert silent_block_detector.check_silent_block(url, result3)
    
    def test_successful_response_resets_counter(self, silent_block_detector):
        """Test that successful responses reset the failure counter."""
        url = "https://example.com/test"
        
        # Add two failures
        silent_block_detector.check_silent_block(url, Mock(markdown=""))
        silent_block_detector.check_silent_block(url, Mock(markdown=""))
        
        # Successful response
        silent_block_detector.check_silent_block(url, Mock(markdown="Content here"))
        
        # Next empty response should not trigger
        assert not silent_block_detector.check_silent_block(url, Mock(markdown=""))
    
    def test_suggest_delay(self, silent_block_detector):
        """Test delay suggestions based on failure patterns."""
        domain = "example.com"
        
        # No history - default delay
        assert silent_block_detector.suggest_delay(domain) == 10
        
        # Add failures
        url = "https://example.com/test"
        silent_block_detector.check_silent_block(url, Mock(markdown=""))
        assert silent_block_detector.suggest_delay(domain) == 20  # 10 * 2^1
        
        silent_block_detector.check_silent_block(url, Mock(markdown=""))
        assert silent_block_detector.suggest_delay(domain) == 40  # 10 * 2^2

class TestConnectionManager:
    """Test the ConnectionManager functionality."""
    
    @pytest.mark.asyncio
    async def test_session_creation(self, connection_manager):
        """Test HTTP session creation."""
        session = await connection_manager.get_session()
        assert session is not None
        assert connection_manager.connection_stats['new_connections'] == 1
        
        # Second call should reuse session
        session2 = await connection_manager.get_session()
        assert session2 is session
        assert connection_manager.connection_stats['reused_connections'] == 1
        
        # Cleanup
        await connection_manager.close_session()
    
    @pytest.mark.asyncio
    async def test_connection_stats(self, connection_manager):
        """Test connection statistics tracking."""
        # Initial stats
        stats = connection_manager.get_connection_stats()
        assert stats['new_connections'] == 0
        assert stats['reused_connections'] == 0
        
        # Create session
        await connection_manager.get_session()
        stats = connection_manager.get_connection_stats()
        assert stats['new_connections'] == 1
        
        # Cleanup
        await connection_manager.close_session()

class TestErrorRecoveryManager:
    """Test the ErrorRecoveryManager functionality."""
    
    def test_schedule_retry(self, error_recovery_manager):
        """Test retry scheduling."""
        url = "https://example.com/test"
        error = "Timeout error"
        
        # Schedule first retry
        assert error_recovery_manager.schedule_retry(url, error, 0)
        assert len(error_recovery_manager.retry_queue) == 1
        
        # Check retry item
        retry_item = error_recovery_manager.retry_queue[0]
        assert retry_item['url'] == url
        assert retry_item['error'] == error
        assert retry_item['retry_count'] == 1
    
    def test_max_retries_limit(self, error_recovery_manager):
        """Test that max retries are enforced."""
        url = "https://example.com/test"
        error = "Persistent error"
        
        # Schedule up to max retries
        assert error_recovery_manager.schedule_retry(url, error, 0)
        assert error_recovery_manager.schedule_retry(url, error, 1)
        assert error_recovery_manager.schedule_retry(url, error, 2)
        
        # Should not schedule beyond max retries
        assert not error_recovery_manager.schedule_retry(url, error, 3)
    
    def test_get_ready_retries(self, error_recovery_manager):
        """Test getting retries that are ready."""
        # Monkey patch time to control scheduled times
        with patch('asyncio.get_event_loop') as mock_loop:
            mock_loop.return_value.time.return_value = 1000
            
            # Schedule retry for immediate execution
            error_recovery_manager.retry_queue.append({
                'url': 'https://example.com/test1',
                'error': 'Error 1',
                'retry_count': 1,
                'scheduled_time': 900,  # Past time
                'delay_minutes': 1,
                'metadata': {}
            })
            
            # Schedule retry for future
            error_recovery_manager.retry_queue.append({
                'url': 'https://example.com/test2',
                'error': 'Error 2',
                'retry_count': 1,
                'scheduled_time': 2000,  # Future time
                'delay_minutes': 2,
                'metadata': {}
            })
            
            ready = error_recovery_manager.get_ready_retries()
            assert len(ready) == 1
            assert ready[0]['url'] == 'https://example.com/test1'
            
            # Queue should only have future retry
            assert len(error_recovery_manager.retry_queue) == 1
            assert error_recovery_manager.retry_queue[0]['url'] == 'https://example.com/test2'

class TestRobustCrawlWithRetry:
    """Test the robust_crawl_with_retry function."""
    
    @pytest.mark.asyncio
    async def test_successful_crawl_first_attempt(self, mock_crawler):
        """Test successful crawl on first attempt."""
        # Mock successful result
        mock_result = Mock()
        mock_result.success = True
        mock_result.markdown = "Test content"
        mock_crawler.arun.return_value = mock_result
        
        result = await robust_crawl_with_retry(mock_crawler, "https://example.com")
        
        assert result.success
        assert result.markdown == "Test content"
        assert mock_crawler.arun.call_count == 1
    
    @pytest.mark.asyncio
    async def test_retry_on_timeout(self, mock_crawler):
        """Test retry behavior on timeout errors."""
        # First attempt: timeout
        # Second attempt: success
        mock_result_success = Mock()
        mock_result_success.success = True
        mock_result_success.markdown = "Test content"
        
        mock_crawler.arun.side_effect = [
            ConnectionTimeoutError("Timeout"),
            mock_result_success
        ]
        
        with patch('asyncio.sleep'):  # Mock sleep to speed up test
            result = await robust_crawl_with_retry(mock_crawler, "https://example.com")
        
        assert result.success
        assert result.markdown == "Test content"
        assert mock_crawler.arun.call_count == 2
    
    @pytest.mark.asyncio
    async def test_no_retry_on_client_error(self, mock_crawler):
        """Test that client errors are not retried."""
        # Mock 404 error
        error = HTTPStatusError("Not found")
        error.status_code = 404
        mock_crawler.arun.side_effect = error
        
        with pytest.raises(HTTPStatusError):
            await robust_crawl_with_retry(mock_crawler, "https://example.com")
        
        # Should only try once for client errors
        assert mock_crawler.arun.call_count == 1
    
    @pytest.mark.asyncio
    async def test_exponential_backoff(self, mock_crawler):
        """Test exponential backoff timing."""
        # Mock multiple failures
        mock_crawler.arun.side_effect = ConnectionTimeoutError("Timeout")
        
        sleep_calls = []
        
        async def mock_sleep(duration):
            sleep_calls.append(duration)
        
        with patch('asyncio.sleep', side_effect=mock_sleep):
            with pytest.raises(ConnectionTimeoutError):
                await robust_crawl_with_retry(
                    mock_crawler, 
                    "https://example.com",
                    max_retries=3,
                    backoff_factor=2
                )
        
        # Check exponential backoff pattern: 2, 4, 8
        assert sleep_calls == [2, 4, 8]
    
    @pytest.mark.asyncio
    async def test_error_recovery_integration(self, mock_crawler, error_recovery_manager):
        """Test integration with error recovery manager."""
        # Mock persistent failure
        mock_crawler.arun.side_effect = ConnectionTimeoutError("Persistent timeout")
        
        with patch('asyncio.sleep'):
            with pytest.raises(ConnectionTimeoutError):
                await robust_crawl_with_retry(
                    mock_crawler,
                    "https://example.com",
                    max_retries=1,
                    error_recovery=error_recovery_manager
                )
        
        # Check that retry was scheduled
        stats = error_recovery_manager.get_retry_stats()
        assert stats['total_queued'] == 1

if __name__ == "__main__":
    pytest.main([__file__, "-v"])