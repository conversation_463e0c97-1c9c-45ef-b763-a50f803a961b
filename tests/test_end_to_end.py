"""
End-to-End Tests for MCP Crawl4AI RAG Server

Tests complete user workflows from MCP tool calls to results.
"""

import asyncio
import pytest
import sys
import json
from pathlib import Path
from typing import Dict, Any
from unittest.mock import Mock, patch

# Add src to path
src_path = Path(__file__).resolve().parent.parent / "src"
sys.path.insert(0, str(src_path))

# Import MCP server components
from crawl4ai_mcp import mcp, CrawlResult
from job_manager import JobManager, JobType
from utils import search_documents, search_code_examples


class TestEndToEndWorkflows:
    """Test complete end-to-end workflows"""
    
    @pytest.fixture
    async def context(self):
        """Mock MCP context"""
        return Mock()
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_single_page_crawl_workflow(self, context):
        """Test single page crawl from MCP call to search"""
        # 1. Health check
        health_result = await mcp.health_check(context)
        assert "status" in health_result
        assert "version" in health_result
        
        # 2. Crawl single page
        url = "https://httpbin.org/html"
        crawl_result = await mcp.crawl_single_page(context, url=url)
        
        # Parse result
        result_data = json.loads(crawl_result)
        assert result_data["status"] == "Job created successfully"
        assert "job_id" in result_data
        
        job_id = result_data["job_id"]
        
        # 3. Wait for job completion (mock immediate completion)
        job_manager = JobManager()
        await job_manager.mark_job_running(job_id)
        await job_manager.complete_job(
            job_id,
            result_summary={
                "url": url,
                "chunks_created": 3,
                "content_length": 1500
            },
            pages_crawled=1,
            chunks_created=3
        )
        
        # 4. Get available sources
        sources_result = await mcp.get_available_sources(context)
        sources_data = json.loads(sources_result)
        
        assert "sources" in sources_data
        assert sources_data["total_sources"] >= 0
        
        # 5. Perform RAG query
        query_result = await mcp.perform_rag_query(
            context,
            query="HTML content",
            source="httpbin.org"
        )
        
        query_data = json.loads(query_result)
        assert "results" in query_data
        assert "total_results" in query_data
        
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_smart_crawl_workflow(self, context):
        """Test smart crawl with automatic detection"""
        # 1. Smart crawl a sitemap
        sitemap_url = "https://example.com/sitemap.xml"
        
        with patch('crawl4ai_mcp.get_crawler') as mock_crawler:
            # Mock crawler to avoid actual web requests
            mock_instance = Mock()
            mock_crawler.return_value = mock_instance
            
            crawl_result = await mcp.smart_crawl_url(
                context,
                url=sitemap_url,
                max_depth=2,
                max_concurrent=5
            )
            
            result_data = json.loads(crawl_result)
            assert result_data["status"] == "Job created successfully"
            assert result_data["job_type"] == JobType.SMART_CRAWL.value
            
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_code_search_workflow(self, context):
        """Test code extraction and search workflow"""
        # 1. Mock crawl with code content
        code_content = '''
        # Python Example
        
        Here's how to use the API:
        
        ```python
        import requests
        
        def fetch_data(url):
            response = requests.get(url)
            return response.json()
        ```
        
        And here's another example:
        
        ```python
        async def async_fetch(session, url):
            async with session.get(url) as response:
                return await response.json()
        ```
        '''
        
        # 2. Extract and store code examples (mocked)
        with patch('utils.add_code_examples_to_supabase') as mock_add_code:
            mock_add_code.return_value = None
            
            # Simulate code extraction during crawl
            from utils import extract_code_blocks
            code_blocks = extract_code_blocks(code_content)
            
            assert len(code_blocks) == 2
            assert code_blocks[0]["language"] == "python"
            assert "fetch_data" in code_blocks[0]["code"]
            
        # 3. Search for code examples
        with patch('utils.search_code_examples') as mock_search:
            mock_search.return_value = [
                {
                    "code": "def fetch_data(url):\n    response = requests.get(url)\n    return response.json()",
                    "language": "python",
                    "summary": "Function to fetch data from URL",
                    "url": "https://example.com/docs",
                    "similarity": 0.95
                }
            ]
            
            search_result = await mcp.search_code_examples(
                context,
                query="fetch data from API"
            )
            
            search_data = json.loads(search_result)
            assert search_data["total_results"] == 1
            assert search_data["results"][0]["language"] == "python"
            
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_error_recovery_workflow(self, context):
        """Test error handling and recovery workflow"""
        # 1. Attempt to crawl invalid URL
        invalid_url = "https://this-definitely-does-not-exist-12345.com"
        
        crawl_result = await mcp.crawl_single_page(context, url=invalid_url)
        result_data = json.loads(crawl_result)
        
        job_id = result_data["job_id"]
        
        # 2. Simulate job failure
        job_manager = JobManager()
        await job_manager.mark_job_running(job_id)
        await job_manager.fail_job(job_id, "DNS resolution failed")
        
        # 3. Check retry queue status
        retry_result = await mcp.get_retry_status(context)
        retry_data = json.loads(retry_result)
        
        assert "queue_status" in retry_data
        # The failed job should be in retry queue
        
    @pytest.mark.asyncio
    @pytest.mark.e2e
    @pytest.mark.skipif(not pytest.config.getoption("--knowledge-graph"), 
                        reason="Knowledge graph not enabled")
    async def test_knowledge_graph_workflow(self, context):
        """Test knowledge graph integration workflow"""
        # 1. Parse a repository
        repo_url = "https://github.com/example/test-repo"
        
        with patch('crawl4ai_mcp.get_repo_extractor') as mock_extractor:
            mock_instance = Mock()
            mock_instance.parse_repository.return_value = {
                "files_processed": 10,
                "classes_extracted": 5,
                "methods_extracted": 20
            }
            mock_extractor.return_value = mock_instance
            
            # This would normally create a REPOSITORY_PARSE job
            # For testing, we'll check the knowledge graph query
            
        # 2. Query knowledge graph
        with patch('crawl4ai_mcp.get_knowledge_validator') as mock_validator:
            mock_instance = Mock()
            mock_instance.query.return_value = {
                "repositories": ["test-repo"],
                "classes": ["TestClass"],
                "methods": ["test_method"]
            }
            mock_validator.return_value = mock_instance
            
            query_result = await mcp.query_knowledge_graph(
                context,
                command="repos"
            )
            
            query_data = json.loads(query_result)
            assert "repositories" in query_data["result"]
            
        # 3. Check AI script for hallucinations
        test_script = '''
import test_repo

class MyClass(test_repo.TestClass):
    def test_method(self):
        return self.non_existent_method()  # This should be flagged
'''
        
        with patch('crawl4ai_mcp.AIScriptAnalyzer') as mock_analyzer:
            mock_instance = Mock()
            mock_instance.analyze_script.return_value = {
                "hallucinations": [{
                    "type": "method_not_found",
                    "class": "TestClass",
                    "method": "non_existent_method",
                    "line": 5
                }],
                "confidence": 0.85
            }
            mock_analyzer.return_value = mock_instance
            
            # Would normally check script, simplified for test
            assert True  # Placeholder for actual test
            
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_performance_degradation_workflow(self, context):
        """Test system degradation under load"""
        # 1. Simulate high load by creating many jobs
        job_manager = JobManager()
        job_ids = []
        
        for i in range(20):
            crawl_result = await mcp.crawl_single_page(
                context,
                url=f"https://example.com/page{i}"
            )
            result_data = json.loads(crawl_result)
            job_ids.append(result_data["job_id"])
        
        # 2. Simulate failures to trigger degradation
        for job_id in job_ids[:10]:
            await job_manager.mark_job_running(job_id)
            await job_manager.fail_job(job_id, "Timeout")
        
        # 3. System should degrade (mocked)
        with patch('crawl4ai_mcp.GracefulDegradation') as mock_degradation:
            mock_instance = Mock()
            mock_instance.current_level = "reduced"
            mock_instance.get_current_config.return_value = {
                "level": "reduced",
                "max_concurrent": 5
            }
            
            # New crawls should use reduced resources
            crawl_result = await mcp.smart_crawl_url(
                context,
                url="https://example.com",
                max_concurrent=10  # Will be limited by degradation
            )
            
            # In real system, max_concurrent would be capped at 5
            
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_monitoring_and_health_workflow(self, context):
        """Test monitoring and health check integration"""
        # 1. Perform several operations
        urls = [
            "https://httpbin.org/status/200",
            "https://httpbin.org/status/404",
            "https://httpbin.org/delay/1"
        ]
        
        for url in urls:
            await mcp.crawl_single_page(context, url=url)
        
        # 2. Check health status
        health_result = await mcp.health_check(context)
        health_data = json.loads(health_result)
        
        assert health_data["status"] == "healthy"
        assert "performance_metrics" in health_data
        assert "system_health" in health_data
        
        # 3. Verify monitoring data is being collected
        assert health_data["job_system"]["enabled"] == True
        if "queue_metrics" in health_data:
            assert health_data["queue_metrics"]["health"] == "operational"


class TestMCPToolValidation:
    """Validate all MCP tools work correctly"""
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_all_mcp_tools_available(self):
        """Test that all MCP tools are properly registered"""
        # Get all registered tools
        tools = mcp.resources
        
        expected_tools = [
            "health_check",
            "crawl_single_page",
            "get_retry_status",
            "smart_crawl_url",
            "get_available_sources",
            "perform_rag_query",
            "search_code_examples",
            "check_ai_script_hallucinations",
            "query_knowledge_graph",
            "parse_github_repository"
        ]
        
        # Note: In FastMCP, tools are registered differently
        # This is a placeholder for the actual test
        assert len(expected_tools) == 10
        
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_tool_error_handling(self, context):
        """Test that tools handle errors gracefully"""
        # Test with invalid parameters
        with pytest.raises(Exception):
            await mcp.crawl_single_page(context, url=None)
            
        # Test with missing required fields
        with pytest.raises(Exception):
            await mcp.perform_rag_query(context, query=None)
            
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_tool_timeout_handling(self, context):
        """Test that tools handle timeouts properly"""
        # Mock a slow URL that would timeout
        slow_url = "https://httpbin.org/delay/60"
        
        # Should return quickly with job created
        result = await mcp.crawl_single_page(context, url=slow_url)
        result_data = json.loads(result)
        
        assert result_data["status"] == "Job created successfully"
        # The actual crawl happens asynchronously


if __name__ == "__main__":
    # Custom pytest configuration for e2e tests
    pytest.main([
        __file__, 
        "-v", 
        "-s", 
        "-m", "e2e",
        "--tb=short"
    ])