#!/usr/bin/env python3
"""Test error classification logic based on industry best practices"""

import pytest
import ssl
from unittest.mock import Mock
from src.crawl4ai_mcp import ErrorClassifier
from crawl4ai.async_crawler_strategy import ConnectionTimeoutError
import asyncio

# Run with: python tests/test_error_classification.py

class TestErrorClassifierIndustryStandards:
    """Test error classification against industry-standard retry patterns."""
    
    @pytest.fixture
    def classifier(self):
        return ErrorClassifier()
    
    def test_industry_standard_retryable_status_codes(self, classifier):
        """Test all industry-standard retryable status codes."""
        # Based on Oxylabs, ZenRows, Scrapfly recommendations
        industry_retryable_codes = {
            403: "Forbidden - can be temporary blocking",
            429: "Too Many Requests - rate limiting",
            500: "Internal Server Error",
            502: "Bad Gateway",
            503: "Service Unavailable",
            504: "Gateway Timeout",
            520: "Cloudflare: Unknown Error",
            521: "Cloudflare: Web Server Is Down",
            522: "Cloudflare: Connection Timed Out",
            524: "Cloudflare: A Timeout Occurred"
        }
        
        for code, description in industry_retryable_codes.items():
            error = Mock()
            error.status_code = code
            assert classifier.is_retryable_error(error), f"Status {code} ({description}) should be retryable per industry standards"
    
    def test_non_retryable_client_errors(self, classifier):
        """Test that client errors are not retryable."""
        non_retryable_codes = {
            400: "Bad Request",
            401: "Unauthorized",
            404: "Not Found",
            405: "Method Not Allowed",
            406: "Not Acceptable",
            410: "Gone",
            422: "Unprocessable Entity"
        }
        
        for code, description in non_retryable_codes.items():
            error = Mock()
            error.status_code = code
            assert not classifier.is_retryable_error(error), f"Status {code} ({description}) should NOT be retryable"
    
    def test_network_errors_are_retryable(self, classifier):
        """Test that network-level errors are retryable."""
        # Connection errors
        assert classifier.is_retryable_error(ConnectionError("Connection refused"))
        assert classifier.is_retryable_error(ConnectionTimeoutError("Connection timeout"))
        assert classifier.is_retryable_error(asyncio.TimeoutError())
        
        # DNS errors (Windows error codes)
        dns_error = OSError("DNS lookup failed")
        dns_error.errno = 11001
        assert classifier.is_retryable_error(dns_error)
    
    def test_ssl_error_discrimination(self, classifier):
        """Test SSL error classification based on error type."""
        # Timeout/handshake errors are retryable
        timeout_ssl = ssl.SSLError("SSL handshake timeout")
        assert classifier.is_retryable_error(timeout_ssl)
        
        handshake_ssl = ssl.SSLError("handshake operation timed out")
        assert classifier.is_retryable_error(handshake_ssl)
        
        # Certificate errors are NOT retryable
        cert_ssl = ssl.SSLError("certificate verify failed")
        assert not classifier.is_retryable_error(cert_ssl)
        
        verify_ssl = ssl.SSLError("unable to verify the first certificate")
        assert not classifier.is_retryable_error(verify_ssl)
    
    def test_retry_after_header_extraction(self, classifier):
        """Test extraction of Retry-After header values."""
        # Integer seconds
        error_with_seconds = Mock()
        error_with_seconds.response = Mock()
        error_with_seconds.response.headers = {'Retry-After': '120'}
        assert classifier.extract_retry_delay(error_with_seconds) == 120
        
        # No Retry-After header
        error_no_header = Mock()
        error_no_header.response = Mock()
        error_no_header.response.headers = {}
        assert classifier.extract_retry_delay(error_no_header) is None
        
        # No response object
        error_no_response = Mock(spec=[])  # No response attribute
        assert classifier.extract_retry_delay(error_no_response) is None
    
    def test_edge_cases(self, classifier):
        """Test edge cases in error classification."""
        # Error with no status_code attribute
        generic_error = Exception("Generic error")
        assert not classifier.is_retryable_error(generic_error)
        
        # Mock error with status_code but not in any list
        unknown_status = Mock()
        unknown_status.status_code = 418  # I'm a teapot
        assert not classifier.is_retryable_error(unknown_status)
        
        # OSError without errno
        os_error_no_errno = OSError("Some OS error")
        assert classifier.is_retryable_error(os_error_no_errno)  # OSError is in RETRYABLE_ERRORS
        
        # OSError with non-DNS errno
        os_error_other = OSError("File not found")
        os_error_other.errno = 2  # ENOENT
        assert classifier.is_retryable_error(os_error_other)  # Still retryable as OSError

class TestRetryDelayCalculation:
    """Test retry delay calculation logic."""
    
    @pytest.fixture
    def classifier(self):
        return ErrorClassifier()
    
    def test_retry_after_parsing(self, classifier):
        """Test parsing of different Retry-After formats."""
        # Simple integer (seconds)
        error = Mock()
        error.response = Mock()
        error.response.headers = {'Retry-After': '60'}
        assert classifier.extract_retry_delay(error) == 60
        
        # Zero delay
        error.response.headers = {'Retry-After': '0'}
        assert classifier.extract_retry_delay(error) == 0
        
        # Invalid format should return None
        error.response.headers = {'Retry-After': 'invalid'}
        assert classifier.extract_retry_delay(error) is None
        
        # Empty string
        error.response.headers = {'Retry-After': ''}
        assert classifier.extract_retry_delay(error) is None

class TestRealWorldScenarios:
    """Test classification of real-world error scenarios."""
    
    @pytest.fixture
    def classifier(self):
        return ErrorClassifier()
    
    def test_cloudflare_protection(self, classifier):
        """Test Cloudflare-specific error codes."""
        cloudflare_errors = [
            (520, "Unknown Error"),
            (521, "Web Server Is Down"),
            (522, "Connection Timed Out"),
            (524, "A Timeout Occurred")
        ]
        
        for code, description in cloudflare_errors:
            error = Mock()
            error.status_code = code
            assert classifier.is_retryable_error(error), f"Cloudflare {code} ({description}) should be retryable"
    
    def test_api_rate_limiting(self, classifier):
        """Test API rate limiting scenarios."""
        # Standard 429 rate limit
        rate_limit_error = Mock()
        rate_limit_error.status_code = 429
        assert classifier.is_retryable_error(rate_limit_error)
        
        # With Retry-After header
        rate_limit_with_delay = Mock()
        rate_limit_with_delay.status_code = 429
        rate_limit_with_delay.response = Mock()
        rate_limit_with_delay.response.headers = {'Retry-After': '300'}
        
        assert classifier.is_retryable_error(rate_limit_with_delay)
        assert classifier.extract_retry_delay(rate_limit_with_delay) == 300
    
    def test_server_overload_patterns(self, classifier):
        """Test server overload error patterns."""
        overload_codes = [500, 502, 503, 504]
        
        for code in overload_codes:
            error = Mock()
            error.status_code = code
            assert classifier.is_retryable_error(error), f"Server overload {code} should be retryable"
    
    def test_authentication_errors(self, classifier):
        """Test that authentication errors are not retryable."""
        auth_errors = [401, 403]
        
        # 401 is never retryable
        unauth_error = Mock()
        unauth_error.status_code = 401
        assert not classifier.is_retryable_error(unauth_error)
        
        # 403 IS retryable per industry best practices (can be temporary blocking)
        forbidden_error = Mock()
        forbidden_error.status_code = 403
        assert classifier.is_retryable_error(forbidden_error)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])