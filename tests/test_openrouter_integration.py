#!/usr/bin/env python3
"""
Test script for OpenRouter integration
"""

import os
import sys
import time
from dotenv import load_dotenv

# Add src to path for importing utils
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Load environment variables
load_dotenv()

def test_model_selector():
    """Test ModelSelector functionality"""
    print("🔍 Testing ModelSelector functionality...")
    
    try:
        from utils import ModelSelector, LLMStage
        
        selector = ModelSelector()
        
        # Test model retrieval for different stages
        print("   Testing model selection for different stages:")
        for stage in LLMStage:
            model = selector.get_model_for_stage(stage)
            print(f"     {stage.value}: {model}")
            
            # Test model config retrieval
            config = selector.get_model_config(model)
            if config:
                print(f"       Config: {config.quality_tier} tier, {config.cost_per_1k_tokens}/1K tokens")
            else:
                print(f"       Warning: No config found for {model}")
        
        # Test model validation
        print("   Testing model validation:")
        test_models = ["google/gemini-flash-1.5", "nonexistent/model", "anthropic/claude-3-haiku"]
        for model in test_models:
            is_valid = selector.validate_model_availability(model)
            print(f"     {model}: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        print("   ✅ ModelSelector tests passed!")
        return True
    except Exception as e:
        print(f"   ❌ ModelSelector test failed: {e}")
        return False

def test_openrouter_client_availability():
    """Test OpenRouter client initialization and availability"""
    print("\n🔍 Testing OpenRouter client availability...")
    
    try:
        from utils import OpenRouterClient, get_openrouter_client
        
        # Test direct initialization
        client = OpenRouterClient()
        is_available = client.is_available()
        
        print(f"   API Key Configured: {'✅ Yes' if client.api_key else '❌ No'}")
        print(f"   Client Available: {'✅ Yes' if is_available else '❌ No'}")
        print(f"   Base URL: {client.base_url}")
        
        # Test global client getter
        global_client = get_openrouter_client()
        print(f"   Global Client: {'✅ Available' if global_client else '❌ Not available'}")
        
        if not is_available:
            print("   ⚠️  Set OPENROUTER_API_KEY in your .env file to test API functionality")
        
        return True
    except Exception as e:
        print(f"   ❌ OpenRouter client test failed: {e}")
        return False

def test_llm_functions_without_api():
    """Test LLM functions without making API calls"""
    print("\n🔍 Testing LLM functions (without API calls)...")
    
    try:
        from utils import generate_code_example_summary, extract_source_summary, generate_contextual_embedding
        
        # Test with OpenRouter not available (should return defaults)
        old_key = os.environ.get('OPENROUTER_API_KEY')
        if 'OPENROUTER_API_KEY' in os.environ:
            del os.environ['OPENROUTER_API_KEY']
        
        # Test code example summary
        print("   Testing code example summary (no API)...")
        summary = generate_code_example_summary(
            "def hello():\n    print('Hello, world!')",
            "This is a simple function example.",
            "This demonstrates basic Python syntax."
        )
        print(f"     Result: {summary[:50]}...")
        
        # Test source summary
        print("   Testing source summary (no API)...")
        source_summary = extract_source_summary("example.com", "This is example content about a library.")
        print(f"     Result: {source_summary}")
        
        # Test contextual embedding
        print("   Testing contextual embedding (no API)...")
        contextual_text, was_enhanced = generate_contextual_embedding(
            "This is a long document with multiple sections.",
            "This is a specific chunk within the document."
        )
        print(f"     Enhanced: {'✅ Yes' if was_enhanced else '❌ No'}")
        print(f"     Result: {contextual_text[:50]}...")
        
        # Restore API key
        if old_key:
            os.environ['OPENROUTER_API_KEY'] = old_key
        
        print("   ✅ LLM functions tests passed (graceful degradation working)!")
        return True
    except Exception as e:
        print(f"   ❌ LLM functions test failed: {e}")
        return False

def test_llm_functions_with_api():
    """Test LLM functions with API calls (if API key is available)"""
    print("\n🔍 Testing LLM functions with OpenRouter API...")
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("   ⏭️  Skipping API tests - OPENROUTER_API_KEY not configured")
        return True
    
    try:
        from utils import get_openrouter_client
        
        client = get_openrouter_client()
        if not client:
            print("   ❌ OpenRouter client not available")
            return False
        
        print("   Testing code summary generation...")
        start_time = time.time()
        summary = client.generate_code_summary(
            "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)",
            "Here's a recursive implementation of the Fibonacci sequence:",
            "This function demonstrates recursive programming concepts."
        )
        duration = time.time() - start_time
        print(f"     ✅ Generated in {duration:.2f}s: {summary[:80]}...")
        
        print("   Testing source summary generation...")
        start_time = time.time()
        source_summary = client.generate_source_summary(
            "numpy", 
            "NumPy is a library for the Python programming language, adding support for large, multi-dimensional arrays and matrices, along with a large collection of high-level mathematical functions to operate on these arrays."
        )
        duration = time.time() - start_time
        print(f"     ✅ Generated in {duration:.2f}s: {source_summary[:80]}...")
        
        print("   Testing contextual embedding generation...")
        start_time = time.time()
        contextual_text = client.generate_contextual_embedding_text(
            "This is a comprehensive guide to machine learning. It covers various topics including supervised learning, unsupervised learning, and neural networks.",
            "Neural networks are computational models inspired by biological neural networks."
        )
        duration = time.time() - start_time
        print(f"     ✅ Generated in {duration:.2f}s: {contextual_text[:80]}...")
        
        print("   ✅ OpenRouter API tests passed!")
        return True
    except Exception as e:
        print(f"   ❌ OpenRouter API test failed: {e}")
        print(f"     This might be due to API limits, invalid key, or network issues")
        return False

def test_environment_configuration():
    """Test environment variable configuration"""
    print("\n🔍 Testing environment configuration...")
    
    try:
        # Test configuration loading
        config_vars = [
            "OPENROUTER_API_KEY",
            "OPENROUTER_BASE_URL", 
            "MODEL_QUALITY_LEVEL",
            "CODE_SUMMARIZATION_MODEL",
            "SOURCE_SUMMARIZATION_MODEL",
            "CONTEXTUAL_EMBEDDINGS_MODEL"
        ]
        
        print("   Environment variables:")
        for var in config_vars:
            value = os.getenv(var, "Not set")
            # Mask API key for security
            if "API_KEY" in var and value != "Not set":
                masked_value = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else "***"
                print(f"     {var}: {masked_value}")
            else:
                print(f"     {var}: {value}")
        
        # Test model configuration
        from utils import CODE_SUMMARIZATION_MODEL, SOURCE_SUMMARIZATION_MODEL, CONTEXTUAL_EMBEDDINGS_MODEL
        print(f"\n   Loaded model configurations:")
        print(f"     Code Summarization: {CODE_SUMMARIZATION_MODEL}")
        print(f"     Source Summarization: {SOURCE_SUMMARIZATION_MODEL}")
        print(f"     Contextual Embeddings: {CONTEXTUAL_EMBEDDINGS_MODEL}")
        
        print("   ✅ Environment configuration tests passed!")
        return True
    except Exception as e:
        print(f"   ❌ Environment configuration test failed: {e}")
        return False

def test_integration_flow():
    """Test the complete integration flow"""
    print("\n🔍 Testing complete integration flow...")
    
    try:
        from utils import (
            get_openrouter_client, 
            generate_code_example_summary,
            extract_source_summary,
            generate_contextual_embedding
        )
        
        # Simulate a complete workflow
        print("   Simulating complete RAG workflow with OpenRouter...")
        
        # 1. Code example processing
        print("     Step 1: Code example summarization")
        code_summary = generate_code_example_summary(
            "import requests\nresponse = requests.get('https://api.example.com')",
            "Here's how to make HTTP requests:",
            "This shows basic API interaction."
        )
        print(f"       ✅ Code summary: {code_summary[:60]}...")
        
        # 2. Source summarization
        print("     Step 2: Source summarization")
        source_summary = extract_source_summary(
            "requests", 
            "Requests is a simple HTTP library for Python, built for human beings."
        )
        print(f"       ✅ Source summary: {source_summary[:60]}...")
        
        # 3. Contextual embeddings (if enabled)
        use_contextual = os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false").lower() == "true"
        print(f"     Step 3: Contextual embeddings ({'enabled' if use_contextual else 'disabled'})")
        contextual_text, enhanced = generate_contextual_embedding(
            "This document explains how to use HTTP libraries in Python.",
            "The requests library is the most popular choice."
        )
        print(f"       ✅ Context enhanced: {'Yes' if enhanced else 'No'}")
        print(f"       ✅ Result: {contextual_text[:60]}...")
        
        print("   ✅ Integration flow test completed!")
        return True
    except Exception as e:
        print(f"   ❌ Integration flow test failed: {e}")
        return False

def main():
    """Run all OpenRouter integration tests"""
    print("🚀 Starting OpenRouter Integration Tests")
    print("=" * 60)
    
    tests = [
        ("Environment Configuration", test_environment_configuration),
        ("Model Selector", test_model_selector),
        ("OpenRouter Client", test_openrouter_client_availability),
        ("LLM Functions (No API)", test_llm_functions_without_api),
        ("LLM Functions (With API)", test_llm_functions_with_api),
        ("Integration Flow", test_integration_flow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 Running: {test_name}")
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ Test '{test_name}' crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All OpenRouter integration tests passed!")
        print("\nNext steps:")
        print("1. Set OPENROUTER_API_KEY in your .env file for full functionality")
        print("2. Configure specific models for different stages if desired")
        print("3. Enable USE_CONTEXTUAL_EMBEDDINGS=true for enhanced document processing")
        print("4. Test the MCP server: uv run src/crawl4ai_mcp.py")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)