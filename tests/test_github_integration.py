import pytest
import sys
import os
from unittest.mock import patch, MagicMock

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from crawl4ai_mcp import is_github_repo, extract_github_info, crawl_github_repository

class TestGitHubURLDetection:
    """Test GitHub URL detection and parsing."""
    
    def test_is_github_repo_valid_urls(self):
        """Test valid GitHub repository URLs."""
        valid_urls = [
            "https://github.com/PyGithub/PyGithub",
            "http://github.com/user/repo",
            "https://github.com/user/repo.git",
            "https://github.com/user/repo/tree/main",
            "https://github.com/user/repo/blob/main/README.md"
        ]
        for url in valid_urls:
            assert is_github_repo(url), f"Should detect {url} as GitHub repo"
    
    def test_is_github_repo_invalid_urls(self):
        """Test invalid URLs that should not be detected as GitHub repos."""
        invalid_urls = [
            "https://github.com",
            "https://github.com/user",
            "https://gitlab.com/user/repo",
            "https://github.com/docs",
            "https://docs.github.com/en/rest"
        ]
        for url in invalid_urls:
            assert not is_github_repo(url), f"Should not detect {url} as GitHub repo"
    
    def test_extract_github_info(self):
        """Test extraction of owner and repo name."""
        test_cases = [
            ("https://github.com/PyGithub/PyGithub", ("PyGithub", "PyGithub")),
            ("https://github.com/user/repo.git", ("user", "repo")),
            ("https://github.com/user/repo/tree/main", ("user", "repo")),
            ("https://gitlab.com/user/repo", (None, None))
        ]
        for url, expected in test_cases:
            assert extract_github_info(url) == expected

class TestGitHubCrawling:
    """Test GitHub repository crawling."""
    
    @pytest.mark.asyncio
    @patch('crawl4ai_mcp.Github')
    async def test_crawl_github_repository_success(self, mock_github):
        """Test successful repository crawling."""
        # Mock repository object
        mock_repo = MagicMock()
        mock_repo.full_name = "PyGithub/PyGithub"
        mock_repo.description = "Typed interactions with the GitHub API v3"
        mock_repo.stargazers_count = 6000
        mock_repo.language = "Python"
        mock_repo.license.name = "LGPL-3.0"
        mock_repo.updated_at = "2024-01-15"
        mock_repo.default_branch = "main"
        
        # Mock README
        mock_readme = MagicMock()
        mock_readme.decoded_content = b"# PyGithub\n\nPython library for GitHub API"
        mock_repo.get_readme.return_value = mock_readme
        
        # Mock empty docs directory
        mock_repo.get_contents.side_effect = Exception("Not found")
        
        # Setup GitHub client mock
        mock_github_instance = MagicMock()
        mock_github_instance.get_repo.return_value = mock_repo
        mock_github.return_value = mock_github_instance
        
        # Test crawling
        results = await crawl_github_repository(
            "https://github.com/PyGithub/PyGithub",
            "test_token"
        )
        
        assert len(results) == 1
        assert "PyGithub/PyGithub" in results[0]['markdown']
        assert "**Stars**: 6000" in results[0]['markdown']
    
    @pytest.mark.asyncio  
    @patch('crawl4ai_mcp.Github')
    async def test_crawl_github_repository_auth_error(self, mock_github):
        """Test handling of authentication errors."""
        from github.GithubException import GithubException
        
        mock_github_instance = MagicMock()
        mock_github_instance.get_repo.side_effect = GithubException(401, "Bad credentials")
        mock_github.return_value = mock_github_instance
        
        from crawl4ai.async_crawler_strategy import HTTPStatusError
        
        with pytest.raises(HTTPStatusError) as exc_info:
            await crawl_github_repository(
                "https://github.com/PyGithub/PyGithub",
                "invalid_token"
            )
        
        # HTTPStatusError stores the message and status code
        assert "authentication failed" in str(exc_info.value).lower()