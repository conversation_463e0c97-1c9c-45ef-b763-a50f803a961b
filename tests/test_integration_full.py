"""
Comprehensive Integration Tests for MCP Crawl4AI RAG Server

Tests the full system integration including all waves of enhancements.
"""

import asyncio
import pytest
import sys
import time
import json
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

# Add src to path
src_path = Path(__file__).resolve().parent.parent / "src"
sys.path.insert(0, str(src_path))

from utils import get_supabase_client
from job_manager import JobManager, JobType
from monitoring import CrawlMonitor, HealthStatus
from parallel_processor import ParallelCrawler
from error_handler import ErrorHandler, GracefulDegradation
from config_manager import init_config, get_config
from operational_tools import HealthChecker, PerformanceProfiler


class TestFullSystemIntegration:
    """Test full system integration with all components"""
    
    @pytest.fixture(scope="class")
    def config(self):
        """Initialize configuration for tests"""
        # Set required environment variables for testing
        import os
        os.environ["SUPABASE_URL"] = os.getenv("SUPABASE_URL", "http://localhost:54321")
        os.environ["SUPABASE_SERVICE_KEY"] = os.getenv("SUPABASE_SERVICE_KEY", "test-key")
        os.environ["CRAWL4AI_ENVIRONMENT"] = "test"
        
        return init_config()
    
    @pytest.fixture
    def job_manager(self):
        """Get job manager instance"""
        return JobManager()
    
    @pytest.fixture
    def monitor(self):
        """Get monitoring instance"""
        return CrawlMonitor()
    
    @pytest.fixture
    def error_handler(self):
        """Get error handler instance"""
        return ErrorHandler()
    
    @pytest.fixture
    def health_checker(self, config):
        """Get health checker instance"""
        return HealthChecker(config)
    
    @pytest.fixture
    def profiler(self):
        """Get performance profiler"""
        return PerformanceProfiler()
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_job_creation_to_completion_flow(self, job_manager, monitor, profiler):
        """Test complete job flow from creation to completion"""
        # Profile the entire operation
        profiler.start_profile("job_lifecycle")
        
        try:
            # Create a job
            job_id = await job_manager.create_job(
                job_type=JobType.SINGLE_PAGE,
                parameters={
                    "url": "https://example.com",
                    "chunk_size": 1000
                },
                priority=10
            )
            
            assert job_id is not None
            
            # Check initial status
            status = await job_manager.get_job_status(job_id)
            assert status["status"] == "queued"
            
            # Simulate worker picking up job
            await job_manager.mark_job_running(job_id)
            
            # Update progress
            for i in range(0, 101, 25):
                await job_manager.update_job_progress(
                    job_id, f"Processing step {i//25}", i, "Test progress"
                )
                
                # Record metrics
                await monitor.record_crawl(
                    url="https://example.com",
                    duration=0.5,
                    success=True
                )
                
                await asyncio.sleep(0.1)
            
            # Complete job
            await job_manager.complete_job(
                job_id,
                result_summary={
                    "url": "https://example.com",
                    "chunks": 5,
                    "duration": 2.5
                },
                pages_crawled=1,
                chunks_created=5
            )
            
            # Verify final status
            final_status = await job_manager.get_job_status(job_id)
            assert final_status["status"] == "completed"
            assert final_status["pages_crawled"] == 1
            
            # Check monitoring metrics
            health = monitor.get_health_status()
            assert health["status"] == HealthStatus.HEALTHY.value
            
        finally:
            profile = profiler.end_profile("job_lifecycle")
            assert profile["duration_seconds"] > 0
            
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_error_handling_with_recovery(self, job_manager, error_handler, monitor):
        """Test error handling and recovery flow"""
        # Create a job that will fail
        job_id = await job_manager.create_job(
            job_type=JobType.SINGLE_PAGE,
            parameters={
                "url": "https://invalid-domain-12345.com",
                "chunk_size": 1000
            }
        )
        
        # Simulate error
        error = Exception("DNS resolution failed")
        error_result = await error_handler.handle_error(error, {
            "url": "https://invalid-domain-12345.com",
            "job_id": job_id
        })
        
        assert error_result["handled"] == True
        assert error_result["classification"].category.value == "network"
        assert error_result["classification"].is_retryable == True
        
        # Mark job as failed
        await job_manager.fail_job(job_id, "DNS resolution failed")
        
        # Check job can be retried
        status = await job_manager.get_job_status(job_id)
        assert status["status"] == "failed"
        assert status["retry_count"] == 0
        
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_parallel_processing_with_monitoring(self, monitor):
        """Test parallel crawler with monitoring integration"""
        # Create parallel crawler with monitor
        crawler = ParallelCrawler(
            max_concurrent=3,
            max_depth=1,
            monitor=monitor
        )
        
        # Track progress
        progress_updates = []
        
        async def progress_callback(progress):
            progress_updates.append(progress)
        
        crawler.progress_callback = progress_callback
        
        # Initialize crawler
        await crawler.initialize()
        
        try:
            # Crawl multiple URLs
            urls = [
                "https://httpbin.org/html",
                "https://httpbin.org/status/200",
                "https://httpbin.org/delay/1"
            ]
            
            results = await crawler.crawl_batch(urls)
            
            # Verify results
            assert len(results) == len(urls)
            assert any(r.success for r in results)
            
            # Check progress updates
            assert len(progress_updates) > 0
            assert any(p.get("completed", False) for p in progress_updates)
            
            # Check monitoring data
            dashboard = monitor.get_dashboard_data()
            assert dashboard["performance"]["avg_crawl_time"] > 0
            
        finally:
            await crawler.cleanup()
            
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_graceful_degradation_under_load(self, monitor, config):
        """Test graceful degradation when system is under load"""
        degradation = GracefulDegradation()
        
        # Simulate high error rate
        for i in range(15):
            await monitor.record_crawl(
                url=f"https://example.com/{i}",
                duration=1.0,
                success=False,
                error="Connection timeout"
            )
        
        # Check health status
        health = monitor.get_health_status()
        assert health["metrics"]["error_rate"] > 10.0
        
        # Degrade system
        new_config = degradation.degrade()
        assert new_config["level"] == "reduced"
        assert new_config["max_concurrent"] < 10
        
        # Simulate recovery
        for i in range(20):
            await monitor.record_crawl(
                url=f"https://example.com/good/{i}",
                duration=0.5,
                success=True
            )
        
        # Restore system
        restored_config = degradation.restore()
        assert restored_config["level"] == "full"
        
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_health_check_integration(self, health_checker):
        """Test comprehensive health check"""
        health = await health_checker.check_health()
        
        assert health.status in ["healthy", "degraded", "unhealthy"]
        assert health.cpu_percent >= 0
        assert health.memory_percent >= 0
        
        # Check individual components
        assert "system_resources" in health.details
        assert "database_connectivity" in health.details
        
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_configuration_hot_reload(self, config):
        """Test configuration changes at runtime"""
        # Get initial value
        initial_concurrent = config.get("crawler.max_concurrent")
        
        # Update configuration
        config.set("crawler.max_concurrent", 5)
        assert config.get("crawler.max_concurrent") == 5
        
        # Update monitoring thresholds
        config.update({
            "monitoring": {
                "error_rate_threshold": 5.0,
                "consecutive_errors_threshold": 5
            }
        })
        
        assert config.get("monitoring.error_rate_threshold") == 5.0
        
        # Restore original
        config.set("crawler.max_concurrent", initial_concurrent)
        
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_circuit_breaker_integration(self, monitor):
        """Test circuit breaker behavior"""
        # Get circuit breaker for a component
        cb = monitor.get_circuit_breaker("test_service")
        
        assert cb.can_proceed() == True
        
        # Record failures
        for i in range(6):
            cb.record_failure()
        
        # Circuit should be open
        assert cb.state == "open"
        assert cb.can_proceed() == False
        
        # Wait for recovery timeout
        await asyncio.sleep(1)
        
        # Record success in half-open state
        cb.record_success()
        cb.record_success()
        cb.record_success()
        
        # Circuit should be closed again
        assert cb.state == "closed"
        
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_adaptive_throttling(self, monitor):
        """Test adaptive throttling behavior"""
        throttler = monitor.get_throttler("test_domain")
        
        initial_delay = throttler.current_delay
        
        # Record failures to increase delay
        for i in range(10):
            throttler.record_result(False)
        
        assert throttler.current_delay > initial_delay
        
        # Record successes to decrease delay
        for i in range(20):
            throttler.record_result(True)
        
        assert throttler.current_delay < throttler.max_delay
        
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_job_queue_metrics(self, job_manager):
        """Test job queue metrics and monitoring"""
        # Create multiple jobs
        job_ids = []
        for i in range(5):
            job_id = await job_manager.create_job(
                job_type=JobType.SINGLE_PAGE,
                parameters={
                    "url": f"https://example.com/page{i}",
                    "chunk_size": 1000
                },
                priority=5 - i  # Different priorities
            )
            job_ids.append(job_id)
        
        # Get job stats
        stats = await job_manager.get_job_stats()
        
        assert stats["queued_count"] >= 5
        assert "success_rate_percent" in stats
        assert "avg_completion_minutes" in stats
        
        # Clean up - mark jobs as completed
        for job_id in job_ids:
            await job_manager.mark_job_running(job_id)
            await job_manager.complete_job(
                job_id,
                result_summary={"test": True},
                pages_crawled=1
            )


class TestPerformanceBenchmarks:
    """Performance benchmarking tests"""
    
    @pytest.mark.asyncio
    @pytest.mark.benchmark
    async def test_job_throughput(self, job_manager):
        """Benchmark job creation and processing throughput"""
        start_time = time.time()
        job_count = 100
        
        # Create jobs
        job_ids = []
        for i in range(job_count):
            job_id = await job_manager.create_job(
                job_type=JobType.SINGLE_PAGE,
                parameters={
                    "url": f"https://example.com/bench/{i}",
                    "chunk_size": 1000
                }
            )
            job_ids.append(job_id)
        
        creation_time = time.time() - start_time
        creation_rate = job_count / creation_time
        
        # Process jobs
        process_start = time.time()
        for job_id in job_ids:
            await job_manager.mark_job_running(job_id)
            await job_manager.complete_job(
                job_id,
                result_summary={"processed": True},
                pages_crawled=1
            )
        
        process_time = time.time() - process_start
        process_rate = job_count / process_time
        
        print(f"\nPerformance Metrics:")
        print(f"  Job Creation Rate: {creation_rate:.1f} jobs/second")
        print(f"  Job Processing Rate: {process_rate:.1f} jobs/second")
        
        assert creation_rate > 50  # Should create >50 jobs/second
        assert process_rate > 30   # Should process >30 jobs/second
        
    @pytest.mark.asyncio
    @pytest.mark.benchmark
    async def test_monitoring_overhead(self, monitor):
        """Benchmark monitoring system overhead"""
        iterations = 1000
        
        # Benchmark without monitoring
        start_time = time.time()
        for i in range(iterations):
            # Simulate work
            await asyncio.sleep(0.001)
        baseline_time = time.time() - start_time
        
        # Benchmark with monitoring
        start_time = time.time()
        for i in range(iterations):
            await monitor.record_crawl(
                url=f"https://example.com/{i}",
                duration=0.1,
                success=True
            )
            await asyncio.sleep(0.001)
        monitored_time = time.time() - start_time
        
        overhead_percent = ((monitored_time - baseline_time) / baseline_time) * 100
        
        print(f"\nMonitoring Overhead:")
        print(f"  Baseline: {baseline_time:.3f}s")
        print(f"  With Monitoring: {monitored_time:.3f}s")
        print(f"  Overhead: {overhead_percent:.1f}%")
        
        assert overhead_percent < 20  # Monitoring should add <20% overhead
        
    @pytest.mark.asyncio
    @pytest.mark.benchmark
    async def test_error_handling_performance(self, error_handler):
        """Benchmark error handling performance"""
        iterations = 100
        
        start_time = time.time()
        
        for i in range(iterations):
            error = Exception(f"Test error {i}")
            await error_handler.handle_error(error, {
                "url": f"https://example.com/{i}",
                "job_id": f"job-{i}"
            })
        
        total_time = time.time() - start_time
        avg_time_ms = (total_time / iterations) * 1000
        
        print(f"\nError Handling Performance:")
        print(f"  Average Time: {avg_time_ms:.2f}ms per error")
        print(f"  Throughput: {iterations/total_time:.1f} errors/second")
        
        assert avg_time_ms < 10  # Should handle errors in <10ms


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s", "-m", "integration"])