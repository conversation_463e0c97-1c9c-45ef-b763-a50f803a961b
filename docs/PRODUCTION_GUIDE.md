# Production Deployment Guide for MCP Crawl4AI RAG Server

## Overview

This guide covers deploying and operating the MCP Crawl4AI RAG Server in a production environment. The system has been hardened through 5 waves of enhancements and is ready for internal production use.

## Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended) 
- **CPU**: 4+ cores recommended
- **RAM**: 8GB minimum, 16GB recommended
- **Disk**: 50GB+ free space
- **GPU**: Optional, for direct embedding mode

### Software Requirements
- Python 3.9+
- PostgreSQL 13+ (for Supabase)
- Docker (for BGE service if not using direct embedding)
- Chromium or Chrome browser
- Git

### Services
- Supabase instance (local or cloud)
- Neo4j instance (optional, for knowledge graph)
- OpenRouter API key (optional, for agentic RAG)

## Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd mcp-crawl4ai-rag
```

### 2. Setup Python Environment
```bash
# Using uv (recommended for speed)
pip install uv
uv venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# Install dependencies
uv pip install -e .

# One-time Crawl4AI setup
crawl4ai-setup
```

### 3. Configure Environment
```bash
# Copy example environment file
cp .env.example .env

# Edit with your values
nano .env
```

Required environment variables:
```bash
# Supabase (required)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key

# Embedding configuration
USE_DIRECT_EMBEDDING=true  # or false for BGE service
BGE_SERVICE_URL=http://localhost:8080

# Optional features
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=false
USE_RERANKING=false
USE_KNOWLEDGE_GRAPH=false

# OpenRouter (if using agentic RAG)
OPENROUTER_API_KEY=your_key

# Neo4j (if using knowledge graph)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
```

### 4. Setup Database
Run in Supabase SQL editor:
```sql
-- Execute contents of crawled_pages.sql
-- Execute contents of job_management_schema.sql
-- Execute contents of pgmq_functions.sql
```

### 5. Configure Production Settings
Edit `config.yaml`:
```yaml
environment: "production"
debug: false

crawler:
  max_concurrent: 10  # Adjust based on resources
  headless: true
  
monitoring:
  enable_monitoring: true
  log_level: "INFO"
  
features:
  enable_job_system: true
  enable_circuit_breakers: true
  enable_graceful_degradation: true
```

## Starting Services

### Using Production Script (Recommended)
```bash
# Start all services
./scripts/start_production.sh start

# Start with monitoring
./scripts/start_production.sh start --monitor

# Check status
./scripts/start_production.sh status

# View logs
./scripts/start_production.sh logs
```

### Manual Start

#### 1. Start BGE Service (if not using direct embedding)
```bash
cd services/bge-embedding-server
docker-compose up -d
```

#### 2. Start MCP Server
```bash
CRAWL4AI_ENVIRONMENT=production python src/crawl4ai_mcp.py
```

#### 3. Start Job Workers
```bash
# Start multiple workers
WORKER_ID=worker-1 python src/enhanced_job_worker.py &
WORKER_ID=worker-2 python src/enhanced_job_worker.py &
```

## Monitoring and Health

### Health Checks
```bash
# Check overall health
curl http://localhost:8051/health

# Run diagnostics
./scripts/start_production.sh diagnostics
```

### Monitoring Endpoints
- **Health**: `http://localhost:8051/health`
- **Metrics**: `http://localhost:9090/metrics` (if enabled)

### Log Files
- **MCP Server**: `logs/crawl4ai_mcp.log`
- **Workers**: `logs/worker-*.log`
- **BGE Service**: `logs/bge-service.log`

### Key Metrics to Monitor
1. **Error Rate**: Should stay below 10%
2. **Average Crawl Time**: Should be under 60s
3. **Memory Usage**: Should stay below 80%
4. **Queue Depth**: Should not exceed 1000
5. **Circuit Breaker Status**: Should be mostly "closed"

## Operational Procedures

### Handling High Load

1. **Monitor Health Status**
   ```bash
   ./scripts/start_production.sh health
   ```

2. **Check Degradation Level**
   - System auto-degrades under load
   - Levels: Full → Reduced → Minimal → Emergency

3. **Scale Workers**
   ```bash
   CRAWL4AI_WORKER_COUNT=4 ./scripts/start_production.sh restart
   ```

### Error Recovery

1. **Check Error Patterns**
   - Review logs for repeated errors
   - Monitor alerts for critical issues

2. **Reset Circuit Breakers**
   ```python
   # If needed, reset via operational tools
   python -c "from operational_tools import MaintenanceTools; 
   mt = MaintenanceTools(); 
   mt.reset_error_counters()"
   ```

3. **Clear Retry Queue**
   - Failed jobs automatically retry
   - Manual intervention rarely needed

### Database Maintenance

1. **Vacuum Database** (weekly)
   ```sql
   VACUUM ANALYZE crawled_pages;
   VACUUM ANALYZE crawl_jobs;
   ```

2. **Clean Old Jobs** (monthly)
   ```sql
   DELETE FROM crawl_jobs 
   WHERE completed_at < NOW() - INTERVAL '30 days';
   ```

3. **Monitor Table Sizes**
   ```sql
   SELECT 
     schemaname,
     tablename,
     pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
   FROM pg_tables
   WHERE schemaname = 'public'
   ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
   ```

### Log Management

1. **Rotate Logs** (automated via script)
   ```bash
   # Cleans logs older than 7 days
   python -c "from operational_tools import MaintenanceTools;
   import asyncio;
   mt = MaintenanceTools();
   asyncio.run(mt.cleanup_old_logs(7))"
   ```

2. **Archive Important Logs**
   ```bash
   tar -czf logs_$(date +%Y%m%d).tar.gz logs/*.log
   mv logs_*.tar.gz /backup/logs/
   ```

## Performance Tuning

### Crawler Configuration
```yaml
# For high-throughput
crawler:
  max_concurrent: 20
  timeout: 20000
  chunk_size: 3000

# For stability
crawler:
  max_concurrent: 5
  timeout: 60000
  retry_max_attempts: 5
```

### Worker Tuning
```bash
# More workers for CPU-bound tasks
CRAWL4AI_WORKER_COUNT=8

# Larger batches for efficiency
CRAWL4AI_BATCH_SIZE=20
```

### Database Tuning
```sql
-- Increase work memory for better query performance
ALTER SYSTEM SET work_mem = '256MB';

-- Optimize for SSDs
ALTER SYSTEM SET random_page_cost = 1.1;
```

## Troubleshooting

### Common Issues

1. **MCP Server Won't Start**
   - Check Supabase credentials
   - Verify PostgreSQL is running
   - Check port 8051 is available

2. **Workers Crashing**
   - Check crawler initialization
   - Verify Chromium is installed
   - Review worker logs

3. **High Memory Usage**
   - Reduce max_concurrent
   - Enable graceful degradation
   - Restart workers periodically

4. **Slow Crawling**
   - Check network connectivity
   - Review circuit breaker status
   - Adjust timeout settings

### Debug Mode
```bash
# Enable debug logging
CRAWL4AI_DEBUG=true CRAWL4AI_LOG_LEVEL=DEBUG ./scripts/start_production.sh start
```

### Performance Profiling
```python
# Profile specific operations
from operational_tools import PerformanceProfiler
profiler = PerformanceProfiler()

# Check profile stats
stats = profiler.get_profile_stats("job_lifecycle")
print(f"Average duration: {stats['avg_duration']}s")
```

## Security Considerations

### Internal Tool Security
- No authentication required (internal use)
- Ensure firewall blocks external access
- Use VPN for remote access
- Rotate Supabase service keys periodically

### Data Protection
- Sensitive URLs should use HTTPS
- Don't crawl sites with PII without consent
- Implement robots.txt compliance
- Add rate limiting for external sites

## Backup and Recovery

### Database Backup
```bash
# Backup Supabase data
pg_dump $SUPABASE_URL > backup_$(date +%Y%m%d).sql

# Backup configuration
cp config.yaml backup/
cp .env backup/.env.backup
```

### Recovery Procedure
1. Stop all services
2. Restore database from backup
3. Restore configuration files
4. Restart services
5. Verify health status

## Upgrading

### Rolling Upgrade
1. Start new workers with updated code
2. Stop old workers gracefully
3. Restart MCP server
4. Verify functionality

### Database Migrations
```sql
-- Check current version
SELECT * FROM schema_migrations;

-- Apply new migrations
SELECT apply_migration('version', 'migration SQL');
```

## Monitoring Dashboard

### Grafana Setup (Optional)
1. Install Grafana
2. Add PostgreSQL data source
3. Import dashboard from `monitoring/grafana-dashboard.json`
4. Set up alerts for key metrics

### Key Dashboards
- Job Processing Rate
- Error Rate by Category
- System Resource Usage
- Crawl Performance
- Queue Depth

## Maintenance Schedule

### Daily
- Check health status
- Review error logs
- Monitor queue depth

### Weekly
- Vacuum database
- Clean old logs
- Review performance metrics

### Monthly
- Archive old data
- Update dependencies
- Performance analysis
- Capacity planning

## Support

### Logs to Collect for Issues
1. MCP server logs
2. Worker logs
3. System diagnostics output
4. Database query logs
5. Configuration files (sanitized)

### Performance Baseline
After deployment, establish baselines:
- Jobs per minute: ___
- Average crawl time: ___
- Error rate: ___%
- Memory usage: ___GB
- CPU usage: ___%

Document these for comparison when issues arise.