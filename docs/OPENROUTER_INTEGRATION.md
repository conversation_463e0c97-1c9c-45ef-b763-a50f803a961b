# OpenRouter Integration Guide

This document explains the OpenRouter integration for the Crawl4AI RAG MCP Server, enabling intelligent LLM functionality for code summarization, source summarization, and contextual embeddings.

## Overview

The OpenRouter integration provides a unified interface to multiple LLM providers through OpenRouter's API, enabling:

- **Code Summarization**: Automatic generation of summaries for code examples
- **Source Summarization**: AI-generated summaries for crawled libraries/frameworks  
- **Contextual Embeddings**: Enhanced document chunks with contextual information for better retrieval

## Features

### ✅ Implemented Features

1. **Configurable Model Selection**: Choose different models for different tasks via environment variables
2. **Cost Optimization**: Support for free, balanced, and premium model tiers
3. **Intelligent Fallbacks**: Graceful degradation when OpenRouter is unavailable
4. **No Hard-coded Models**: All model selections configurable through environment variables
5. **Stage-specific Optimization**: Different models optimized for different LLM stages

### 🎯 LLM Usage Stages

| Stage | Purpose | Default Model | Environment Variable |
|-------|---------|---------------|---------------------|
| Code Summarization | Generate summaries for code examples | `google/gemini-flash-1.5` | `CODE_SUMMARIZATION_MODEL` |
| Source Summarization | Create library/framework descriptions | `mistralai/mistral-7b-instruct:free` | `SOURCE_SUMMARIZATION_MODEL` |
| Contextual Embeddings | Enhance document chunks with context | `google/gemini-flash-1.5` | `CONTEXTUAL_EMBEDDINGS_MODEL` |

## Configuration

### 1. Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Required: OpenRouter API Key
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: API Base URL (default: https://openrouter.ai/api/v1)
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Optional: Model quality level (default: balanced)
MODEL_QUALITY_LEVEL=free  # or balanced, premium

# Optional: Specific models for each stage
CODE_SUMMARIZATION_MODEL=google/gemini-flash-1.5
SOURCE_SUMMARIZATION_MODEL=mistralai/mistral-7b-instruct:free
CONTEXTUAL_EMBEDDINGS_MODEL=google/gemini-flash-1.5

# Enable contextual embeddings feature
USE_CONTEXTUAL_EMBEDDINGS=true
```

### 2. Getting OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai/keys)
2. Sign up or log in
3. Create a new API key
4. Add credits to your account (or use free models)

## Supported Models

### Free Models (No Cost)
- `google/gemini-flash-1.5` - Fast, efficient, high quality (Recommended)
- `mistralai/mistral-7b-instruct:free` - Good for general tasks
- `huggingfaceh4/zephyr-7b-beta:free` - Alternative option

### Balanced Models (Moderate Cost)
- `anthropic/claude-3-haiku` - High quality, fast responses
- `openai/gpt-3.5-turbo` - Reliable, well-tested
- `google/gemini-pro` - Balanced performance

### Premium Models (Higher Cost, Best Quality)
- `anthropic/claude-3-sonnet` - Excellent reasoning
- `anthropic/claude-3-opus` - Top-tier quality
- `openai/gpt-4` - Industry standard

## Usage Examples

### Basic Configuration (Free Models Only)
```bash
OPENROUTER_API_KEY=your_key_here
MODEL_QUALITY_LEVEL=free
CODE_SUMMARIZATION_MODEL=google/gemini-flash-1.5
SOURCE_SUMMARIZATION_MODEL=mistralai/mistral-7b-instruct:free
CONTEXTUAL_EMBEDDINGS_MODEL=google/gemini-flash-1.5
USE_CONTEXTUAL_EMBEDDINGS=true
```

### Balanced Configuration
```bash
OPENROUTER_API_KEY=your_key_here
MODEL_QUALITY_LEVEL=balanced
CODE_SUMMARIZATION_MODEL=anthropic/claude-3-haiku
SOURCE_SUMMARIZATION_MODEL=openai/gpt-3.5-turbo
CONTEXTUAL_EMBEDDINGS_MODEL=google/gemini-pro
USE_CONTEXTUAL_EMBEDDINGS=true
```

### Premium Configuration
```bash
OPENROUTER_API_KEY=your_key_here
MODEL_QUALITY_LEVEL=premium
CODE_SUMMARIZATION_MODEL=anthropic/claude-3-sonnet
SOURCE_SUMMARIZATION_MODEL=anthropic/claude-3-sonnet
CONTEXTUAL_EMBEDDINGS_MODEL=anthropic/claude-3-opus
USE_CONTEXTUAL_EMBEDDINGS=true
```

## Testing

Run the comprehensive test suite:

```bash
python test_openrouter_integration.py
```

The test suite covers:
- Model selector functionality
- OpenRouter client availability
- LLM functions (with and without API)
- Environment configuration
- Complete integration flow

## Architecture

### Core Components

1. **ModelSelector**: Manages model configurations and selection logic
2. **OpenRouterClient**: Handles API communication and request formatting
3. **LLM Integration Functions**: High-level functions for different use cases

### Class Structure

```python
class ModelSelector:
    """Intelligent model selection for different LLM stages"""
    - get_model_for_stage(stage: LLMStage) -> str
    - get_model_config(model_name: str) -> ModelConfig
    - validate_model_availability(model_name: str) -> bool

class OpenRouterClient:
    """OpenRouter API client for LLM interactions"""
    - generate_code_summary(code, context_before, context_after) -> str
    - generate_source_summary(source_id, content) -> str
    - generate_contextual_embedding_text(full_document, chunk) -> str
```

### Integration Points

The OpenRouter integration enhances these existing functions:

- `generate_code_example_summary()` - Now uses OpenRouter for code summaries
- `extract_source_summary()` - Now uses OpenRouter for source descriptions
- `generate_contextual_embedding()` - Re-enabled with OpenRouter support

## Performance Considerations

### Token Optimization
- Code snippets truncated to 1500 characters
- Context before/after limited to 500 characters each
- Full documents limited to 25,000 characters for source summaries
- Document context limited to 15,000 characters for contextual embeddings

### Cost Management
- Use free models when possible (Google Gemini Flash, Mistral Free)
- Configure different quality levels per stage based on importance
- Set appropriate max_tokens limits for each use case

### Error Handling
- Graceful fallbacks when OpenRouter is unavailable
- Default responses when API calls fail
- Retry logic with exponential backoff
- Comprehensive logging for troubleshooting

## Troubleshooting

### Common Issues

**1. "OpenRouter API key not configured"**
- Solution: Set `OPENROUTER_API_KEY` in your `.env` file

**2. "OpenRouter API request failed"**  
- Check your API key is valid
- Verify you have sufficient credits
- Check network connectivity

**3. "No response content received from OpenRouter"**
- Model may be temporarily unavailable
- Try a different model
- Check OpenRouter status page

**4. LLM functions return default responses**
- OpenRouter integration working correctly (graceful fallback)
- Set `OPENROUTER_API_KEY` to enable full functionality

### Debug Mode

Enable verbose logging by setting:
```bash
DEBUG=true
```

## Integration with Existing Features

### RAG Strategies Compatibility
- ✅ Compatible with all existing BGE embedding functionality
- ✅ Works with hybrid search, reranking, and agentic RAG
- ✅ Integrates with knowledge graph features
- ✅ No conflicts with existing Supabase storage

### Migration from Previous Version
1. No database changes required
2. Existing embeddings continue to work
3. LLM features become available when OpenRouter is configured
4. Backward compatible with disabled LLM stages

## Future Enhancements

### Planned Features
- [ ] Model performance monitoring and automatic selection
- [ ] Cost tracking and budget management
- [ ] Custom model fine-tuning integration
- [ ] Advanced prompt optimization
- [ ] Multi-model ensemble responses

### Advanced Configuration
- [ ] Per-domain model selection
- [ ] Dynamic model selection based on content type
- [ ] Custom prompt templates
- [ ] Response quality scoring and feedback

## Support

For issues or questions:
1. Check this documentation
2. Run the test suite: `python test_openrouter_integration.py`
3. Review the logs for error messages
4. Check OpenRouter API status and credits
5. Create an issue in the GitHub repository