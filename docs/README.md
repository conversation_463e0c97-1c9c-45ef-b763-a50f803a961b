# Documentation Index

This directory contains technical documentation for the Crawl4AI RAG MCP Server.

## Core Documentation

- **[PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)** - Complete project layout and architecture
- **[BGE_SERVICE.md](BGE_SERVICE.md)** - BGE embedding service setup and usage
- **[OPENROUTER_INTEGRATION.md](OPENROUTER_INTEGRATION.md)** - LLM integration guide

## Implementation Reports

- **[IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md)** - BGE embedding implementation
- **[OPENROUTER_IMPLEMENTATION_SUMMARY.md](OPENROUTER_IMPLEMENTATION_SUMMARY.md)** - OpenRouter integration summary
- **[FASTMCP_FIX_SUMMARY.md](FASTMCP_FIX_SUMMARY.md)** - FastMCP context access fix
- **[PERFORMANCE_TEST_REPORT.md](PERFORMANCE_TEST_REPORT.md)** - Performance benchmarking results

## Development Guidelines

- **[TEST_INFRASTRUCTURE_RECOMMENDATIONS.md](TEST_INFRASTRUCTURE_RECOMMENDATIONS.md)** - Testing best practices

## Quick Start

For setup and development instructions, see the main [README.md](../README.md) and [CLAUDE.md](../CLAUDE.md) files in the project root.