# BGE Embedding Service

Self-hosted BAAI bge-base-en-v1.5 embedding service for the MCP Crawl4AI RAG server.

## Features

- **Model**: BAAI/bge-base-en-v1.5 (768-dimensional embeddings)
- **GPU Support**: Optimized for NVIDIA RTX 4060 Ti 16GB
- **Batch Processing**: Efficient batch embedding generation
- **Health Monitoring**: Built-in health checks and metrics
- **Production Ready**: Gunicorn WSGI server with proper error handling

## Quick Start

### Using Docker Compose (Recommended)

1. **Build and start the service:**
   ```bash
   cd services/bge-embedding-server
   docker-compose up --build
   ```

2. **Test the service:**
   ```bash
   curl http://localhost:8080/health
   ```

### Manual Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the service:**
   ```bash
   python app.py
   ```

## API Endpoints

### Health Check
```bash
GET /health
```

Returns service status and GPU information.

### Batch Embedding Generation
```bash
POST /embed
Content-Type: application/json

{
  "texts": ["Hello world", "This is a test"]
}
```

Returns:
```json
{
  "embeddings": [[...], [...]],
  "dimensions": 768,
  "count": 2,
  "processing_time_seconds": 0.123,
  "model": "BAAI/bge-base-en-v1.5"
}
```

### Single Text Embedding
```bash
POST /embed/single
Content-Type: application/json

{
  "text": "Hello world"
}
```

Returns:
```json
{
  "embedding": [...],
  "dimensions": 768,
  "processing_time_seconds": 0.045,
  "model": "BAAI/bge-base-en-v1.5"
}
```

### System Metrics
```bash
GET /metrics
```

Returns GPU memory usage and system information.

## Configuration

Environment variables:

- `HOST`: Bind address (default: 0.0.0.0)
- `PORT`: Server port (default: 8080)
- `DEBUG`: Debug mode (default: false)
- `CUDA_VISIBLE_DEVICES`: GPU device selection (default: 0)

## GPU Requirements

- **Minimum**: NVIDIA GPU with CUDA support
- **Recommended**: RTX 4060 Ti 16GB or better
- **Memory**: ~1-2GB VRAM for the model
- **CUDA**: Version 11.8 or compatible

## Performance

- **Model Loading**: ~5-10 seconds initial startup
- **Batch Processing**: ~32 texts per batch (optimal)
- **Throughput**: ~100-500 embeddings/second depending on text length
- **Memory**: ~640MB model size + batch processing overhead

## Monitoring

The service includes built-in monitoring:

- Health checks every 30 seconds
- GPU memory tracking
- Processing time metrics
- Error logging and recovery