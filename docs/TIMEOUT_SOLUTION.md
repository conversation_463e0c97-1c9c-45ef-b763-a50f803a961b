# Timeout Solution for Large Site Crawling

## Problem Statement

When crawling large and deep websites, the synchronous nature of `smart_crawl_url` causes timeouts:
- All pages must be crawled before any chunking begins
- Claude Desktop times out after ~5-10 minutes
- Large sites (300+ pages) can take hours with the current approach
- The process never reaches the chunking stage, resulting in no data being stored

## Root Cause Analysis

The current implementation follows this synchronous pattern:
1. **Crawl ALL pages** → accumulate in memory
2. **Then chunk ALL content** → process after crawling completes  
3. **Then store ALL chunks** → batch insert to database

For a site with 300 pages at 30 seconds each, this means 2.5 hours of crawling before ANY data is stored.

## Solution: Streaming Pipeline Architecture

### Core Concept
Process and store data incrementally as pages are crawled, rather than waiting for all crawling to complete.

### Implementation Overview

#### 1. **Streaming Infrastructure** (`streaming_processor.py`)
- `StreamingChunkProcessor`: Processes and stores chunks immediately after crawling
- `StreamingCrawler`: Yields results as they complete
- `ResumableStreamingCrawler`: Supports checkpoint-based resumption

#### 2. **Enhanced Smart Crawl** (`enhanced_smart_crawl.py`)
- `smart_crawl_url_streaming`: Drop-in replacement with streaming support
- Automatic detection of when to use streaming mode
- Real-time progress updates via SSE

#### 3. **Monitoring & Resilience** (`monitoring.py`)
- `CrawlMonitor`: Comprehensive performance tracking
- `CircuitBreaker`: Prevents cascading failures
- `AdaptiveThrottler`: Dynamic rate limiting
- Health status monitoring and alerting

#### 4. **Parallel Processing** (`parallel_processor.py`)
- `ParallelPipeline`: Multi-stage concurrent processing
- `WorkScheduler`: Intelligent URL prioritization
- Separate workers for crawling and chunking

## Usage

### Basic Streaming Mode
```python
# Enable streaming mode for large sites
result = await smart_crawl_url(
    ctx=ctx,
    url="https://large-documentation-site.com",
    max_depth=3,
    streaming_mode=True  # Enable incremental processing
)
```

### Resume Interrupted Crawl
```python
# Resume from checkpoint after timeout
result = await resume_crawl(
    ctx=ctx,
    crawl_id="crawl_documentation_site_1234567890",
    max_depth=3
)
```

## Performance Improvements

### Before (Synchronous)
- 300 pages × 30s = 2.5 hours before any data stored
- Timeout after 5-10 minutes with 0 pages stored
- No progress visibility
- Complete restart required on failure

### After (Streaming)
- First page stored within 30 seconds
- Continuous progress with ~10 pages/minute stored
- Real-time progress updates every 10 pages
- Checkpoint-based resumption on failure
- Parallel processing: up to 10 crawlers + 20 chunk processors

## Configuration

### Automatic Streaming Detection
Streaming mode activates automatically when:
- `max_depth >= 3` (deep crawls)
- URL is a sitemap (many pages)
- URL contains documentation patterns (/docs, /guide, etc.)

### Performance Tuning
```python
STREAMING_CONFIG = {
    "DEFAULT_CHUNK_SIZE": 5000,
    "DEFAULT_BATCH_SIZE": 10,
    "CHECKPOINT_INTERVAL": 100,  # pages
    "MAX_CRAWL_WORKERS": 10,
    "MAX_CHUNK_WORKERS": 20,
    "ADAPTIVE_TIMEOUT": {
        "min_timeout": 10000,  # 10s
        "max_timeout": 120000,  # 2 minutes
        "scale_factor": 1.5
    }
}
```

## Monitoring

The solution includes comprehensive monitoring:

### Health Status
- **Healthy**: Normal operation
- **Degraded**: Performance issues detected
- **Critical**: Major failures occurring
- **Failed**: System unable to continue

### Key Metrics
- Pages per minute
- Chunks stored per minute
- Error rate and consecutive errors
- Average crawl/chunk/store times
- Memory usage and active connections

### Dashboard Data
```json
{
    "health": {
        "status": "healthy",
        "error_rate": 2.5,
        "consecutive_errors": 0
    },
    "performance": {
        "pages_per_minute": 12.5,
        "chunks_per_minute": 125,
        "avg_crawl_time": 2.4
    },
    "resources": {
        "active_connections": 8,
        "queue_depth": 45,
        "memory_mb": 384
    }
}
```

## Error Recovery

### Circuit Breaker Pattern
- Prevents cascading failures
- Automatic recovery after cooldown
- Component-level isolation (crawler, storage)

### Adaptive Throttling
- Dynamic rate adjustment based on success rate
- Automatic slowdown during high error rates
- Gradual speedup when stable

### Checkpoint System
- Automatic checkpoints every 100 pages
- Stores visited URLs and next URLs to process
- JSON-based persistence in `./checkpoints/`

## Integration

### Minimal Changes Required
1. Add `streaming_mode=True` to enable streaming
2. Import new modules in `crawl4ai_mcp.py`
3. No changes to existing database schema
4. Backward compatible with existing code

### MCP Tool Enhancement
```python
@mcp.tool()
async def smart_crawl_url(
    ctx: Context,
    url: str,
    max_depth: int = 3,
    max_concurrent: int = 10,
    chunk_size: int = 5000,
    streaming_mode: bool = False  # New parameter
) -> str:
```

## Benefits

1. **No More Timeouts**: Incremental processing prevents timeout issues
2. **Immediate Results**: Data available within seconds, not hours
3. **Progress Visibility**: Real-time updates on crawling progress
4. **Failure Recovery**: Resume from where it stopped
5. **Better Resource Usage**: Parallel processing and adaptive throttling
6. **Production Ready**: Comprehensive monitoring and error handling

## Future Enhancements

1. **Distributed Crawling**: Multiple instances for massive sites
2. **Smart Prioritization**: ML-based URL importance scoring  
3. **Incremental Updates**: Only crawl changed pages
4. **API Rate Limiting**: Per-domain rate limit management
5. **WebSocket Progress**: Real-time progress via WebSocket