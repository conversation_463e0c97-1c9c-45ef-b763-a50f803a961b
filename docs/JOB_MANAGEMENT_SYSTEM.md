# Job Management System for MCP Crawl4AI RAG Server

## Overview

The Job Management System eliminates MCP timeout issues by implementing asynchronous job processing using Supabase PGMQ (PostgreSQL Message Queue). Instead of blocking MCP calls that can timeout, jobs are queued and processed in the background with real-time progress tracking.

## Architecture

### Core Components

1. **Job Manager** (`src/job_manager.py`) - Handles job creation, status tracking, and progress updates
2. **Background Worker** (`src/job_worker.py`) - Processes jobs from the queue
3. **MCP Tools** - Five new tools for job interaction
4. **Database Schema** - PGMQ-based job tracking with progress monitoring
5. **Management Scripts** - Startup and monitoring utilities

### Flow Diagram

```
MCP Client → start_crawl_job → Job Queue → Background Worker → Results
     ↓              ↑                            ↓
get_job_status ←────┴────────── Progress Updates ←┘
```

## Database Schema

### Tables

- **`crawl_jobs`** - Main job tracking with status, progress, and timing
- **`job_progress`** - Detailed progress history with timestamps
- **`job_results`** - Structured results storage for completed jobs

### Job States

- `queued` - Job created and waiting for processing
- `running` - Job currently being processed
- `completed` - Job finished successfully
- `failed` - Job encountered an error
- `cancelled` - Job was cancelled by user
- `timeout` - Job exceeded time limits

### Job Types

- `single_page` - Crawl a single web page
- `smart_crawl` - Intelligent multi-page crawling
- `sitemap_crawl` - Crawl from XML sitemap
- `text_file_crawl` - Process text files directly
- `repository_parse` - Parse GitHub repositories into knowledge graph

## Setup Instructions

### 1. Database Setup

Execute the database schema in your Supabase instance:

```sql
-- Apply job management schema
\i job_management_schema.sql

-- Apply PGMQ helper functions
\i pgmq_functions.sql
```

### 2. Install Dependencies

Ensure all dependencies are installed:

```bash
# Install Python dependencies
uv pip install -e .

# Verify PGMQ extension is available in Supabase
```

### 3. Environment Configuration

Update your `.env` file with job-specific settings:

```bash
# Required for job management
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key

# Optional job system configuration
MAX_CONCURRENT_JOBS=10
DEFAULT_JOB_TIMEOUT=3600
WORKER_POLL_INTERVAL=5
```

### 4. Start the System

Use the management script to start both MCP server and workers:

```bash
# Start everything (recommended)
./scripts/start_job_system.sh start

# Check status
./scripts/start_job_system.sh status

# View logs
./scripts/start_job_system.sh logs all
```

## New MCP Tools

### 1. start_crawl_job

Creates and queues a job for background processing.

**Parameters:**
- `job_type` (string) - Type of crawl job
- `url` (string) - URL to crawl
- `max_depth` (int, optional) - Maximum crawl depth (default: 3)
- `max_concurrent` (int, optional) - Concurrent sessions (default: 10)
- `chunk_size` (int, optional) - Text chunk size (default: 5000)
- `priority` (int, optional) - Job priority 1-10 (default: 5)

**Returns:**
```json
{
  "success": true,
  "job_id": "uuid-string",
  "job_type": "smart_crawl",
  "status": "queued",
  "message": "Job created successfully and queued for processing",
  "parameters": {...},
  "priority": 5,
  "next_steps": [...]
}
```

**Example Usage:**
```python
# Start a smart crawl job
job_result = await start_crawl_job(
    job_type="smart_crawl",
    url="https://docs.python.org",
    max_depth=2,
    priority=7
)
```

### 2. get_job_status

Retrieves current job status and progress information.

**Parameters:**
- `job_id` (string) - Job ID from start_crawl_job

**Returns:**
```json
{
  "success": true,
  "job_id": "uuid-string",
  "job_type": "smart_crawl",
  "status": "running",
  "progress": {
    "percent": 45,
    "current_operation": "Crawling depth 2/3",
    "completed_operations": 12,
    "total_operations": 25
  },
  "metrics": {
    "pages_crawled": 8,
    "pages_processed": 8,
    "chunks_created": 156
  },
  "timing": {
    "created_at": "2025-01-15T10:30:00Z",
    "started_at": "2025-01-15T10:30:15Z",
    "processing_time_minutes": "2.5",
    "estimated_completion": "3.2 minutes"
  }
}
```

### 3. get_job_results

Retrieves detailed results for completed jobs.

**Parameters:**
- `job_id` (string) - Job ID from start_crawl_job

**Returns:**
```json
{
  "success": true,
  "job_id": "uuid-string",
  "status": "completed",
  "job_type": "smart_crawl",
  "summary": {
    "pages_crawled": 15,
    "pages_processed": 15,
    "chunks_created": 234,
    "processing_time_minutes": 4.2,
    "success_rate": "100%"
  },
  "timing": {
    "created_at": "2025-01-15T10:30:00Z",
    "completed_at": "2025-01-15T10:34:12Z"
  },
  "detailed_results": [...],
  "progress_history": [...],
  "next_steps": [...]
}
```

### 4. cancel_job

Cancels a queued or running job.

**Parameters:**
- `job_id` (string) - Job ID to cancel

**Returns:**
```json
{
  "success": true,
  "job_id": "uuid-string",
  "message": "Job cancelled successfully",
  "previous_status": "running",
  "previous_progress": 25,
  "cancelled_at": "2025-01-15T10:32:00Z"
}
```

### 5. list_jobs

Lists jobs with filtering and pagination.

**Parameters:**
- `status_filter` (string, optional) - Filter by status
- `job_type_filter` (string, optional) - Filter by job type
- `limit` (int, optional) - Max results (default: 20, max: 100)
- `offset` (int, optional) - Pagination offset (default: 0)

**Returns:**
```json
{
  "success": true,
  "jobs": [
    {
      "job_id": "uuid-string",
      "job_type": "smart_crawl",
      "status": "completed",
      "progress_percent": 100,
      "pages_crawled": 15,
      "chunks_created": 234,
      "created_at": "2025-01-15T10:30:00Z",
      "processing_time_minutes": 4.2
    }
  ],
  "pagination": {
    "total_returned": 5,
    "limit": 20,
    "offset": 0,
    "has_more": false
  },
  "queue_statistics": {
    "total_jobs": 25,
    "queued_jobs": 2,
    "running_jobs": 1,
    "completed_jobs": 20,
    "failed_jobs": 2,
    "cancelled_jobs": 0,
    "avg_processing_time_minutes": 3.8
  }
}
```

## Usage Patterns

### Basic Crawling Workflow

1. **Start Job**
   ```python
   result = await start_crawl_job(
       job_type="smart_crawl",
       url="https://example.com",
       max_depth=3
   )
   job_id = result["job_id"]
   ```

2. **Monitor Progress**
   ```python
   while True:
       status = await get_job_status(job_id)
       if status["status"] in ["completed", "failed", "cancelled"]:
           break
       await asyncio.sleep(5)  # Poll every 5 seconds
   ```

3. **Retrieve Results**
   ```python
   if status["status"] == "completed":
       results = await get_job_results(job_id)
       # Process results...
   ```

### Batch Processing

```python
# Start multiple jobs
job_ids = []
urls = ["https://site1.com", "https://site2.com", "https://site3.com"]

for url in urls:
    result = await start_crawl_job(
        job_type="single_page",
        url=url,
        priority=5
    )
    job_ids.append(result["job_id"])

# Monitor all jobs
while job_ids:
    for job_id in job_ids[:]:  # Copy list for safe removal
        status = await get_job_status(job_id)
        if status["status"] in ["completed", "failed", "cancelled"]:
            # Process completed job
            if status["status"] == "completed":
                results = await get_job_results(job_id)
                # Handle results...
            job_ids.remove(job_id)
    
    if job_ids:  # Still have jobs running
        await asyncio.sleep(10)
```

### Error Handling

```python
try:
    # Start job
    result = await start_crawl_job(
        job_type="smart_crawl",
        url="https://example.com"
    )
    
    if not result["success"]:
        print(f"Failed to start job: {result['error']}")
        return
    
    job_id = result["job_id"]
    
    # Monitor with timeout
    max_wait = 300  # 5 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        status = await get_job_status(job_id)
        
        if status["status"] == "completed":
            results = await get_job_results(job_id)
            break
        elif status["status"] == "failed":
            print(f"Job failed: {status['error']}")
            break
        elif status["status"] == "cancelled":
            print("Job was cancelled")
            break
        
        await asyncio.sleep(5)
    else:
        # Timeout - cancel job
        await cancel_job(job_id)
        print("Job timed out and was cancelled")

except Exception as e:
    print(f"Error in job processing: {str(e)}")
```

## Performance Targets

The job system is designed to meet these performance targets:

- **Job Creation**: <2 seconds response time
- **Status Queries**: <1 second response time
- **Progress Updates**: Real-time (10-second intervals)
- **Worker Throughput**: 10-20 concurrent jobs
- **Queue Processing**: <100ms per message
- **Database Operations**: <500ms per query

## Monitoring and Maintenance

### Health Checks

```bash
# Check database connectivity
./scripts/start_job_system.sh check-db

# View system status
./scripts/start_job_system.sh status

# Monitor logs in real-time
./scripts/start_job_system.sh logs all follow
```

### Queue Management

```python
# Get queue statistics
stats = await list_jobs()
queue_stats = stats["queue_statistics"]

print(f"Active jobs: {queue_stats['queued_jobs'] + queue_stats['running_jobs']}")
print(f"Success rate: {queue_stats['completed_jobs'] / queue_stats['total_jobs'] * 100:.1f}%")
```

### Performance Monitoring

Key metrics to monitor:

1. **Queue Length** - Jobs waiting for processing
2. **Processing Time** - Average job completion time
3. **Success Rate** - Percentage of jobs completing successfully
4. **Worker Utilization** - Number of active workers vs capacity
5. **Database Performance** - Query response times

## Troubleshooting

### Common Issues

#### 1. Jobs Stuck in Queued State

**Symptoms:** Jobs remain in "queued" status indefinitely

**Solutions:**
- Check if job workers are running: `./scripts/start_job_system.sh status`
- Start a worker: `./scripts/start_job_system.sh start-worker`
- Check worker logs: `./scripts/start_job_system.sh logs worker`

#### 2. Database Connection Issues

**Symptoms:** "Database connection failed" errors

**Solutions:**
- Verify Supabase credentials in `.env`
- Check database schema is applied
- Test connection: `./scripts/start_job_system.sh check-db`

#### 3. PGMQ Not Available

**Symptoms:** "PGMQ extension not available" errors

**Solutions:**
- Enable PGMQ extension in Supabase dashboard
- Apply PGMQ helper functions: Run `pgmq_functions.sql`
- Verify permissions for service role

#### 4. High Memory Usage

**Symptoms:** Workers consuming excessive memory

**Solutions:**
- Reduce `max_concurrent` parameter in job requests
- Restart workers periodically: `./scripts/start_job_system.sh restart`
- Monitor worker resource usage

#### 5. Job Processing Failures

**Symptoms:** Jobs failing with errors

**Solutions:**
- Check worker logs for specific error messages
- Verify URLs are accessible and valid
- Check network connectivity and timeouts
- Review crawler configuration

### Debug Mode

Enable debug logging by setting environment variable:

```bash
export LOG_LEVEL=DEBUG
./scripts/start_job_system.sh start
```

This provides detailed logging for troubleshooting issues.

## Migration from Direct MCP Tools

For existing users of the direct MCP tools, here's how to migrate:

### Before (Direct MCP Call)
```python
# This could timeout on large crawls
result = await smart_crawl_url(
    url="https://large-site.com",
    max_depth=5
)
```

### After (Job-Based)
```python
# This returns immediately
job_result = await start_crawl_job(
    job_type="smart_crawl",
    url="https://large-site.com",
    max_depth=5
)

# Monitor progress
job_id = job_result["job_id"]
while True:
    status = await get_job_status(job_id)
    print(f"Progress: {status['progress']['percent']}%")
    
    if status["status"] == "completed":
        results = await get_job_results(job_id)
        break
    elif status["status"] in ["failed", "cancelled"]:
        print(f"Job {status['status']}: {status.get('error', 'No details')}")
        break
    
    await asyncio.sleep(10)
```

## Benefits

1. **No More Timeouts** - MCP calls return immediately
2. **Real-time Progress** - Monitor job progress and current operations
3. **Better Error Handling** - Detailed error messages and recovery options
4. **Scalability** - Multiple workers can process jobs concurrently
5. **Reliability** - Failed jobs don't break the entire system
6. **Monitoring** - Comprehensive statistics and performance metrics
7. **Resource Management** - Better control over system resources