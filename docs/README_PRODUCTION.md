# MCP Crawl4AI RAG Server - Production Ready

A production-ready Model Context Protocol (MCP) server that provides AI agents with advanced web crawling and RAG (Retrieval Augmented Generation) capabilities. Built through 5 waves of systematic enhancements for reliability, performance, and ease of operation.

## 🚀 Quick Start

```bash
# Clone and setup
git clone <repository>
cd mcp-crawl4ai-rag
pip install uv && uv venv && source .venv/bin/activate
uv pip install -e .
crawl4ai-setup

# Configure environment
cp .env.example .env
# Edit .env with your Supabase credentials

# Start production server
./scripts/start_production.sh start --monitor
```

## 🏗️ Architecture Overview

```
MCP Client (Claude) → MCP Server → Job Queue → Background Workers
                                        ↓
                                  Parallel Crawlers
                                        ↓
                                  Supabase Storage
                                        ↓
                                  Vector Search/RAG
```

### Key Components
- **MCP Server**: FastMCP-based server with 10 tools
- **Job System**: Async processing with PGMQ queue
- **Parallel Crawling**: Concurrent crawling with monitoring
- **Error Handling**: Intelligent classification and recovery
- **Monitoring**: Comprehensive health and performance tracking
- **Storage**: Supabase for vectors and content

## 📋 Features

### Core Capabilities
- ✅ **Intelligent Crawling**: Auto-detects sitemaps, text files, and webpages
- ✅ **Async Job Processing**: No MCP timeouts, background processing
- ✅ **Parallel Execution**: Configurable concurrent crawling
- ✅ **Vector Search**: Semantic and hybrid search with BGE embeddings
- ✅ **Code Extraction**: Automatic code block detection and indexing
- ✅ **Error Recovery**: Circuit breakers, retries, and graceful degradation
- ✅ **Production Monitoring**: Health checks, metrics, and diagnostics

### Optional Features
- 🔧 **Knowledge Graphs**: Neo4j integration for AI hallucination detection
- 🔧 **Agentic RAG**: LLM-enhanced search results
- 🔧 **Reranking**: Result quality improvements

## 🛠️ MCP Tools Available

1. **health_check**: System health and status
2. **crawl_single_page**: Crawl a single URL
3. **smart_crawl_url**: Intelligent multi-page crawling
4. **get_retry_status**: Check retry queue status
5. **get_available_sources**: List crawled domains
6. **perform_rag_query**: Semantic/hybrid search
7. **search_code_examples**: Code-specific search
8. **check_ai_script_hallucinations**: Validate AI-generated code
9. **query_knowledge_graph**: Query code structure graph
10. **parse_github_repository**: Import repo to knowledge graph

## 📊 Performance

Established baselines (your results may vary):
- **Job Creation**: 50-100 jobs/second
- **Job Processing**: 30-50 jobs/second  
- **Search Latency**: <500ms average
- **Concurrent Crawls**: 10 default (configurable)
- **Error Overhead**: <10ms per error
- **Memory Usage**: ~100MB base + 50MB per crawler

## 🔧 Configuration

### Required Environment Variables
```bash
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key
```

### Optional Configuration
```bash
# Embedding mode
USE_DIRECT_EMBEDDING=true  # GPU mode vs HTTP service

# Features
USE_HYBRID_SEARCH=true     # Semantic + keyword search
USE_AGENTIC_RAG=false      # LLM enhancement
USE_KNOWLEDGE_GRAPH=false  # Neo4j integration

# Performance
CRAWL4AI_MAX_CONCURRENT=10
CRAWL4AI_WORKER_COUNT=2
```

See `config.yaml` for detailed configuration options.

## 📚 Documentation

- **[Production Guide](docs/PRODUCTION_GUIDE.md)**: Deployment and operations
- **[API Reference](docs/API_REFERENCE.md)**: Complete tool documentation
- **[Architecture](docs/ARCHITECTURE.md)**: System design and patterns
- **[Wave Summaries](docs/)**: Development history and decisions

## 🧪 Testing

```bash
# Run integration tests
pytest tests/test_integration_full.py -v

# Run end-to-end tests
pytest tests/test_end_to_end.py -v -m e2e

# Run performance benchmarks
python scripts/benchmark.py -v
```

## 🚦 Monitoring

### Health Check
```bash
curl http://localhost:8051/health
```

### Service Status
```bash
./scripts/start_production.sh status
```

### View Logs
```bash
./scripts/start_production.sh logs
```

## 🔄 Operational Procedures

### Scaling Workers
```bash
CRAWL4AI_WORKER_COUNT=4 ./scripts/start_production.sh restart
```

### Database Maintenance
```sql
-- Weekly vacuum
VACUUM ANALYZE crawled_pages;

-- Monthly cleanup
DELETE FROM crawl_jobs WHERE completed_at < NOW() - INTERVAL '30 days';
```

### Graceful Degradation
System automatically degrades under load:
- **Full** → **Reduced** → **Minimal** → **Emergency**

## 🏃 Development Workflow

1. **Make Changes**: Primary work in `src/crawl4ai_mcp.py` and `src/utils.py`
2. **Test Locally**: Run relevant tests from `/tests`
3. **Check Performance**: Run benchmarks if performance-critical
4. **Update Docs**: Keep documentation current
5. **Deploy**: Use production script for deployment

## 🤝 Contributing

This is an internal tool for a two-person team. Follow these principles:
- **KISS**: Keep it simple
- **YAGNI**: Don't over-engineer
- **Test**: Ensure reliability
- **Document**: Keep docs updated

## 📈 Enhancement History

1. **Wave 1**: Architecture Analysis - Systematic design approach
2. **Wave 2**: Core Infrastructure - PGMQ job system implementation  
3. **Wave 3**: Advanced Features - Monitoring and parallel processing
4. **Wave 4**: Production Hardening - Error handling and operations
5. **Wave 5**: Validation & Documentation - Testing and guides

## 🔒 Security Notes

- Internal tool - no authentication required
- Ensure firewall blocks external access
- Use VPN for remote access
- Rotate Supabase keys periodically
- Follow robots.txt compliance

## 📝 License

Internal use only.

## 🆘 Troubleshooting

### Common Issues

**MCP Server Won't Start**
- Check Supabase credentials
- Verify PostgreSQL is running
- Ensure port 8051 is available

**High Memory Usage**
- Reduce `max_concurrent` in config
- Enable graceful degradation
- Restart workers periodically

**Slow Crawling**
- Check network connectivity
- Review circuit breaker status
- Adjust timeout settings

For detailed troubleshooting, see the [Production Guide](docs/PRODUCTION_GUIDE.md).

---

**Built with**: Python, FastMCP, Crawl4AI, Supabase, PostgreSQL, PGMQ