# Test Infrastructure Recommendations

## Current State Assessment

### Existing Test Coverage
- ✅ **Performance benchmarking**: Direct embedding comparison tests
- ✅ **Integration testing**: BGE service and OpenRouter validation  
- ✅ **Manual testing**: Scripts for individual component validation
- ❌ **Unit testing**: No pytest configuration or unit tests
- ❌ **CI/CD integration**: No automated test pipeline
- ❌ **Coverage reporting**: No code coverage metrics

### Test Execution Issues
1. **Service Dependencies**: Tests fail when BGE service not running
2. **Environment Setup**: No virtual environment activation in test scripts
3. **Missing Dependencies**: FlagEmbedding not installed in test environment
4. **No Test Runner**: Manual Python execution required

## Recommended Improvements

### 1. Add Pytest Infrastructure

**Create `pyproject.toml` test configuration:**
```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--cov=src",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=70"
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "pytest-timeout>=2.1.0",
    "pytest-benchmark>=4.0.0"
]
```

### 2. Create Unit Tests

**Example unit test structure for `tests/unit/test_timeout_protection.py`:**
```python
import pytest
import asyncio
from unittest.mock import Mock, patch
from src.crawl4ai_mcp import robust_crawl_with_retry, ErrorRecoveryManager

class TestTimeoutProtection:
    @pytest.mark.asyncio
    async def test_progressive_timeout_increase(self):
        """Test that timeouts increase progressively"""
        mock_crawler = Mock()
        mock_crawler.arun.side_effect = ConnectionTimeoutError("Timeout")
        
        with pytest.raises(ConnectionTimeoutError):
            await robust_crawl_with_retry(mock_crawler, "http://example.com", max_retries=2)
        
        # Verify timeout progression: 30s, 45s, 60s
        assert mock_crawler.arun.call_count == 3
        timeouts = [call[1]['config'].page_timeout for call in mock_crawler.arun.call_args_list]
        assert timeouts == [30000, 45000, 60000]

    @pytest.mark.asyncio
    async def test_exponential_backoff(self):
        """Test exponential backoff between retries"""
        # Test implementation
        pass

    @pytest.mark.asyncio
    async def test_partial_result_preservation(self):
        """Test that partial results are saved on failure"""
        # Test implementation
        pass
```

### 3. Add Integration Test Fixtures

**Create `tests/conftest.py`:**
```python
import pytest
import asyncio
from unittest.mock import Mock

@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing"""
    client = Mock()
    client.table.return_value.insert.return_value.execute.return_value = Mock(data=[])
    return client

@pytest.fixture
def mock_crawler():
    """Mock AsyncWebCrawler for testing"""
    crawler = Mock()
    crawler.arun.return_value = Mock(success=True, html="<html>Test</html>")
    return crawler

@pytest.fixture
async def error_recovery_manager(mock_supabase_client):
    """Create ErrorRecoveryManager instance for testing"""
    from src.crawl4ai_mcp import ErrorRecoveryManager
    return ErrorRecoveryManager(mock_supabase_client)
```

### 4. Implement CI/CD Pipeline

**Create `.github/workflows/test.yml`:**
```yaml
name: Test Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        pip install uv
        uv venv
        source .venv/bin/activate
        uv pip install -e ".[test]"
        crawl4ai-setup
    
    - name: Run unit tests
      run: |
        source .venv/bin/activate
        pytest tests/unit -v --cov=src --cov-report=xml
    
    - name: Run integration tests
      run: |
        source .venv/bin/activate
        pytest tests/integration -v
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

### 5. Add Performance Benchmarking

**Create `tests/benchmarks/test_performance.py`:**
```python
import pytest
from src.utils import create_embeddings_batch

@pytest.mark.benchmark(group="embedding")
def test_embedding_throughput(benchmark):
    """Benchmark embedding generation throughput"""
    test_texts = ["Sample text"] * 100
    result = benchmark(create_embeddings_batch, test_texts)
    assert len(result) == 100

@pytest.mark.benchmark(group="crawling")
@pytest.mark.asyncio
async def test_crawl_performance(benchmark):
    """Benchmark crawling performance"""
    # Benchmark implementation
    pass
```

### 6. Create Test Documentation

**Add `tests/README.md`:**
```markdown
# Test Suite Documentation

## Running Tests

### Quick Start
```bash
# Install test dependencies
uv pip install -e ".[test]"

# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_direct_embedding.py

# Run benchmarks
pytest tests/benchmarks -v --benchmark-only
```

### Test Categories

1. **Unit Tests** (`tests/unit/`)
   - Isolated component testing
   - Mock external dependencies
   - Fast execution (<1s per test)

2. **Integration Tests** (`tests/integration/`)
   - Test component interactions
   - May require services running
   - Medium execution (1-10s per test)

3. **Performance Tests** (`tests/benchmarks/`)
   - Measure throughput and latency
   - Benchmark comparisons
   - Longer execution (10-60s per test)

### Writing Tests

Follow these patterns:
- Use pytest fixtures for common setup
- Mock external services in unit tests
- Use `pytest.mark.asyncio` for async tests
- Add timeouts with `pytest.mark.timeout(30)`
```

### 7. Add Pre-commit Hooks

**Create `.pre-commit-config.yaml`:**
```yaml
repos:
  - repo: local
    hooks:
      - id: pytest
        name: pytest
        entry: pytest tests/unit -v
        language: system
        pass_filenames: false
        always_run: true
```

## Implementation Priority

1. **Phase 1** (Immediate):
   - Add pytest to dependencies
   - Create basic unit tests for new timeout features
   - Fix existing test dependencies

2. **Phase 2** (Week 1):
   - Implement test fixtures and conftest
   - Add integration tests for error recovery
   - Set up coverage reporting

3. **Phase 3** (Week 2):
   - Create CI/CD pipeline
   - Add performance benchmarks
   - Document testing procedures

4. **Phase 4** (Ongoing):
   - Maintain 70%+ code coverage
   - Add tests for new features
   - Regular benchmark comparisons

## Expected Outcomes

- **Code Coverage**: Achieve 70%+ coverage
- **Test Execution Time**: <2 minutes for full suite
- **CI/CD Integration**: Automated testing on every commit
- **Performance Tracking**: Historical benchmark data
- **Quality Gates**: Prevent regressions with test requirements