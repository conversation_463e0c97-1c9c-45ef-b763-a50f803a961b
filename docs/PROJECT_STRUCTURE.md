# Project Structure

This document outlines the organized structure of the Crawl4AI RAG MCP Server project.

## Directory Layout

```
mcp-crawl4ai-rag/
├── CLAUDE.md                    # Main development guide
├── README.md                    # Project overview
├── pyproject.toml              # Python project configuration
├── crawled_pages.sql           # Supabase database schema
├── Dockerfile                  # Docker containerization
├── LICENSE                     # Project license
├── uv.lock                     # Dependency lock file
├── .env.example               # Environment configuration template
│
├── src/                       # Source code
│   ├── crawl4ai_mcp.py       # Main MCP server
│   └── utils.py              # Utility functions and classes
│
├── tests/                     # Test suite
│   ├── test_bge_integration.py         # BGE embedding service tests
│   ├── test_direct_embedding.py       # Direct GPU embedding tests
│   └── test_openrouter_integration.py # OpenRouter LLM tests
│
├── scripts/                   # Utility scripts
│   └── start_services.sh     # Service startup and management
│
├── docs/                      # Documentation
│   ├── PROJECT_STRUCTURE.md             # This file
│   ├── BGE_SERVICE.md                   # BGE embedding service guide
│   ├── IMPLEMENTATION_SUMMARY.md        # BGE implementation summary
│   ├── OPENROUTER_INTEGRATION.md       # OpenRouter integration guide
│   ├── OPENROUTER_IMPLEMENTATION_SUMMARY.md # OpenRouter implementation summary
│   └── ref-embed.md                     # Embedding reference documentation
│
├── services/                  # External services
│   └── bge-embedding-server/  # BGE HTTP embedding service
│       ├── app.py            # Flask application
│       ├── Dockerfile        # Service container
│       ├── docker-compose.yml # Service orchestration
│       └── requirements.txt  # Service dependencies
│
├── knowledge_graphs/          # Knowledge graph tools (optional)
│   ├── ai_hallucination_detector.py  # AI hallucination detection
│   ├── ai_script_analyzer.py         # Script analysis
│   ├── parse_repo_into_neo4j.py     # Repository parsing
│   └── query_knowledge_graph.py     # Graph querying
│
└── logs/                      # Runtime logs (created automatically)
    ├── mcp_server.log        # MCP server logs
    └── mcp_server.pid        # Process ID file
```

## Key Components

### Core Application (`/src`)
- **`crawl4ai_mcp.py`**: Main MCP server with 8 tools for web crawling and RAG operations
- **`utils.py`**: Comprehensive utility module including:
  - OpenRouter LLM integration
  - Direct GPU embedding management
  - Database operations
  - Vector similarity search

### Testing Suite (`/tests`)
- **`test_bge_integration.py`**: Tests for BGE HTTP embedding service
- **`test_direct_embedding.py`**: Performance tests comparing direct vs HTTP embedding
- **`test_openrouter_integration.py`**: Comprehensive OpenRouter LLM integration tests

### Scripts (`/scripts`)
- **`start_services.sh`**: Service management script with:
  - Intelligent service startup
  - Live log streaming
  - Background mode support
  - Process management

### Documentation (`/docs`)
- **Architecture guides**: Technical implementation details
- **Integration guides**: Step-by-step setup instructions
- **API documentation**: Comprehensive feature documentation
- **Reference materials**: Technical specifications and examples

### Services (`/services`)
- **BGE Embedding Service**: Self-hosted HTTP service for BGE embeddings
  - GPU-accelerated embedding generation
  - Docker containerization
  - Health monitoring and metrics

## Usage Patterns

### Running Tests
```bash
# From project root
python tests/test_direct_embedding.py
python tests/test_bge_integration.py
python tests/test_openrouter_integration.py
```

### Starting Services
```bash
# From project root
scripts/start_services.sh both        # Start with live logs
scripts/start_services.sh both-bg     # Start in background
scripts/start_services.sh logs        # View live logs
scripts/start_services.sh stop        # Stop all services
```

### Accessing Documentation
```bash
# Main development guide
cat CLAUDE.md

# Specific feature documentation
cat docs/OPENROUTER_INTEGRATION.md
cat docs/BGE_SERVICE.md

# Implementation summaries
cat docs/OPENROUTER_IMPLEMENTATION_SUMMARY.md
```

## File Naming Conventions

### Tests
- `test_*.py` - All test files follow this pattern
- Located in `/tests` directory
- Import from `../src` using relative paths

### Documentation
- `*.md` - Markdown format for all documentation
- UPPERCASE for major documents (implementation summaries)
- Mixed case for guides and references
- Located in `/docs` directory

### Scripts
- `*.sh` - Shell scripts for automation
- Descriptive names indicating purpose
- Located in `/scripts` directory
- Executable permissions set

## Development Workflow

1. **Code Development**: Work in `/src` directory
2. **Testing**: Run tests from `/tests` directory
3. **Documentation**: Update relevant files in `/docs`
4. **Service Management**: Use scripts from `/scripts`
5. **Logs**: Monitor runtime logs in `/logs`

This organized structure provides clear separation of concerns and makes the project easier to navigate and maintain.