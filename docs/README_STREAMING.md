# Streaming Crawl Implementation Guide

## Quick Start

### 1. Install Dependencies
No additional dependencies required - uses existing packages.

### 2. Enable Streaming Mode

```python
# In your MCP client
result = await mcp_client.call_tool(
    "smart_crawl_url",
    {
        "url": "https://docs.example.com",
        "max_depth": 3,
        "streaming_mode": True  # Enable streaming
    }
)
```

### 3. Monitor Progress

The streaming mode provides real-time updates:
```json
{
    "type": "progress",
    "data": {
        "processed": 45,
        "successful": 43,
        "chunks_stored": 215,
        "success_rate": 95.6,
        "processing_rate": 12.3
    }
}
```

## Implementation Steps

### Step 1: Add New Modules

Place these files in your `src/` directory:
- `streaming_processor.py` - Core streaming infrastructure
- `enhanced_smart_crawl.py` - Enhanced crawling functions
- `monitoring.py` - Monitoring and resilience
- `parallel_processor.py` - Parallel processing

### Step 2: Update Main MCP Server

Add to `crawl4ai_mcp.py`:

```python
# At the top with other imports
from enhanced_smart_crawl import smart_crawl_url_streaming

# Modify the existing smart_crawl_url function
@mcp.tool()
async def smart_crawl_url(
    ctx: Context,
    url: str,
    max_depth: int = 3,
    max_concurrent: int = 10,
    chunk_size: int = 5000,
    streaming_mode: bool = False  # New parameter
) -> str:
    """
    [Existing docstring...]
    
    NEW: Streaming Mode (streaming_mode=True)
    - Processes and stores chunks incrementally as pages are crawled
    - Provides real-time progress updates
    - Supports checkpoint-based resumption for interrupted crawls
    - Prevents timeouts on large sites by not waiting for all crawling to complete
    """
    
    # Use streaming mode if requested
    if streaming_mode:
        return await smart_crawl_url_streaming(
            ctx=ctx,
            url=url,
            max_depth=max_depth,
            max_concurrent=max_concurrent,
            chunk_size=chunk_size
        )
    
    # Original implementation continues...
```

### Step 3: Add Resume Capability

Add new MCP tool:

```python
@mcp.tool()
async def resume_crawl(
    ctx: Context,
    crawl_id: str,
    max_depth: int = 3,
    max_concurrent: int = 10
) -> str:
    """
    Resume an interrupted crawl from a checkpoint.
    """
    from enhanced_smart_crawl import resume_crawl_from_checkpoint
    return await resume_crawl_from_checkpoint(
        ctx=ctx,
        crawl_id=crawl_id,
        max_depth=max_depth,
        max_concurrent=max_concurrent
    )
```

## Testing

### Test Streaming Mode

```bash
# Test with a small site first
python test_streaming.py

# Monitor logs
tail -f crawl_monitor.log
```

### Test Script Example

```python
# test_streaming.py
import asyncio
from crawl4ai_mcp import get_crawler
from streaming_processor import StreamingChunkProcessor, StreamingCrawler
from monitoring import CrawlMonitor

async def test_streaming():
    crawler = get_crawler()
    chunk_processor = StreamingChunkProcessor()
    monitor = CrawlMonitor(log_file="crawl_monitor.log")
    
    streaming_crawler = StreamingCrawler(crawler, chunk_processor)
    
    # Test with a documentation site
    test_url = "https://docs.python.org/3/tutorial/"
    
    async for update in streaming_crawler.crawl_recursive_streaming(
        [test_url],
        max_depth=2,
        max_concurrent=5
    ):
        if update["type"] == "result":
            print(f"✅ Processed: {update['result'].url}")
        elif update["type"] == "checkpoint":
            print(f"💾 Checkpoint at depth {update['depth']}")
        elif update["type"] == "complete":
            print(f"🎉 Complete! {update['summary']}")

if __name__ == "__main__":
    asyncio.run(test_streaming())
```

## Configuration

### Environment Variables

Add to `.env`:
```bash
# Streaming Configuration
STREAMING_CHUNK_BATCH_SIZE=10
STREAMING_CHECKPOINT_INTERVAL=100
STREAMING_PROGRESS_INTERVAL=10

# Monitoring
ENABLE_CRAWL_MONITORING=true
CRAWL_MONITOR_LOG_FILE=./logs/crawl_monitor.log

# Performance
MAX_CRAWL_WORKERS=10
MAX_CHUNK_WORKERS=20
ADAPTIVE_THROTTLING=true
```

### Performance Tuning

Adjust based on your resources:

```python
# For high-performance servers
config = {
    "max_crawl_workers": 20,
    "max_chunk_workers": 40,
    "chunk_batch_size": 20
}

# For limited resources
config = {
    "max_crawl_workers": 5,
    "max_chunk_workers": 10,
    "chunk_batch_size": 5
}
```

## Monitoring Dashboard

### Simple Web Dashboard

```python
# monitoring_dashboard.py
from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import json

app = FastAPI()

@app.get("/dashboard")
async def dashboard():
    # Get monitor instance
    monitor = get_current_monitor()
    data = monitor.get_dashboard_data()
    
    html = f"""
    <html>
        <head>
            <title>Crawl Monitor</title>
            <meta http-equiv="refresh" content="5">
        </head>
        <body>
            <h1>Crawl Monitor Dashboard</h1>
            <h2>Health: {data['health']['status']}</h2>
            <h3>Performance</h3>
            <ul>
                <li>Pages/min: {data['performance']['pages_per_minute']:.1f}</li>
                <li>Chunks/min: {data['performance']['chunks_per_minute']:.1f}</li>
                <li>Avg Crawl Time: {data['performance']['avg_crawl_time']:.2f}s</li>
            </ul>
            <h3>Errors</h3>
            <ul>
                <li>Total: {data['errors']['total']}</li>
                <li>Rate: {data['errors']['rate']:.1f}%</li>
                <li>Consecutive: {data['errors']['consecutive']}</li>
            </ul>
            <pre>{json.dumps(data, indent=2)}</pre>
        </body>
    </html>
    """
    return HTMLResponse(content=html)

# Run with: uvicorn monitoring_dashboard:app --reload
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure all new modules are in the `src/` directory
   - Check Python path includes the src directory

2. **Database Errors**
   - Streaming uses same schema - no changes needed
   - Check Supabase connection is configured

3. **Memory Usage**
   - Monitor with `ps aux | grep python`
   - Adjust batch sizes if memory grows too large

4. **Slow Performance**
   - Check monitor dashboard for bottlenecks
   - Reduce concurrent workers if system is overloaded
   - Enable adaptive throttling

### Debug Mode

Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# In your code
logger = logging.getLogger(__name__)
logger.debug(f"Processing URL: {url}")
```

## Best Practices

1. **Start Small**: Test with small sites before large ones
2. **Monitor Resources**: Watch CPU, memory, and network usage
3. **Use Checkpoints**: Enable for any crawl over 100 pages
4. **Tune Workers**: Start conservative, increase gradually
5. **Set Alerts**: Configure monitoring alerts for production

## Next Steps

1. **Deploy**: Test in your environment
2. **Monitor**: Use dashboard to track performance
3. **Tune**: Adjust settings based on your needs
4. **Scale**: Consider distributed crawling for massive sites

For questions or issues, check the logs and monitoring dashboard first!