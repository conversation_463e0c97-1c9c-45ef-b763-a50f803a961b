# System Architecture - MCP Crawl4AI RAG Server

## Overview

The MCP Crawl4AI RAG Server is a production-ready web crawling and retrieval-augmented generation system built with a focus on reliability, performance, and ease of operation. The system has evolved through 5 waves of enhancements to reach its current architecture.

## High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Claude/LLM    │     │  External APIs  │     │   Web Content   │
│   (MCP Client)  │     │  (OpenRouter)   │     │   (Websites)    │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                         │
         │ MCP Protocol          │ HTTP                   │ HTTP/S
         ▼                       ▼                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                        MCP Server Layer                          │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────┐    │
│  │   FastMCP   │  │ Config Mgr   │  │ Operational Tools  │    │
│  │   Server    │  │              │  │ (Health, Diag)     │    │
│  └──────┬──────┘  └──────┬───────┘  └─────────┬──────────┘    │
└─────────┼────────────────┼────────────────────┼────────────────┘
          │                │                    │
          ▼                ▼                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Job Management Layer                         │
│  ┌──────────────┐  ┌──────────────┐  ┌───────────────────┐    │
│  │ Job Manager  │  │   PGMQ       │  │  Job Workers      │    │
│  │              │  │   Queue      │  │  (Enhanced)       │    │
│  └──────┬───────┘  └──────┬───────┘  └────────┬──────────┘    │
└─────────┼──────────────────┼──────────────────┼────────────────┘
          │                  │                   │
          ▼                  ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Processing Layer                              │
│  ┌──────────────┐  ┌──────────────┐  ┌───────────────────┐    │
│  │  Parallel    │  │   Error      │  │   Monitoring      │    │
│  │  Crawler     │  │   Handler    │  │   System          │    │
│  └──────┬───────┘  └──────┬───────┘  └────────┬──────────┘    │
└─────────┼──────────────────┼──────────────────┼────────────────┘
          │                  │                   │
          ▼                  ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Storage Layer                               │
│  ┌──────────────┐  ┌──────────────┐  ┌───────────────────┐    │
│  │   Supabase   │  │     BGE      │  │     Neo4j         │    │
│  │  (Postgres)  │  │  Embeddings  │  │ (Knowledge Graph) │    │
│  └──────────────┘  └──────────────┘  └───────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. MCP Server Layer

#### FastMCP Server (`crawl4ai_mcp.py`)
- **Purpose**: Exposes crawling functionality through Model Context Protocol
- **Key Features**:
  - 10 MCP tools for various operations
  - Async request handling
  - Global resource management with lifespan
  - Immediate job creation (no timeouts)

#### Configuration Manager (`config_manager.py`)
- **Purpose**: Centralized configuration with validation
- **Key Features**:
  - Hierarchical configuration (defaults → file → env → runtime)
  - YAML/JSON file support
  - Environment variable overrides
  - Hot reload capability
  - Comprehensive validation

#### Operational Tools (`operational_tools.py`)
- **Purpose**: System health, diagnostics, and maintenance
- **Components**:
  - Health Checker: Resource and service monitoring
  - Performance Profiler: Function timing and memory tracking
  - Diagnostic Tool: Dependency and system checks
  - Maintenance Tools: Log cleanup, metrics export

### 2. Job Management Layer

#### Job Manager (`job_manager.py`)
- **Purpose**: Async job lifecycle management
- **Features**:
  - Job creation with priorities
  - Progress tracking
  - State transitions (queued → running → completed/failed)
  - Retry management
  - Statistics and monitoring

#### PGMQ Integration
- **Purpose**: PostgreSQL-based message queue
- **Features**:
  - Reliable job queuing
  - Visibility timeout for crash recovery
  - At-least-once delivery
  - Built on Supabase

#### Enhanced Job Workers (`enhanced_job_worker.py`)
- **Purpose**: Background job processing with monitoring
- **Features**:
  - Parallel crawling integration
  - Real-time progress updates
  - Circuit breaker awareness
  - Graceful shutdown
  - Performance tracking

### 3. Processing Layer

#### Parallel Crawler (`parallel_processor.py`)
- **Purpose**: High-performance concurrent crawling
- **Features**:
  - Configurable concurrency (default: 10)
  - Domain-based throttling
  - Circuit breakers per domain
  - Resource pooling
  - Progress callbacks

#### Error Handler (`error_handler.py`)
- **Purpose**: Intelligent error classification and recovery
- **Features**:
  - Error categorization (network, storage, etc.)
  - Severity classification
  - Retry strategies with backoff
  - Pattern detection
  - Graceful degradation (4 levels)

#### Monitoring System (`monitoring.py`)
- **Purpose**: Comprehensive system monitoring
- **Components**:
  - Performance metrics collection
  - Circuit breakers
  - Adaptive throttling
  - Health status tracking
  - Alert system

### 4. Storage Layer

#### Supabase Integration (`utils.py`)
- **Purpose**: Primary data storage and vector search
- **Features**:
  - Document storage with chunking
  - Vector embeddings for semantic search
  - Hybrid search (semantic + keyword)
  - Code example extraction
  - Source metadata management

#### BGE Embeddings
- **Two Modes**:
  1. Direct GPU embedding (faster startup)
  2. HTTP service via Docker (better resource isolation)
- **Model**: BAAI/bge-large-en-v1.5
- **Features**: High-quality embeddings for RAG

#### Neo4j Knowledge Graph (Optional)
- **Purpose**: AI hallucination detection
- **Features**:
  - Repository structure storage
  - Class/method relationship tracking
  - Script validation
  - Query interface

## Data Flow

### 1. Crawl Request Flow
```
MCP Client → MCP Tool Call → Job Creation → PGMQ Queue
                                    ↓
                              Job Worker picks up
                                    ↓
                           Parallel Crawler executes
                                    ↓
                         Content chunking & embedding
                                    ↓
                           Storage in Supabase
                                    ↓
                         Job completion & cleanup
```

### 2. Search Request Flow
```
MCP Client → RAG Query Tool → Vector/Hybrid Search
                                    ↓
                              Supabase query
                                    ↓
                         Optional reranking/LLM enhancement
                                    ↓
                            Results returned
```

### 3. Error Recovery Flow
```
Error Occurs → Error Classification → Severity Assessment
                                            ↓
                              Recovery Strategy Selection
                                            ↓
                         Retry/Circuit Break/Degrade/Alert
                                            ↓
                              Pattern Detection
                                            ↓
                           System Adjustment
```

## Key Design Patterns

### 1. Circuit Breaker Pattern
- Prevents cascade failures
- Per-domain implementation
- Configurable thresholds
- Automatic recovery

### 2. Adaptive Throttling
- Dynamic rate limiting
- Success rate based
- Per-domain delays
- Automatic adjustment

### 3. Graceful Degradation
- 4 operational levels
- Feature toggling
- Automatic transitions
- Recovery support

### 4. Resource Pooling
- Crawler instance reuse
- Connection pooling
- Memory management
- Concurrent limits

### 5. Event-Driven Architecture
- Async job processing
- Progress callbacks
- Health monitoring
- Alert notifications

## Scalability Considerations

### Horizontal Scaling
- Multiple worker instances
- Shared job queue
- Stateless workers
- Load distribution

### Vertical Scaling
- Configurable concurrency
- Memory-aware processing
- Resource monitoring
- Automatic throttling

### Performance Optimizations
- Batch operations
- Connection reuse
- Intelligent caching
- Parallel processing

## Security Model

### Internal Tool Security
- No authentication (internal use)
- Network isolation recommended
- VPN for remote access
- Audit logging

### Data Security
- HTTPS for web crawling
- Encrypted storage
- Service key rotation
- Input validation

## Monitoring and Observability

### Metrics Collection
- Performance metrics (timing, memory)
- Error rates and patterns
- Resource utilization
- Queue depths

### Health Checks
- System resources
- Service connectivity
- Component status
- Composite health

### Logging
- Structured logging
- Multiple log levels
- File and console output
- Automatic rotation

### Alerting
- Configurable thresholds
- Multiple alert channels
- Pattern-based alerts
- Escalation support

## Deployment Architecture

### Development
```
Single instance → Local Supabase → Direct embedding
```

### Production
```
Load Balancer → Multiple MCP servers
                        ↓
               Shared Job Queue (PGMQ)
                        ↓
              Multiple Worker Instances
                        ↓
          Supabase (Cloud) + BGE Service
```

### High Availability
- Service redundancy
- Automatic failover
- Health monitoring
- Self-healing

## Technology Stack

### Core Technologies
- **Language**: Python 3.9+
- **Framework**: FastMCP
- **Database**: PostgreSQL (via Supabase)
- **Queue**: PGMQ
- **Browser**: Chromium (via Crawl4AI)

### Key Libraries
- **crawl4ai**: Web crawling engine
- **sentence-transformers**: Embeddings
- **supabase-py**: Database client
- **fastmcp**: MCP server framework
- **asyncio**: Async operations

### Optional Components
- **Neo4j**: Knowledge graphs
- **OpenRouter**: LLM enhancement
- **Docker**: BGE service container

## Future Architecture Considerations

### Potential Enhancements
1. **Distributed Crawling**: Multi-node crawler coordination
2. **Event Streaming**: Kafka/Redis Streams for real-time updates
3. **API Gateway**: RESTful API alongside MCP
4. **Caching Layer**: Redis for frequently accessed content
5. **ML Pipeline**: Custom embedding models

### Scalability Path
1. **Phase 1**: Current architecture (1-10 users)
2. **Phase 2**: Add caching and CDN (10-100 users)
3. **Phase 3**: Microservices split (100-1000 users)
4. **Phase 4**: Full distributed system (1000+ users)