# API Reference - MCP Crawl4AI RAG Server

## MCP Tools

The server exposes the following tools through the Model Context Protocol:

### health_check

Check server health and status.

**Parameters**: None

**Returns**:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "crawler_status": "initialized",
  "job_system": {
    "enabled": true,
    "workers": 2
  },
  "knowledge_graph": {
    "enabled": false
  },
  "performance_metrics": {
    "avg_crawl_time": 2.5,
    "success_rate": 95.5
  }
}
```

### crawl_single_page

Crawl a single web page and store its content.

**Parameters**:
- `url` (string, required): URL of the page to crawl
- `chunk_size` (integer, optional): Size of content chunks (default: 5000)

**Returns**:
```json
{
  "status": "Job created successfully",
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "job_type": "single_page",
  "message": "Job queued for processing"
}
```

### smart_crawl_url

Intelligently crawl a URL based on its type (sitemap, text file, or webpage).

**Parameters**:
- `url` (string, required): URL to crawl
- `max_depth` (integer, optional): Maximum recursion depth (default: 3)
- `max_concurrent` (integer, optional): Maximum concurrent crawlers (default: 10)
- `chunk_size` (integer, optional): Content chunk size (default: 5000)

**Returns**:
```json
{
  "status": "Job created successfully",
  "job_id": "550e8400-e29b-41d4-a716-446655440001",
  "job_type": "smart_crawl",
  "detected_type": "sitemap",
  "message": "Sitemap detected, will crawl all URLs"
}
```

### get_retry_status

Get the current retry queue status and process ready retries.

**Parameters**: None

**Returns**:
```json
{
  "queue_status": {
    "total_items": 5,
    "ready_for_retry": 2,
    "waiting": 3
  },
  "processed_retries": [
    {
      "url": "https://example.com",
      "retry_count": 1,
      "status": "requeued"
    }
  ]
}
```

### get_available_sources

Get all available sources (domains) that have been crawled.

**Parameters**: None

**Returns**:
```json
{
  "sources": [
    {
      "source_id": "docs.python.org",
      "summary": "Python official documentation",
      "total_documents": 150,
      "total_words": 500000,
      "last_updated": "2024-01-15T10:30:00Z"
    }
  ],
  "total_sources": 1
}
```

### perform_rag_query

Perform a RAG query on stored content.

**Parameters**:
- `query` (string, required): Search query
- `source` (string, optional): Filter by source domain
- `match_count` (integer, optional): Maximum results (default: 5)

**Returns**:
```json
{
  "results": [
    {
      "content": "Matched content chunk...",
      "metadata": {
        "source": "https://example.com/page",
        "chunk_index": 2,
        "created_at": "2024-01-15T10:00:00Z"
      },
      "similarity": 0.85
    }
  ],
  "total_results": 5,
  "search_method": "hybrid",
  "enhanced": false
}
```

### search_code_examples

Search for code examples in the stored content.

**Parameters**:
- `query` (string, required): Search query for code
- `source_id` (string, optional): Filter by source
- `match_count` (integer, optional): Maximum results (default: 5)

**Returns**:
```json
{
  "results": [
    {
      "code": "def example_function():\n    return 'Hello, World!'",
      "language": "python",
      "summary": "Simple function example",
      "url": "https://example.com/tutorial",
      "similarity": 0.92
    }
  ],
  "total_results": 3,
  "languages": ["python", "javascript"]
}
```

### check_ai_script_hallucinations

Check an AI-generated script for potential hallucinations against the knowledge graph.

**Parameters**:
- `script_path` (string, required): Absolute path to the Python script

**Returns**:
```json
{
  "hallucinations": [
    {
      "type": "method_not_found",
      "class": "RequestsSession",
      "method": "fetch_async",
      "line": 15,
      "suggestion": "Use 'get' or 'post' instead"
    }
  ],
  "confidence": 0.85,
  "total_issues": 1,
  "severity": "medium"
}
```

### query_knowledge_graph

Query the Neo4j knowledge graph for repository information.

**Parameters**:
- `command` (string, required): Query command

**Available Commands**:
- `repos` - List all repositories
- `explore <repo_name>` - Explore a repository
- `classes <repo_name>` - List classes in a repository
- `class <class_name>` - Get class details
- `method <method_name>` - Search for methods
- `query <cypher_query>` - Execute custom Cypher query

**Returns**:
```json
{
  "command": "repos",
  "result": {
    "repositories": ["pydantic-ai", "crawl4ai"],
    "count": 2
  },
  "execution_time": 0.05
}
```

### parse_github_repository

Parse a GitHub repository into the knowledge graph.

**Parameters**:
- `repo_url` (string, required): GitHub repository URL

**Returns**:
```json
{
  "status": "Job created successfully",
  "job_id": "550e8400-e29b-41d4-a716-446655440002",
  "job_type": "repository_parse",
  "repository": "https://github.com/example/repo"
}
```

## Job Management System

### Job Types
- `single_page`: Crawl a single URL
- `smart_crawl`: Intelligent multi-page crawl
- `sitemap_crawl`: Crawl all URLs in a sitemap
- `text_file_crawl`: Process text file content
- `repository_parse`: Parse GitHub repository

### Job States
- `queued`: Job created and waiting
- `running`: Job being processed
- `completed`: Job finished successfully
- `failed`: Job failed with error

### Job Progress
Jobs report progress through the following stages:
1. Initialization (0-10%)
2. Content fetching (10-50%)
3. Processing (50-80%)
4. Storage (80-95%)
5. Completion (95-100%)

## Error Handling

### Error Categories
- `network`: Connection, timeout, DNS issues
- `parsing`: Content parsing failures
- `storage`: Database or storage errors
- `validation`: Input validation failures
- `resource`: Memory or CPU constraints
- `configuration`: Missing or invalid config
- `external_service`: Third-party service errors

### Error Recovery
- **Automatic Retry**: Network and external service errors
- **Circuit Breaker**: Prevents cascade failures
- **Graceful Degradation**: Reduces features under load
- **Fallback Storage**: Queues data for later processing

## Configuration

### Environment Variables
```bash
# Required
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_KEY=xxx

# Optional
USE_DIRECT_EMBEDDING=true
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=false
USE_RERANKING=false
USE_KNOWLEDGE_GRAPH=false

# Crawler settings
CRAWL4AI_MAX_CONCURRENT=10
CRAWL4AI_MAX_DEPTH=3
CRAWL4AI_TIMEOUT=30000

# Monitoring
CRAWL4AI_ENABLE_MONITORING=true
CRAWL4AI_LOG_LEVEL=INFO

# Workers
CRAWL4AI_WORKER_COUNT=2
```

### Configuration File (config.yaml)
```yaml
crawler:
  max_concurrent: 10
  max_depth: 3
  chunk_size: 5000
  
monitoring:
  enable_monitoring: true
  error_rate_threshold: 10.0
  
features:
  enable_job_system: true
  enable_circuit_breakers: true
```

## Performance Considerations

### Rate Limits
- Default: 10 concurrent crawls
- Per-domain throttling with adaptive delays
- Circuit breakers per domain

### Resource Usage
- Memory: ~100MB base + 50MB per crawler
- CPU: 1 core per 5 concurrent crawlers
- Disk: Varies with content volume

### Optimization Tips
1. Use appropriate chunk sizes (3000-8000)
2. Enable direct embedding for better performance
3. Set reasonable max_depth (1-3)
4. Monitor queue depth and adjust workers
5. Use circuit breakers to prevent overload

## Monitoring Metrics

### Key Metrics
- `crawl_time_avg`: Average crawl duration
- `error_rate`: Percentage of failed operations
- `queue_depth`: Number of pending jobs
- `active_connections`: Current crawler count
- `memory_usage`: RAM consumption

### Health Status Levels
- `healthy`: All systems operational
- `degraded`: Some issues, reduced capacity
- `critical`: Major issues, emergency mode
- `failed`: System non-operational

## Database Schema

### Main Tables
- `crawled_pages`: Stored content with embeddings
- `code_examples`: Extracted code snippets
- `sources`: Domain-level metadata
- `crawl_jobs`: Job queue and status
- `job_progress`: Detailed progress tracking
- `job_results`: Completion summaries

### Indexes
- Vector similarity search on embeddings
- Full-text search on content
- Source and URL lookups
- Job status queries

## Best Practices

### Crawling
1. Respect robots.txt
2. Use appropriate delays between requests
3. Handle errors gracefully
4. Monitor resource usage
5. Clean up old data periodically

### Search
1. Use specific queries for better results
2. Filter by source when possible
3. Adjust match_count based on needs
4. Consider reranking for quality

### Job Management
1. Monitor job queue depth
2. Handle failed jobs appropriately
3. Set reasonable priorities
4. Clean up old job records

### Performance
1. Batch operations when possible
2. Use connection pooling
3. Enable caching where appropriate
4. Monitor and tune regularly