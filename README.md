# AY Team Knowledge Base - RAG System

**Simple AI-powered research and knowledge management**

## What This Does

This system lets you:
- **Crawl any website** and store the content
- **Search through everything** you've saved using AI
- **Get instant answers** from your collected knowledge

Perfect for research across software development, financial analysis, eBay operations, and family planning.

## Team Use Cases

### For Aung - Software Development & Research
- Crawl documentation sites (React, Python, frameworks)
- Index financial research papers and market analysis
- Store investment guides and trading resources  
- Quick code example searches during development

### For Yohanna - Business & Family Management
- Index eBay seller resources and best practices
- Store customer support templates and solutions
- Research family activities, education resources
- Organize product research and competitor analysis

## Quick Setup (KISS Approach)

### 1. Get Your Keys
- **Supabase**: Create account at [supabase.com](https://supabase.com), get URL + service key
- **OpenRouter**: Get free API key at [openrouter.ai](https://openrouter.ai/keys) (for AI features)

### 2. Database Setup  
1. In your Supabase dashboard, go to SQL Editor
2. Copy-paste everything from `crawled_pages.sql`
3. Run it

### 3. Environment Setup
Copy `.env.example` to `.env` and fill in your keys:
```
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_KEY=your_service_key_here  
OPENROUTER_API_KEY=your_openrouter_key_here

# Simple config - no over-engineering
USE_DIRECT_EMBEDDING=true
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=false
USE_RERANKING=false
USE_KNOWLEDGE_GRAPH=false
```

### 4. Run It
```bash
# Install and run (one time setup)
pip install uv
uv venv && source .venv/bin/activate  # Windows: .venv\Scripts\activate
uv pip install -e .
crawl4ai-setup

# Start the system
uv run src/crawl4ai_mcp.py
```

### 5. Connect to Claude
```bash
claude mcp add-json crawl4ai-rag '{"type":"http","url":"http://localhost:8051/sse"}' --scope user
```

## How to Use

### Basic Workflow
1. **Crawl a site**: "Crawl https://example.com and store it"
2. **Search**: "Find information about [topic] from crawled sites"  
3. **Get answers**: The AI will search your knowledge base and provide answers

### Real Examples

**Aung's Development Research**:
```
"Crawl the React documentation site"
"Search for React hooks examples"
"Find Python async/await best practices"
```

**Yohanna's eBay Research**:
```  
"Crawl eBay seller help pages"
"Search for product photography tips"
"Find customer service response templates"
```

**Financial Research**:
```
"Crawl this investment analysis report"  
"Search for information about dividend stocks"
"Find market analysis for tech stocks"
```

**Family Planning**:
```
"Crawl this parenting resource site"
"Search for kids activities in our area"  
"Find information about school programs"
```

## Available Tools

The system provides these simple commands:

1. **`crawl_single_page`** - Crawl one webpage
2. **`smart_crawl_url`** - Crawl an entire site (follows links)
3. **`get_available_sources`** - See what sites you've crawled
4. **`perform_rag_query`** - Search your knowledge base

## Configuration Options

### Basic Setup (Recommended)
```
USE_DIRECT_EMBEDDING=true     # Faster, runs locally
USE_HYBRID_SEARCH=true        # Better search results
USE_AGENTIC_RAG=false         # Keep it simple
USE_RERANKING=false           # Not needed for basic use
USE_KNOWLEDGE_GRAPH=false     # Advanced feature, skip for now
```

### If You Want Code Examples
```
USE_AGENTIC_RAG=true          # Extracts code snippets
USE_RERANKING=true            # Better code search results
```

## Troubleshooting

### Common Issues

**"Can't connect to database"**
- Check your Supabase URL and service key in `.env`
- Make sure you ran the SQL setup script

**"Embedding errors"**  
- Make sure you have GPU available, or set `USE_DIRECT_EMBEDDING=false`
- Try the BGE service: `cd services/bge-embedding-server && docker-compose up`

**"Slow crawling"**
- Start with single pages before crawling entire sites
- Some sites block crawlers - try different URLs

### Getting Help
- Check the logs in the terminal
- Look at `CLAUDE.md` for detailed technical info
- Keep it simple - don't enable advanced features until you need them

## Files You Care About

- **`README.md`** - This guide
- **`.env`** - Your configuration  
- **`crawled_pages.sql`** - Database setup
- **`CLAUDE.md`** - Detailed technical documentation

## KISS Principle Reminders

- Start with basic configuration
- Only enable advanced features when you have a specific need  
- Use the simple workflow: crawl → search → get answers
- Don't over-engineer - this is a tool, not a project
- Focus on your actual research needs, not the technology

---

*This is AY team's private knowledge base. Keep it simple, keep it useful.*