# Simplified Docker Compose for MCP Crawl4AI RAG Server
# Single container deployment - most common use case
version: '3.8'

services:
  mcp-crawl4ai-rag:
    build:
      context: .
      dockerfile: Dockerfile
    image: mcp-crawl4ai-rag:latest
    container_name: mcp-crawl4ai-rag-server
    ports:
      - "8051:8051"     # MCP Server
    environment:
      # Core MCP Server Configuration
      - MCP_SERVER_NAME=mcp-crawl4ai-rag
      - MCP_SERVER_VERSION=1.0.0
      - HOST=0.0.0.0
      - PORT=8051
      
      # BGE Embedding Service Configuration (Direct mode)
      - USE_DIRECT_EMBEDDING=true
      - BGE_SERVICE_URL=${BGE_SERVICE_URL:-http://bge-embedding-service:8080}
      
      # Supabase Configuration (Required)
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # Production Configuration
      - CRAWL4AI_ENVIRONMENT=production
      - CRAWL4AI_ENABLE_MONITORING=true
      - CRAWL4AI_ENABLE_JOB_SYSTEM=true
      
      # Performance Configuration  
      - CRAWL4AI_MAX_CONCURRENT=${MAX_CONCURRENT:-10}
      - CRAWL4AI_MAX_DEPTH=${MAX_DEPTH:-3}
      - DEFAULT_CHUNK_SIZE=${CHUNK_SIZE:-5000}
      - REQUEST_TIMEOUT=${TIMEOUT:-30000}
      
      # Feature Toggles
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-false}
      - USE_KNOWLEDGE_GRAPH=${USE_KNOWLEDGE_GRAPH:-false}
      
      # Optional: OpenRouter for Agentic RAG
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-}
      - OPENROUTER_MODEL=${OPENROUTER_MODEL:-anthropic/claude-3.5-sonnet}
      
      # Logging
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - CRAWL4AI_LOG_FILE=/app/logs/crawl4ai_mcp.log
      
      # Core Transport Configuration
      - TRANSPORT=stdio
      
      # Error Handling & Resilience
      - CRAWL4AI_ENABLE_CIRCUIT_BREAKERS=true
      - CRAWL4AI_ENABLE_GRACEFUL_DEGRADATION=true

    volumes:
      - mcp-logs:/app/logs
      - mcp-data:/app/data
    
    networks:
      - mcp-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "python", "-c", "import socket; s=socket.socket(); s.settimeout(2); exit(0 if s.connect_ex(('localhost', 8051))==0 else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
      
    # Resource limits (adjust based on your needs)
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

networks:
  mcp-network:
    driver: bridge
    name: mcp-crawl4ai-rag-network

volumes:
  mcp-logs:
    name: mcp-crawl4ai-rag-logs
    driver: local
  mcp-data:
    name: mcp-crawl4ai-rag-data
    driver: local