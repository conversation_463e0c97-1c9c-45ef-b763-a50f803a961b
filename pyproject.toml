[project]
name = "crawl4ai-mcp"
version = "0.1.0"
description = "MCP server for integrating web crawling and RAG into AI agents and AI coding assistants with integrated Research CLI"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # Core MCP server dependencies
    "crawl4ai==0.6.2",
    "supabase==2.15.1",
    "requests>=2.31.0",
    "python-dotenv>=1.0.0",
    "sentence-transformers>=4.1.0",
    "FlagEmbedding>=1.2.0",
    "torch>=2.0.0",
    "numpy>=1.24.0",
    "neo4j>=5.28.1",
    "mcp==1.12.1",
    
    # Research CLI dependencies
    "click>=8.0.0",
    "rich>=13.0.0",
]

[project.scripts]
# CLI entry points
crawl4ai-research = "cli.research:main"
research-cli = "cli.research:main"

[project.optional-dependencies]
# Development dependencies
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.23.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

# CLI-only dependencies for minimal installations
cli = [
    "click>=8.0.0",
    "rich>=13.0.0",
    "mcp==1.12.1",
]
