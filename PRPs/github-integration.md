# PRP: GitHub URL Detection and PyGithub Integration

## Feature Overview
Add GitHub URL detection to smart_crawl_url, implement basic PyGithub integration, and add environment variable configuration to enable crawling and processing GitHub repositories for knowledge extraction and AI-assisted development workflows.

## Context and Research

### Existing Codebase Patterns
The current implementation in `src/crawl4ai_mcp.py` has:
- URL detection functions: `is_sitemap()` (line 1176) and `is_txt()` (line 1188)
- `smart_crawl_url()` function (line 1590) that determines crawling strategy
- Robust error handling with exponential backoff and retry logic
- Smart markdown chunking (line 1222)
- Supabase integration for storage
- Knowledge graph integration for AI hallucination detection

### PyGithub Documentation and Examples
**Official PyGithub Documentation**: https://pygithub.readthedocs.io/en/stable/

**Key PyGithub Patterns** (from research in spark-rag):
```python
# Authentication
from github import Github
from github import Auth

auth = Auth.Token("your_personal_access_token")
g = Github(auth=auth)

# Repository access
repo = g.get_repo("owner/repo-name")

# Get README
readme = repo.get_readme()
readme_content = readme.decoded_content.decode('utf-8')

# Recursive directory traversal
contents = repo.get_contents("")
while contents:
    file_content = contents.pop(0)
    if file_content.type == "dir":
        contents.extend(repo.get_contents(file_content.path))
    else:
        print(file_content)
```

### GitHub API References
- **REST API Docs**: https://docs.github.com/en/rest
- **Repository Endpoint**: `GET /repos/{owner}/{repo}`
- **Rate Limiting**: 5,000 requests/hour for authenticated users
- **PyGithub Examples**: https://github.com/PyGithub/PyGithub/tree/main/doc/examples

### Critical Gotchas
1. **PyGithub Rate Limiting**: PyGithub doesn't handle rate limiting automatically - must implement retry logic
2. **File Decoding**: `get_contents()` returns objects with `.decoded_content` that needs decoding
3. **Binary Files**: Will throw exception on decode - need try/except handling
4. **Directory Traversal**: `repo.get_contents("")` gets root, must manually recurse subdirectories
5. **Token Scopes**: Private repos require appropriate token permissions

## Implementation Blueprint

### Pseudocode Overview
```python
# 1. Add URL detection
def is_github_repo(url: str) -> bool:
    # Use regex to match github.com/owner/repo patterns
    # Handle .git suffix and various URL formats
    
def extract_github_info(url: str) -> tuple[str, str]:
    # Extract owner and repository name from URL

# 2. GitHub crawling function
async def crawl_github_repository(url: str, github_token: str) -> List[Dict]:
    # Initialize PyGithub client
    # Get repository object
    # Fetch README content
    # Recursively get docs/ folder contents
    # Format as markdown with metadata
    # Return list of crawl results

# 3. Integration in smart_crawl_url
elif is_github_repo(url):
    github_token = os.getenv("GITHUB_TOKEN")
    if not github_token:
        return error_response("GitHub token required")
    crawl_results = await crawl_github_repository(url, github_token)
    crawl_type = "github_repository"
```

### Detailed Implementation Steps

#### 1. Update Dependencies (pyproject.toml)
Add after line 17:
```toml
    "PyGithub>=2.0.0",
```

#### 2. Environment Configuration (.env.example)
Add after line 167:
```bash
# GitHub Integration Configuration
# Personal Access Token for GitHub API access
# Create at: https://github.com/settings/tokens
# Required scopes: public_repo (for public repos), repo (for private repos)
GITHUB_TOKEN=

# Optional: GitHub Enterprise API URL (leave empty for github.com)
# Example: https://github.enterprise.com/api/v3
GITHUB_API_URL=
```

#### 3. URL Detection Functions (crawl4ai_mcp.py)
Add after line 1198:
```python
import re
from github import Github, Auth
from github.GithubException import RateLimitExceededException, UnknownObjectException, GithubException

GITHUB_URL_PATTERN = re.compile(
    r'https?://github\.com/([^/]+)/([^/]+)(?:/.*)?'
)

def is_github_repo(url: str) -> bool:
    """
    Check if a URL is a GitHub repository.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is a GitHub repository, False otherwise
    """
    match = GITHUB_URL_PATTERN.match(url)
    if match:
        owner, repo = match.groups()
        # Remove .git suffix if present
        repo = repo.rstrip('.git')
        # Validate it's not a GitHub page subdomain or special path
        if owner and repo and '.' not in owner:
            return True
    return False

def extract_github_info(url: str) -> tuple[str, str]:
    """
    Extract owner and repository name from GitHub URL.
    
    Args:
        url: GitHub URL
        
    Returns:
        Tuple of (owner, repo_name) or (None, None) if not a valid GitHub URL
    """
    match = GITHUB_URL_PATTERN.match(url)
    if match:
        owner, repo = match.groups()
        repo = repo.rstrip('.git')
        return owner, repo
    return None, None
```

#### 4. GitHub Crawling Function
Add before `crawl_markdown_file` function:
```python
async def crawl_github_repository(url: str, github_token: str, max_files: int = 50) -> List[Dict[str, Any]]:
    """
    Crawl a GitHub repository and extract documentation content.
    
    Args:
        url: GitHub repository URL
        github_token: GitHub personal access token
        max_files: Maximum number of files to process
        
    Returns:
        List of dictionaries with URL and markdown content
    """
    owner, repo_name = extract_github_info(url)
    if not owner or not repo_name:
        logger.error(f"Invalid GitHub URL: {url}")
        return []
    
    try:
        # Initialize GitHub client
        auth = Auth.Token(github_token)
        g = Github(auth=auth, base_url=os.getenv("GITHUB_API_URL", "https://api.github.com"))
        
        # Get repository
        try:
            repo = g.get_repo(f"{owner}/{repo_name}")
        except UnknownObjectException:
            logger.error(f"Repository not found: {owner}/{repo_name}")
            return []
        
        results = []
        files_processed = 0
        
        # Build repository metadata markdown
        repo_md = f"# {repo.full_name}\n\n"
        if repo.description:
            repo_md += f"{repo.description}\n\n"
        repo_md += f"- **Stars**: {repo.stargazers_count}\n"
        repo_md += f"- **Language**: {repo.language or 'Not specified'}\n"
        repo_md += f"- **License**: {repo.license.name if repo.license else 'Not specified'}\n"
        repo_md += f"- **Last Updated**: {repo.updated_at}\n\n"
        
        # Get README
        try:
            readme = repo.get_readme()
            readme_content = readme.decoded_content.decode('utf-8')
            results.append({
                'url': f"{url}#readme",
                'markdown': repo_md + "## README\n\n" + readme_content
            })
            files_processed += 1
        except Exception as e:
            logger.warning(f"Could not fetch README: {e}")
        
        # Get documentation files
        doc_paths = ["docs", "documentation", "doc", "wiki"]
        for doc_path in doc_paths:
            if files_processed >= max_files:
                break
                
            try:
                contents = repo.get_contents(doc_path)
                if isinstance(contents, list):
                    # Process directory contents
                    while contents and files_processed < max_files:
                        file_content = contents.pop(0)
                        
                        if file_content.type == "dir":
                            # Add subdirectory contents
                            try:
                                contents.extend(repo.get_contents(file_content.path))
                            except Exception as e:
                                logger.warning(f"Could not access directory {file_content.path}: {e}")
                        elif file_content.name.endswith(('.md', '.rst', '.txt')):
                            # Process text files
                            try:
                                content = file_content.decoded_content.decode('utf-8')
                                results.append({
                                    'url': f"{url}/blob/{repo.default_branch}/{file_content.path}",
                                    'markdown': f"# {file_content.path}\n\n{content}"
                                })
                                files_processed += 1
                            except Exception as e:
                                logger.warning(f"Could not decode {file_content.path}: {e}")
            except Exception as e:
                # Directory doesn't exist, continue
                logger.debug(f"Documentation path {doc_path} not found: {e}")
        
        logger.info(f"Crawled {len(results)} documents from GitHub repository {owner}/{repo_name}")
        return results
        
    except RateLimitExceededException as e:
        # Let existing retry logic handle rate limiting
        logger.error(f"GitHub rate limit exceeded: {e}")
        raise HTTPStatusError("GitHub API rate limit exceeded", 429)
    except GithubException as e:
        logger.error(f"GitHub API error: {e}")
        if e.status == 401:
            raise HTTPStatusError("GitHub authentication failed", 401)
        raise
    except Exception as e:
        logger.error(f"Unexpected error crawling GitHub repository: {e}")
        return []
```

#### 5. Integration in smart_crawl_url
In `smart_crawl_url` function, add after line 1636 (after sitemap check):
```python
        elif is_github_repo(url):
            # For GitHub repositories, use PyGithub API
            github_token = os.getenv("GITHUB_TOKEN")
            if not github_token:
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": "GitHub token not configured. Please set GITHUB_TOKEN environment variable."
                }, indent=2)
            
            try:
                crawl_results = await crawl_github_repository(url, github_token)
                crawl_type = "github_repository"
            except HTTPStatusError as e:
                # Let error recovery handle retryable errors
                if e.status_code in [429, 403]:
                    error_recovery.schedule_retry(url, "github_rate_limit", retry_after=300)
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": str(e),
                    "retry_scheduled": e.status_code in [429, 403]
                }, indent=2)
```

### Error Handling Integration
The implementation leverages existing error handling:
- `HTTPStatusError` for API errors that map to HTTP status codes
- Existing retry logic in `robust_crawl_with_retry` 
- Error recovery manager for scheduling retries
- Rate limit detection and exponential backoff

### Testing Implementation

#### Unit Tests (tests/test_github_integration.py)
```python
import pytest
from unittest.mock import Mock, patch, MagicMock
from src.crawl4ai_mcp import is_github_repo, extract_github_info, crawl_github_repository

class TestGitHubURLDetection:
    """Test GitHub URL detection and parsing."""
    
    def test_is_github_repo_valid_urls(self):
        """Test valid GitHub repository URLs."""
        valid_urls = [
            "https://github.com/PyGithub/PyGithub",
            "http://github.com/user/repo",
            "https://github.com/user/repo.git",
            "https://github.com/user/repo/tree/main",
            "https://github.com/user/repo/blob/main/README.md"
        ]
        for url in valid_urls:
            assert is_github_repo(url), f"Should detect {url} as GitHub repo"
    
    def test_is_github_repo_invalid_urls(self):
        """Test invalid URLs that should not be detected as GitHub repos."""
        invalid_urls = [
            "https://github.com",
            "https://github.com/user",
            "https://gitlab.com/user/repo",
            "https://github.com/docs",
            "https://docs.github.com/en/rest"
        ]
        for url in invalid_urls:
            assert not is_github_repo(url), f"Should not detect {url} as GitHub repo"
    
    def test_extract_github_info(self):
        """Test extraction of owner and repo name."""
        test_cases = [
            ("https://github.com/PyGithub/PyGithub", ("PyGithub", "PyGithub")),
            ("https://github.com/user/repo.git", ("user", "repo")),
            ("https://github.com/user/repo/tree/main", ("user", "repo")),
            ("https://gitlab.com/user/repo", (None, None))
        ]
        for url, expected in test_cases:
            assert extract_github_info(url) == expected

class TestGitHubCrawling:
    """Test GitHub repository crawling."""
    
    @pytest.mark.asyncio
    @patch('src.crawl4ai_mcp.Github')
    async def test_crawl_github_repository_success(self, mock_github):
        """Test successful repository crawling."""
        # Mock repository object
        mock_repo = MagicMock()
        mock_repo.full_name = "PyGithub/PyGithub"
        mock_repo.description = "Typed interactions with the GitHub API v3"
        mock_repo.stargazers_count = 6000
        mock_repo.language = "Python"
        mock_repo.license.name = "LGPL-3.0"
        mock_repo.updated_at = "2024-01-15"
        mock_repo.default_branch = "main"
        
        # Mock README
        mock_readme = MagicMock()
        mock_readme.decoded_content = b"# PyGithub\n\nPython library for GitHub API"
        mock_repo.get_readme.return_value = mock_readme
        
        # Mock empty docs directory
        mock_repo.get_contents.side_effect = Exception("Not found")
        
        # Setup GitHub client mock
        mock_github_instance = MagicMock()
        mock_github_instance.get_repo.return_value = mock_repo
        mock_github.return_value = mock_github_instance
        
        # Test crawling
        results = await crawl_github_repository(
            "https://github.com/PyGithub/PyGithub",
            "test_token"
        )
        
        assert len(results) == 1
        assert "PyGithub/PyGithub" in results[0]['markdown']
        assert "Stars: 6000" in results[0]['markdown']
    
    @pytest.mark.asyncio  
    @patch('src.crawl4ai_mcp.Github')
    async def test_crawl_github_repository_auth_error(self, mock_github):
        """Test handling of authentication errors."""
        from github.GithubException import GithubException
        
        mock_github_instance = MagicMock()
        mock_github_instance.get_repo.side_effect = GithubException(401, "Bad credentials")
        mock_github.return_value = mock_github_instance
        
        from crawl4ai.async_crawler_strategy import HTTPStatusError
        
        with pytest.raises(HTTPStatusError) as exc_info:
            await crawl_github_repository(
                "https://github.com/PyGithub/PyGithub",
                "invalid_token"
            )
        
        assert exc_info.value.status_code == 401
```

## Validation Gates

Execute these commands to validate the implementation:

```bash
# 1. Syntax and Style Check
ruff check --fix src/ tests/

# 2. Type Checking  
mypy src/

# 3. Unit Tests
pytest tests/test_github_integration.py -v

# 4. Integration Tests
pytest tests/ -v -k "github"

# 5. Manual Testing
# Start the MCP server
uv run src/crawl4ai_mcp.py

# In another terminal, test with curl or MCP client
# Test URL detection and crawling
```

## Implementation Tasks

Complete these tasks in order:

1. **Update Dependencies** - Add `PyGithub>=2.0.0` to `pyproject.toml`
2. **Environment Configuration** - Add `GITHUB_TOKEN` and `GITHUB_API_URL` to `.env.example`
3. **URL Detection Functions** - Add `is_github_repo()` and `extract_github_info()` to `crawl4ai_mcp.py`
4. **GitHub Crawling Function** - Implement `crawl_github_repository()` with error handling
5. **Smart Crawl Integration** - Add GitHub detection to `smart_crawl_url()`
6. **Unit Tests** - Create `tests/test_github_integration.py` with comprehensive tests
7. **Integration Tests** - Test with real GitHub repositories
8. **Documentation** - Update README with GitHub crawling examples
9. **Error Recovery** - Verify integration with existing retry mechanisms
10. **Manual Testing** - Test with various GitHub URLs through MCP server

## Success Criteria

- GitHub URLs are correctly detected and parsed
- Repository content (README, docs) is successfully crawled
- Authentication errors provide clear user feedback
- Rate limiting is handled gracefully with retries
- Content is properly chunked and stored in Supabase
- All tests pass and code follows project conventions

## Quality Score

**Confidence Level: 9/10**

This PRP provides comprehensive context including:
- Exact line numbers and integration points in existing code
- Complete PyGithub usage examples and documentation links
- Detailed error handling with existing system integration
- Full testing implementation with mocks
- Clear task ordering for implementation

The only uncertainty is around potential edge cases in GitHub's API behavior, but the robust error handling should cover most scenarios.