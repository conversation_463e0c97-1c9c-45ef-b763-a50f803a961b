-- PGMQ Helper Functions for Job Management
-- These functions wrap PGMQ operations for easier use from the job management system

-- Function to send a message to the queue
CREATE OR REPLACE FUNCTION pgmq_send(
    queue_name TEXT,
    msg JSONB,
    delay INTEGER DEFAULT 0
) R<PERSON><PERSON>NS BIGINT AS $$
BEGIN
    RETURN pgmq.send(queue_name, msg, delay);
END;
$$ LANGUAGE plpgsql;

-- Function to read messages from the queue
CREATE OR REPLACE FUNCTION pgmq_read(
    queue_name TEXT,
    vt INTEGER DEFAULT 30,
    qty INTEGER DEFAULT 1
) RETURNS TABLE (
    msg_id BIGINT,
    read_ct INTEGER,
    enqueued_at TIMESTAMP WITH TIME ZONE,
    message JSONB
) AS $$
BEGIN
    RETURN QUERY SELECT * FROM pgmq.read(queue_name, vt, qty);
END;
$$ LANGUAGE plpgsql;

-- Function to delete a message from the queue
CREATE OR REPLACE FUNCTION pgmq_delete(
    queue_name TEXT,
    msg_id BIGINT
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN pgmq.delete(queue_name, msg_id);
END;
$$ LANGUAGE plpgsql;

-- Function to archive a message
CREATE OR REPLACE FUNCTION pgmq_archive(
    queue_name TEXT,
    msg_id BIGINT
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN pgmq.archive(queue_name, msg_id);
END;
$$ LANGUAGE plpgsql;

-- Function to pop a message (read and delete in one operation)
CREATE OR REPLACE FUNCTION pgmq_pop(
    queue_name TEXT
) RETURNS TABLE (
    msg_id BIGINT,
    read_ct INTEGER,
    enqueued_at TIMESTAMP WITH TIME ZONE,
    message JSONB
) AS $$
BEGIN
    RETURN QUERY SELECT * FROM pgmq.pop(queue_name);
END;
$$ LANGUAGE plpgsql;

-- Function to get queue metrics
CREATE OR REPLACE FUNCTION pgmq_metrics(
    queue_name TEXT
) RETURNS TABLE (
    queue_name TEXT,
    queue_length BIGINT,
    newest_msg_age_sec INTEGER,
    oldest_msg_age_sec INTEGER,
    total_messages BIGINT
) AS $$
BEGIN
    RETURN QUERY SELECT * FROM pgmq.metrics(queue_name);
END;
$$ LANGUAGE plpgsql;

-- Function to purge the queue
CREATE OR REPLACE FUNCTION pgmq_purge(
    queue_name TEXT
) RETURNS BIGINT AS $$
BEGIN
    RETURN pgmq.purge(queue_name);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions to public (or restrict as needed)
GRANT EXECUTE ON FUNCTION pgmq_send(TEXT, JSONB, INTEGER) TO public;
GRANT EXECUTE ON FUNCTION pgmq_read(TEXT, INTEGER, INTEGER) TO public;
GRANT EXECUTE ON FUNCTION pgmq_delete(TEXT, BIGINT) TO public;
GRANT EXECUTE ON FUNCTION pgmq_archive(TEXT, BIGINT) TO public;
GRANT EXECUTE ON FUNCTION pgmq_pop(TEXT) TO public;
GRANT EXECUTE ON FUNCTION pgmq_metrics(TEXT) TO public;
GRANT EXECUTE ON FUNCTION pgmq_purge(TEXT) TO public;