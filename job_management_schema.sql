-- Job Management Schema for MCP Crawl4AI RAG Server
-- This schema implements PGMQ-based job processing to eliminate MCP timeout issues

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgmq";

-- Initialize PGMQ for crawl jobs
SELECT pgmq.create('crawl_jobs_queue', 10, 5);  -- queue name, max messages, visibility timeout minutes

-- Job status enum for type safety
CREATE TYPE job_status AS ENUM (
    'queued',       -- Job created and queued for processing
    'running',      -- Job is currently being processed
    'completed',    -- Job completed successfully
    'failed',       -- Job failed with error
    'cancelled',    -- Job was cancelled by user
    'timeout'       -- Job exceeded timeout limits
);

-- Job type enum for different crawl operations
CREATE TYPE job_type AS ENUM (
    'single_page',          -- Single page crawl
    'smart_crawl',          -- Multi-page intelligent crawl
    'sitemap_crawl',        -- Sitemap-based crawl
    'text_file_crawl',      -- Text file crawl
    'repository_parse'      -- GitHub repository parsing
);

-- Main crawl jobs table
CREATE TABLE crawl_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_type job_type NOT NULL,
    status job_status NOT NULL DEFAULT 'queued',
    
    -- Input parameters (stored as JSONB for flexibility)
    parameters JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Progress tracking
    progress_percent INTEGER DEFAULT 0 CHECK (progress_percent >= 0 AND progress_percent <= 100),
    current_operation TEXT,
    total_operations INTEGER DEFAULT 0,
    completed_operations INTEGER DEFAULT 0,
    
    -- Timing information
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Result information
    result_summary JSONB DEFAULT '{}'::jsonb,
    error_message TEXT,
    
    -- Resource tracking
    pages_crawled INTEGER DEFAULT 0,
    pages_processed INTEGER DEFAULT 0,
    chunks_created INTEGER DEFAULT 0,
    
    -- PGMQ integration
    pgmq_message_id BIGINT,  -- Links to PGMQ message
    
    -- Metadata
    created_by TEXT DEFAULT 'system',
    priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10)
);

-- Job progress tracking table for detailed progress updates
CREATE TABLE job_progress (
    id BIGSERIAL PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES crawl_jobs(id) ON DELETE CASCADE,
    
    -- Progress details
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    operation_name TEXT NOT NULL,
    progress_percent INTEGER NOT NULL CHECK (progress_percent >= 0 AND progress_percent <= 100),
    message TEXT,
    
    -- Performance metrics
    pages_processed INTEGER DEFAULT 0,
    processing_time_ms INTEGER DEFAULT 0,
    
    -- Additional context
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Job results table for storing detailed results
CREATE TABLE job_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES crawl_jobs(id) ON DELETE CASCADE,
    
    -- Result data
    result_type TEXT NOT NULL,  -- 'crawled_pages', 'code_examples', 'sources', etc.
    result_data JSONB NOT NULL,
    
    -- Timing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Size tracking
    data_size_bytes INTEGER DEFAULT 0
);

-- Indexes for performance
CREATE INDEX idx_crawl_jobs_status ON crawl_jobs (status);
CREATE INDEX idx_crawl_jobs_type ON crawl_jobs (job_type);
CREATE INDEX idx_crawl_jobs_created_at ON crawl_jobs (created_at);
CREATE INDEX idx_crawl_jobs_priority_status ON crawl_jobs (priority DESC, status, created_at);
CREATE INDEX idx_crawl_jobs_pgmq_message ON crawl_jobs (pgmq_message_id) WHERE pgmq_message_id IS NOT NULL;

CREATE INDEX idx_job_progress_job_id ON job_progress (job_id);
CREATE INDEX idx_job_progress_timestamp ON job_progress (timestamp);

CREATE INDEX idx_job_results_job_id ON job_results (job_id);
CREATE INDEX idx_job_results_type ON job_results (result_type);

-- Composite indexes for common queries
CREATE INDEX idx_crawl_jobs_status_priority ON crawl_jobs (status, priority DESC, created_at);

-- Function to update job progress
CREATE OR REPLACE FUNCTION update_job_progress(
    p_job_id UUID,
    p_operation_name TEXT,
    p_progress_percent INTEGER,
    p_message TEXT DEFAULT NULL,
    p_pages_processed INTEGER DEFAULT 0,
    p_processing_time_ms INTEGER DEFAULT 0,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS VOID AS $$
BEGIN
    -- Insert progress record
    INSERT INTO job_progress (
        job_id, operation_name, progress_percent, message, 
        pages_processed, processing_time_ms, metadata
    ) VALUES (
        p_job_id, p_operation_name, p_progress_percent, p_message,
        p_pages_processed, p_processing_time_ms, p_metadata
    );
    
    -- Update main job record
    UPDATE crawl_jobs 
    SET 
        progress_percent = p_progress_percent,
        current_operation = p_operation_name,
        completed_operations = COALESCE(completed_operations, 0) + 1,
        pages_processed = COALESCE(pages_processed, 0) + p_pages_processed
    WHERE id = p_job_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get job status with latest progress
CREATE OR REPLACE FUNCTION get_job_status(p_job_id UUID)
RETURNS TABLE (
    job_id UUID,
    job_type job_type,
    status job_status,
    progress_percent INTEGER,
    current_operation TEXT,
    total_operations INTEGER,
    completed_operations INTEGER,
    pages_crawled INTEGER,
    pages_processed INTEGER,
    chunks_created INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    result_summary JSONB,
    latest_progress_message TEXT,
    latest_progress_timestamp TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        j.id,
        j.job_type,
        j.status,
        j.progress_percent,
        j.current_operation,
        j.total_operations,
        j.completed_operations,
        j.pages_crawled,
        j.pages_processed,
        j.chunks_created,
        j.created_at,
        j.started_at,
        j.completed_at,
        j.error_message,
        j.result_summary,
        p.message,
        p.timestamp
    FROM crawl_jobs j
    LEFT JOIN LATERAL (
        SELECT message, timestamp
        FROM job_progress
        WHERE job_id = j.id
        ORDER BY timestamp DESC
        LIMIT 1
    ) p ON true
    WHERE j.id = p_job_id;
END;
$$ LANGUAGE plpgsql;

-- Function to mark job as completed
CREATE OR REPLACE FUNCTION complete_job(
    p_job_id UUID,
    p_result_summary JSONB DEFAULT '{}'::jsonb,
    p_pages_crawled INTEGER DEFAULT 0,
    p_chunks_created INTEGER DEFAULT 0
) RETURNS VOID AS $$
BEGIN
    UPDATE crawl_jobs 
    SET 
        status = 'completed',
        completed_at = NOW(),
        progress_percent = 100,
        result_summary = p_result_summary,
        pages_crawled = COALESCE(pages_crawled, 0) + p_pages_crawled,
        chunks_created = COALESCE(chunks_created, 0) + p_chunks_created,
        current_operation = 'Completed'
    WHERE id = p_job_id;
    
    -- Add final progress entry
    INSERT INTO job_progress (job_id, operation_name, progress_percent, message)
    VALUES (p_job_id, 'Completed', 100, 'Job completed successfully');
END;
$$ LANGUAGE plpgsql;

-- Function to mark job as failed
CREATE OR REPLACE FUNCTION fail_job(
    p_job_id UUID,
    p_error_message TEXT
) RETURNS VOID AS $$
BEGIN
    UPDATE crawl_jobs 
    SET 
        status = 'failed',
        completed_at = NOW(),
        error_message = p_error_message,
        current_operation = 'Failed'
    WHERE id = p_job_id;
    
    -- Add failure progress entry
    INSERT INTO job_progress (job_id, operation_name, progress_percent, message)
    VALUES (p_job_id, 'Failed', COALESCE((SELECT progress_percent FROM crawl_jobs WHERE id = p_job_id), 0), p_error_message);
END;
$$ LANGUAGE plpgsql;

-- Function to cancel job
CREATE OR REPLACE FUNCTION cancel_job(p_job_id UUID) RETURNS BOOLEAN AS $$
DECLARE
    current_status job_status;
BEGIN
    -- Get current status
    SELECT status INTO current_status FROM crawl_jobs WHERE id = p_job_id;
    
    -- Only allow cancellation of queued or running jobs
    IF current_status IN ('queued', 'running') THEN
        UPDATE crawl_jobs 
        SET 
            status = 'cancelled',
            completed_at = NOW(),
            current_operation = 'Cancelled'
        WHERE id = p_job_id;
        
        -- Add cancellation progress entry
        INSERT INTO job_progress (job_id, operation_name, progress_percent, message)
        VALUES (p_job_id, 'Cancelled', COALESCE((SELECT progress_percent FROM crawl_jobs WHERE id = p_job_id), 0), 'Job cancelled by user');
        
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get job queue statistics
CREATE OR REPLACE FUNCTION get_job_queue_stats()
RETURNS TABLE (
    total_jobs BIGINT,
    queued_jobs BIGINT,
    running_jobs BIGINT,
    completed_jobs BIGINT,
    failed_jobs BIGINT,
    cancelled_jobs BIGINT,
    avg_processing_time_minutes NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_jobs,
        COUNT(*) FILTER (WHERE status = 'queued') as queued_jobs,
        COUNT(*) FILTER (WHERE status = 'running') as running_jobs,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_jobs,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_jobs,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_jobs,
        AVG(EXTRACT(EPOCH FROM (completed_at - started_at)) / 60.0) FILTER (WHERE status = 'completed' AND started_at IS NOT NULL AND completed_at IS NOT NULL) as avg_processing_time_minutes
    FROM crawl_jobs;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security
ALTER TABLE crawl_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_results ENABLE ROW LEVEL SECURITY;

-- Policies for public access (can be restricted later)
CREATE POLICY "Allow public read access to crawl_jobs"
    ON crawl_jobs FOR SELECT TO public USING (true);

CREATE POLICY "Allow public read access to job_progress"
    ON job_progress FOR SELECT TO public USING (true);

CREATE POLICY "Allow public read access to job_results"
    ON job_results FOR SELECT TO public USING (true);

-- Allow inserts and updates for job management
CREATE POLICY "Allow public insert to crawl_jobs"
    ON crawl_jobs FOR INSERT TO public WITH CHECK (true);

CREATE POLICY "Allow public update to crawl_jobs"
    ON crawl_jobs FOR UPDATE TO public USING (true);

CREATE POLICY "Allow public insert to job_progress"
    ON job_progress FOR INSERT TO public WITH CHECK (true);

CREATE POLICY "Allow public insert to job_results"
    ON job_results FOR INSERT TO public WITH CHECK (true);

-- Create a view for easy job monitoring
CREATE VIEW job_status_summary AS
SELECT 
    j.id,
    j.job_type,
    j.status,
    j.progress_percent,
    j.current_operation,
    j.created_at,
    j.started_at,
    j.completed_at,
    j.pages_crawled,
    j.chunks_created,
    CASE 
        WHEN j.started_at IS NOT NULL AND j.completed_at IS NULL THEN
            EXTRACT(EPOCH FROM (NOW() - j.started_at)) / 60.0
        WHEN j.completed_at IS NOT NULL AND j.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (j.completed_at - j.started_at)) / 60.0
        ELSE NULL
    END as processing_time_minutes,
    p.message as latest_message,
    p.timestamp as latest_update
FROM crawl_jobs j
LEFT JOIN LATERAL (
    SELECT message, timestamp
    FROM job_progress
    WHERE job_id = j.id
    ORDER BY timestamp DESC
    LIMIT 1
) p ON true;

-- Grant access to the view
GRANT SELECT ON job_status_summary TO public;

-- Comment the tables for documentation
COMMENT ON TABLE crawl_jobs IS 'Main job tracking table for crawl operations';
COMMENT ON TABLE job_progress IS 'Detailed progress tracking for each job';
COMMENT ON TABLE job_results IS 'Structured results storage for completed jobs';
COMMENT ON COLUMN crawl_jobs.parameters IS 'JSONB field storing job-specific parameters like URL, max_depth, chunk_size, etc.';
COMMENT ON COLUMN crawl_jobs.result_summary IS 'JSONB field storing high-level job results like pages_crawled, processing_time, etc.';